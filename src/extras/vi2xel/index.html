<!--
    May 2022
    @nimadez

    A simple touch prototype
    Made with Three.js
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="description" content="A simple touch prototype made with Three.js">
    <meta name="author" content="@nimadez">
    <title>V I ² X E L</title>
    <style>
        @font-face {
            font-family: 'DroidSans'; src: url(data:font/truetype;charset=utf-8;base64,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)
                format('truetype');
        }
        * { margin: 0; padding: 0; overflow: hidden; user-select: none; }
        body { background: #3a404d; font-family: 'DroidSans'; cursor: default; }
        canvas { z-index: 10; position: absolute; width: 100%; height: 100%; background: radial-gradient(circle, rgb(81, 90, 109) 0%, rgb(49, 53, 68) 100%); }
        #info { z-index: 100; opacity: 0.8; font-size: 11px; color: white; position: absolute; left: 5px; bottom: 5px; pointer-events: none; }
  </style>
</head>
<body>
    <canvas></canvas>
    <div id="info">
        <span style="color:#ffffff90"><i><b>V I ² X E L</b><br>A simple touch prototype!<br>- Touch: Select <br>- Short Touch: Add<br>- Long Touch: Remove<br>- Double Touch: Change Color<br>Made with Three.js</i></span>
    </div>

<script type="importmap">{ "imports": { "three": "https://cdn.jsdelivr.net/npm/three@0.171.0/build/three.module.js" } }</script>
<script type="module">
    import * as THREE from 'three';
    import { OrbitControls } from 'https://cdn.jsdelivr.net/npm/three@0.171.0/examples/jsm/controls/OrbitControls.js';

    const canvas = document.getElementsByTagName('canvas')[0];
    const renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true, alpha: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setClearColor(0x000000, 0);
    renderer.autoClearColor = false;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1;
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFShadowMap;

    const scene = new THREE.Scene();
    
    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 500);
    camera.position.set(5, 5, 5);
    camera.lookAt(0, 0, 0);

    const controls = new OrbitControls(camera, renderer.domElement);
    controls.minDistance = 0.01;
    controls.maxDistance = 1000;
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.enablePan = true;
    controls.enableRotate = true;
    controls.enableZoom = true;

    const ambient = new THREE.AmbientLight(0xAAAAAA);
    const hemisphere = new THREE.HemisphereLight(0xffffbb, 0x080820, 0.5);
    const directional = new THREE.DirectionalLight(0xffffbb, 1);
    directional.castShadow = true;
    directional.position.set(-5, 10, 5);
    directional.target.position.set(0, 0, 0);
    directional.shadow.mapSize.width = 512;
    directional.shadow.mapSize.height = 512;
    directional.shadow.camera.near = 0.1;
    directional.shadow.camera.far = 500;
    directional.shadow.bias = -0.00001;
    scene.add(ambient, hemisphere, directional);

    const groundGeo = new THREE.PlaneGeometry(1000, 1000);
    groundGeo.rotateX(-Math.PI / 2);
    const shadowMat = new THREE.ShadowMaterial({ color: 0x000000, opacity: 0.1 });
    const shadowGround = new THREE.Mesh(groundGeo, shadowMat);
    shadowGround.receiveShadow = true;
    shadowGround.position.y = -0.5;
    scene.add(shadowGround);

    const colors = [ 0xFFFFFF, 0xFF8080, 0x80FF80, 0x8080FF, 0xFF90AA, 0xFFAA80, 0x689BCA, 0x68BFCA, 0x68CA81, 0xC4D449, 0xCC6666 ];
    const matStd = new THREE.MeshStandardMaterial({ color: randomSelect(colors), roughness: 0.5, metalness: 0.2, side: THREE.FrontSide, precision: 'mediump' }); //precision: support low-end mobile
    const matLine = new THREE.LineBasicMaterial({ color: 0x00ffff, opacity: 0.5, transparent: true });
    const matPoint = new THREE.PointsMaterial({ size: 0.06, color: 0x00ffff });
    const matGhost = new THREE.MeshStandardMaterial({ color: 0x000000, emissive: 0x00ff00, emissiveIntensity: 1, side: THREE.FrontSide, precision: 'mediump' });
    matStd.color.convertSRGBToLinear();
    matStd.emissive = new THREE.Color(0.05, 0.05, 0.05);
    matStd.emissiveMap = createVoxelTexture();
    matLine.depthFunc = 1;
    matGhost.depthFunc = 1;

    const group = new THREE.Object3D();
    const helper = new THREE.Object3D();
    const cubeGeo = new THREE.BoxGeometry(1,1,1);
    const cubeMesh = new THREE.Mesh(cubeGeo, matStd);
    const cubeLine = new THREE.LineSegments(new THREE.EdgesGeometry(cubeGeo), matLine);
    const cubePoint = new THREE.Points(cubeGeo, matPoint);
    const ghost = new THREE.Mesh(new THREE.BoxGeometry(0.01,0.01,0.01), matGhost);

    helper.add(cubeLine, cubePoint, ghost);
    group.add(cubeMesh, helper);
    scene.add(group);

    const mouse = { x: 0, y: 0, z: 0, isDown: false };
    const raycaster = new THREE.Raycaster();
    let isShowUI = false;
    let intersects = [];
    let cubeArray = [];
    let lastTime = 0;

    init();
    animate();

    function init() {
        helper.visible = false;
        cubeMesh.castShadow = true;
        cubeMesh.receiveShadow = false;
        cubeArray.push(cubeMesh);
    }

    function animate() {
        if (lastTime > 0) {
            ghost.scale.lerp(new THREE.Vector3(100,100,100), 0.01);
            matGhost.emissive.lerp(new THREE.Color(1,0,0), 0.02);
        }

        controls.update();
        requestAnimationFrame(animate);
        renderer.render(scene, camera);
    }

    function onToolDown(ev) {
        raycaster.setFromCamera(mouse, camera);
        intersects = raycaster.intersectObjects(cubeArray);
        if (intersects.length > 0) {
            lockCamera(true);
            setHelpers();
        }
    }

    function onToolMove(ev) {
        intersects = raycaster.intersectObjects(cubeArray);
        if (intersects.length > 0) {
            //
        }
    }

    function onToolUp(ev) {
        if (intersects.length > 0) {
            if (ev.timeStamp - lastTime > 2000) { // relative to ghost lerp time
                console.log('noone!');
            } else if (ev.timeStamp - lastTime > 1000) {
                removeCube();
            } else if (ev.timeStamp - lastTime > 200) {
                addCube();
            } else {
                console.log('one-tap, select');
            }
        }
        lockCamera(false);
        disposeHelpers();
    }

    function addCube() {
        const face = intersects[0].face;
        const obj = intersects[0].object;

        const clone = obj.clone();
        clone.position.add(face.normal);
        group.add(clone);
        cubeArray.push(clone);

        // mirror
        /* const mirrorPos = new THREE.Vector3(-clone.position.x, clone.position.y, clone.position.z);
        if (!clone.position.equals(mirrorPos)) { // no duplicate on center
            const mirror = clone.clone();
            mirror.position.copy(mirrorPos);
            group.add(mirror);
            cubeArray.push(mirror);
        } */
    }

    function removeCube() {
        const obj = intersects[0].object;
        const index = cubeArray.indexOf(obj);
        
        if (index > -1 && index !== 0) {
            group.remove(obj);
            cubeArray.splice(index, 1);
        }

        // mirror
        /* for (let i = 1; i < cubeArray.length; i++) {
            if (cubeArray[i].position.equals(new THREE.Vector3(-obj.position.x, obj.position.y, obj.position.z))) {
                group.remove(cubeArray[i]);
                cubeArray.splice(i, 1);
            }
        } */
    }

    function setHelpers() {
        helper.visible = true;
        helper.position.copy(intersects[0].object.position);
    }

    function disposeHelpers() {
        helper.visible = false;
        ghost.scale.set(0.01, 0.01, 0.01);
        matGhost.emissive.r = 0;
        matGhost.emissive.g = 1;
    }

    function lockCamera(isLock) {
        controls.enabled = !isLock;
    }

    canvas.addEventListener("pointerdown", function(ev) {
        mouse.x = (ev.clientX / window.innerWidth) * 2 - 1;
	    mouse.y = -(ev.clientY / window.innerHeight) * 2 + 1;
        mouse.isDown = true;
        lastTime = ev.timeStamp;
        onToolDown(ev);
    }, false);

    canvas.addEventListener("pointermove", function(ev) {
        mouse.x = (ev.clientX / window.innerWidth) * 2 - 1;
	    mouse.y = -(ev.clientY / window.innerHeight) * 2 + 1;
        onToolMove(ev);
    }, false);

    canvas.addEventListener("pointerup", function(ev) {
        mouse.isDown = false;
        onToolUp(ev);
        lastTime = 0;
    }, false);

    canvas.addEventListener("dblclick", function(ev) {
        matStd.color.setHex(randomSelect(colors)).convertSRGBToLinear();
    }, false);

    window.addEventListener('resize', function (ev) {
        renderer.setSize(window.innerWidth, window.innerHeight);
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
    }, false);

    function createVoxelTexture(w=256, h=256) {
        const c = document.createElement('canvas');
        c.width = w;
        c.height = h;
        const ctx = c.getContext('2d');
        ctx.strokeStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.moveTo(0, h/2);
        ctx.lineTo(w, h/2);
        ctx.moveTo(w/2, 0);
        ctx.lineTo(w/2, h);
        ctx.lineWidth = 8;
        ctx.stroke();
        ctx.strokeRect(0, 0, w, h);
        return new THREE.CanvasTexture(c);
    }

    function randomSelect(array) {
        return array[ ~~((Math.random() * array.length)) ];
    }
</script>
</body>
</html>
