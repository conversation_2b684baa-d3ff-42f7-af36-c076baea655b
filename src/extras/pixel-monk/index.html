<!--
    Jan 2022
    1.5.0 alpha 2022
    @nimadez

    Pixel Monk
    Vector-based pixel editor application

    ## General
    - File I/O
    - Pixelate image *(JPG/PNG)*
    - Export PNG
    - Copy/Paste Base64
    - 4 local storages
    - Undo and redo
    - Support file drag and drop
    - Minimum dependency
    - Single HTML file

    ## Drawing
    - 12 Pixel Sizes *(2-24)*
    - Tool: Pencil
    - Tool: Line
    - Tool: Symmetry
    - Tool: Quad Symmetry
    - Tool: Spray *(optional radius)*
    - Tool: Eraser
    - Tool: Rectangle Eraser
    - Effector: Cyclone *(optional velocity)*
    - Effector: Gravity *(optional velocity)*
    - Xform: Transform *(pivot and center)*
    - Magnifier

    ## Painting
    - Tool: Paint
    - Tool: Bucket
    - Tool: Eyedropper
    - Color Picker *(html color picker)*
    - Dynamic Color Palette *(collect unique colors)*
    - Filters: Grayscale, Sepia, Invert

    ## Tips
    - Pixel is a vector object
    - Choose pixel size wisely *(if accuracy is important)*
    - Hide Dynamic Color Palette while manipulating large amounts of pixels

    ## Supported Browsers
    - Google Chrome
    - Google Chrome for mobile devices
    <br><sub>* *Tablet recommended for best experience*</sub>

    ## Keyboard Shortcuts
    | M         | Toggle Magnifier
    | CTRL+Z    | Undo (previous memory)
    | CTRL+X    | Redo (next memory)

    [ Code Map ]
    01. Initialize
    02. Pixel
    03. Board
    04. Tool
    05. Helper
    06. Memory
    07. Dynamic Palette
    08. Palette
    09. Projects
    10. Storages
    11. Events
    12. Canvases
    13. User Interface
    14. Functions
    15. Utils

    Layer0: grid canvas, static grid background
    Layer1: board canvas, main drawing board
    Layer2: helper canvas, draw-and-clear helpers
    layer3: magnifier canvas, dynamic magnifier
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="description" content="Vector-based pixel editor application">
    <meta name="author" content="@nimadez">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:site" content="@nimadez">
    <meta name="twitter:creator" content="@nimadez">
    <meta name="twitter:title" content="Pixel Monk">
    <meta name="twitter:description" content="Vector-based pixel editor application">
    <title>Pixel Monk</title>
    <link rel="icon" href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAupJREFUWEftl99LFFEUx8+982NVnBV/bP4B9SJasLht0g9YAlkR8i0VBl+tiHoKosAsA+ktiyJQgiiGXhJCKCofUvDHhrpLu6uSv7Z80f3hrrtT2s7OzI3ZB9l2cBwfREEPzMtwv2c+98y555yLYJ8Nad+fbG9nyMaGCxFi2wVPwCEIgaye508CgPaYMoJQFBUVDTl6ezNZAA/PW4GQQQZjpykPALCUSvVdHhi4AQDpsdbWLgtNd5jViplM8OXsbNMbvz+0BZCW5ZFihjG9i/eh0EKP318niuLa1+bmR1aWvW0WYDqRgOczM+5vodAXHYBKCCQzGZAVRefPyrJgoajs++0A0ooCKUnSaWmKghKGAYwQGAL8kWXomJj45Y/Hf+R76XY67U6bLZsn2wEMr6zID73ecQDYzNXXVlSU3Hc4ThdSFN4R4Ekg0Ns/PX0lH2CopeUDxzCNRgCeSGS90+uti8Vi/21gsq3tTEZRhliMC44ADnYENmQZhPl54Wp1te5YJSXpdTFNXzTKgalYLDW1tnbpWlXVYm4OKYpil1T1HUtRFsMImD3Le1YHDieAtuvS0tJzADBiNgIA8FmWZV4rxRzHldM0LQCAexf684lEYjRbivfTDgaA1o5ZhD4SAO03mDJCSKdDELq0xZM8fw8h9MCUEAAQwKhESGOdIKQOXjs2u4s9qwNaJez2+WAhldKx3KypgbOVlYbzwFg4DE+DQZ32hNUKd+12KKLpneeBnkDg1eDy8q18L/0NDW/LLZb6Hdpx8o7HU08IWcrVv3C5ao9z3ACLsXEp1gaSo3ngcEdAm4rTqvq7EONEfhKqADaMUIFREmZUVcUAYQohOVdPEGKBkGMIIXQ0D+gioN0NP83NXfdFo4/NVsLw5ubY92i0SWvHfW73hfFI5FlSkk6Z0a9LEiyKouvn6urwVjfkOK6CoqgaMw60NRjjv/F43KfdDQHAUlZWZldVNZsfZkxRlKAoirF9b8f/ACMa/U5ap5eDAAAAAElFTkSuQmCC">
    <style>
        @font-face {
            font-family: 'DroidSans'; src: url(data:font/truetype;charset=utf-8;base64,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)
                format('truetype');
        }
        @font-face {
            font-family: 'Material Icons'; font-style: normal; font-weight: 400;
            src: url(data:font/woff2;charset=utf-8;base64,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)
                format('woff2');
        }
        /* silver, indianred, cadetblue, aquamarine, aqua */
        * { margin: 0; padding: 0; outline: none; }
        html, body { overscroll-behavior-y: contain; user-select: none; overflow: hidden; touch-action: pan-x; -webkit-tap-highlight-color: transparent; }
        body { font-family: 'DroidSans'; cursor: crosshair; position: fixed; width: 100%; height: 100%; }
        a, a:visited { color: cadetblue; text-decoration: none; }
        a:hover { color: aquamarine; }
        button { width: 20px; color: silver; background: none; border: none; cursor: pointer; }
        button:hover, button.selected i { color: indianred; }
        button:disabled { opacity: 0.3; }
        input[type=color] { height: 16px; border: none; vertical-align: middle; border-radius: 10px; }
        input[type=color]::-webkit-color-swatch-wrapper { padding: 0; border-radius: 3px; }
        input[type=color]::-webkit-color-swatch { border: solid 1px #343c49; border-radius: 2px; }
        input[type=color]:hover { cursor: pointer; }
        input[type=range] { accent-color: cadetblue; width: 100%; height: 2px; vertical-align: middle; }
        input[type=range]::-webkit-slider-thumb { transform: scale(0.8); }
        input[type=range]::-webkit-slider-thumb:hover { cursor: pointer; }
        input[type=number] { width: 50px; background: #282c3490; color: silver; border: solid 1px #343c49; padding: 2px; font-size: 10px; border-radius: 2px; text-align: left; }
        input[type=number]:hover { color: indianred; border: solid 1px #43495a; }
        input[type=number]:disabled { opacity: 0.3; }
        input[type=text] { width: 90%; background: #282c3490; color: silver; border: solid 1px #343c49; padding: 3px; font-size: 11px; border-radius: 2px; text-align: left; }
        input[type=checkbox] { accent-color: cadetblue; transform: scale(0.8); vertical-align: text-bottom; cursor: pointer; }
        select { width: 100%; background: #282c3490; color: silver; font-size: 11px; padding: 3px; border: solid 1px #1c1d2090; border-radius: 2px; outline: none; vertical-align: middle; }
        select:hover { color: indianred; cursor: pointer; }
        option { color: silver; background: #252931; }
        label { color: #8a8f9b; font-size: 11px; text-align: left; }
        #canvas_grid { z-index: 0; background: #2c313a; position: absolute; width: 100%; height: 100%; }
        #canvas_board { z-index: 1; position: absolute; width: 100%; height: 100%; }
        #canvas_helper { z-index: 2; position: absolute; width: 100%; height: 100%; pointer-events: none; }
        #canvas_magnifier { z-index: 9; display: none; background: #2F333C; border: solid 1px aqua; width: 100px; height: 100px; position: fixed; bottom: 1px; right: 1px; pointer-events: none; }
        .menu { z-index: 500; background: #252931f1; border: solid 1px #1c1d2090; width: 100px; position: absolute; position: absolute; left: 0; padding: 3px; transform: translate(37px, -500px); transition: transform 0.2s ease; cursor: default; }
        .menu .category { background: #1d2027; color: #545c68; font-size: 10px; padding: 1px; margin: 5px 2px 2px 2px; border-radius: 3px; text-align: center; }
        .menu .sector { list-style: none; padding-bottom: 2px; padding-left: 2px; }
        .menu button { font-size: 11px; background: #30363f; border: solid 1px #1d222b; padding: 5px 0 5px 0; width: 100%; border-radius: 3px; text-align: center; display: block; }
        .menu button:hover { background: #2d323d; color: indianred; }
        #menustorage .sector img { width: 94px; height: 60px; margin-bottom: -2px; border: dotted 1px #3c3f47; cursor: pointer; }
        #menustorage .sector div { display: inline-flex; }
        #menustorage .sector div button { width: 48px; }
        #toolbar_L { z-index: 500; background: #252931f1; border: solid 1px #1c1d2090; position: absolute; width: 35px; top: 0; left: 0; border-bottom-right-radius: 5px; text-align: center; cursor: default; }
        #toolbar_L button { width: 100%; padding: 5px 0 5px 0; }
        #toolbar_R { z-index: 500; background: none; border: none; position: absolute; width: 35px; top: 2px; right: 0; text-align: center; cursor: default; }
        #toolbar_R button { background: #252931f1; border: solid 1px #1c1d2090; width: 100%; padding: 5px 0 5px 0; margin-bottom: 2px; border-top-left-radius: 5px; border-bottom-left-radius: 5px; }
        #toolbar_R button:hover { color: indianred; }
        #toolbar_C { z-index: 500; position: absolute; width: 260px; height: 20px; left: 50%; margin-left: -130px; top: 3px; text-align: center; }
        #toolbar_C ul { list-style: none; line-height: 1em; }
        #toolbar_C li { display: inline-block; margin: 0 2px 0 2px; }
        #toolbar_C .storage { color: #aaa; background: #252931f1; width: 50px; font-size: 10px; font-weight: bold; padding: 4px; border-radius: 3px; }
        #toolbar_C .storage:hover { color: black; background: silver; }
        #toolbar_B { z-index: 500; position: absolute; width: 100px; left: 50%; bottom: 20px; margin-left: -50px; text-align: center; }
        #toolbar_B input::-webkit-slider-thumb { transform: scale(1.1); }
        #toolbar_B :first-child { color: cadetblue; font-size: 10px; font-weight: bold; }
        #hover { z-index: 500; top: 130px; left: 3px; position: absolute; cursor: default; }
        #hover ul { opacity: 0.9; background: #252931f1; border: solid 1px #1c1d2090; border-radius: 3px; list-style: none; }
        #hover ul .sep { height: 1px; background: #3e4550; margin: 2px 0 2px 0; padding: 0; pointer-events: none; }
        #hover li { padding: 5px; }
        #hover li:nth-child(1) { padding: 0; background: #252931; color: #3f4750; text-align: center; cursor: move; }
        #dynamicpalette { z-index: 500; display: none; background: #78869615; width: 100px; position: absolute; right: 5px; top: 240px; bottom: 120px; overflow-y: scroll; line-height: 0; }
        #dynamicpalette li { border: solid 1px #333; display: inline-block; width: 16px; height: 16px; border-radius: 0; }
        #dynamicpalette li:hover { border: solid 1px indianred; }
        #palette { z-index: 500; width: 85px; position: absolute; bottom: 5px; left: 5px; }
        #palette ul { line-height: 0.6em; }
        #palette li { position: absolute; left: 35px; bottom: 35px; background-color: #222222; border: solid 1px #333333; width: 24px; height: 22px; border-radius: 5px; display: inline-block; cursor: pointer; }
        #palette ul :nth-child(1) { z-index: 90; transform: rotate(270deg)  translateX(32px); }
        #palette ul :nth-child(2) { z-index: 80; transform: rotate(315deg)  translateX(32px); }
        #palette ul :nth-child(3) { z-index: 70; transform: rotate(360deg)  translateX(32px); }
        #palette ul :nth-child(4) { z-index: 60; transform: rotate(45deg)   translateX(32px); }
        #palette ul :nth-child(5) { z-index: 50; transform: rotate(90deg)   translateX(32px); }
        #palette ul :nth-child(6) { z-index: 40; transform: rotate(135deg)  translateX(32px); }
        #palette ul :nth-child(7) { z-index: 30; transform: rotate(180deg)  translateX(32px); }
        #palette ul :nth-child(8) { z-index: 20; transform: rotate(225deg)  translateX(32px); }
        #palette button { z-index: 500; padding: 5px; position: absolute; left: 0; bottom: 90px; }
        #palette button:hover { color: cadetblue; }
        #colorpicker { z-index: 500; position: absolute; bottom: 33px; left: 34px; }
        #colorpicker input[type=color] { width: 38px; height: 38px; border-radius: 50%; }
        #colorpicker input[type=color]::-webkit-color-swatch-wrapper { border-radius: 50%; }
        #colorpicker input[type=color]::-webkit-color-swatch { border: solid 1px white; border-radius: 50%; }
        #input-color-bg { width: 100%; vertical-align: text-bottom; }
        #pixelsize { color: cadetblue; font-size: 20px; font-weight: bold; bottom: 25px; right: 5px; position: absolute; }
        #info { z-index: 8; opacity: 0.8; color: #999; font-size: 10px; letter-spacing: 0.05em; position: absolute; width: fit-content; bottom: 5px; right: 5px; text-align: right; pointer-events: none; }
        #info span { color: silver; font-weight: bold; }
        #intro { z-index: 500; color: indianred; font-size: 10px; letter-spacing: 0.1em; position: absolute; width: 200px; left: 50%; bottom: 100px; margin-left: -100px; text-align: center; transition: opacity 0.5s; pointer-events: none; }
        #intro b { color: silver; }
        #about { z-index: 1000; display: none; font-size: 10px; letter-spacing: 1px; line-height: 2.4em; background: #191c22f1; color: silver; position: absolute; width: 220px; height: 160px; left: 50%; top: 50%; margin-left: -110px; margin-top: -85px; padding-top: 10px; border-radius: 5px; box-shadow: 0 0 5px #1c1d20; text-align: center; cursor: help; }
        #about h1 { color: indianred; font-size: 20px; letter-spacing: 2px; margin: 10px 0 -20px 0; }
        #about h1 .material-icons { color: indianred; font-size: 30px; vertical-align: -6px; }
        #about a { font-size: 11px; font-weight: bold; }
        .spacer { height: 5px; }
        .material-icons { font-family: 'Material Icons'; font-weight: normal; font-style: normal; font-size: 16px; vertical-align: middle; text-align: center; line-height: 1; letter-spacing: normal; text-transform: none; display: inline-block; white-space: nowrap; word-wrap: normal; direction: ltr; pointer-events: none; font-feature-settings: 'liga'; -webkit-font-feature-settings: 'liga'; -webkit-font-smoothing: antialiased; }
        ::-webkit-scrollbar { width: 10px; }
        ::-webkit-scrollbar-track { background: #78869650; }
        ::-webkit-scrollbar-thumb { background: steelblue; }
        ::-webkit-scrollbar-thumb:hover { background: indianred; }
  </style>
</head>
<body>
    <canvas id="canvas_grid"></canvas>
    <canvas id="canvas_board" ondrop="dropHandler(event)" ondragover="dragHandler(event)" ondragleave="dragLeaveHandler(event)"></canvas>
    <canvas id="canvas_helper"></canvas>
    <canvas id="canvas_magnifier"></canvas>
    <div id="toolbar_L">
        <button title="Main Menu" onclick="showMenu()"><i class="material-icons">content_copy</i></button>
        <button title="Preferences" onclick="showPrefs()"><i class="material-icons">settings</i></button>
        <button title="Storage" onclick="showStorage()"><i class="material-icons">inbox</i></button>
        <button title="Filters" onclick="showFilters()"><i class="material-icons">tune</i></button>
    </div>
    <div id="toolbar_R">
        <button title="Clear" onclick="clearBoard()"><i class="material-icons">delete_forever</i></button>
        <div class="spacer"></div>
        <button title="Recenter" onclick="recenter()"><i class="material-icons">center_focus_strong</i></button>
        <button title="Tool: Translate" onclick="tool.selectButton('translate', this)"><i class="material-icons">control_camera</i></button>
        <div class="spacer"></div>
        <button title="Tool: Cyclone Effector" onclick="tool.selectButton('cyclone', this)"><i class="material-icons">cyclone</i></button>
        <button title="Tool: Gravity Effector" onclick="tool.selectButton('gravity', this)"><i class="material-icons">swipe_down_alt</i></button>
        <div class="spacer"></div>
        <button title="Dynamic Palette" onclick="toggleDynamicPalette()"><i class="material-icons">color_lens</i></button>
        <button title="Magnifier" onclick="toggleMagnifier()"><i class="material-icons">search</i></button>
    </div>
    <div id="toolbar_C">
        <ul>
            <li><button title="Quick Save" onclick="store()" class="storage">SAVE</button></li>
            <li><button title="Quick Load" onclick="restore()" class="storage">LOAD</button></li>
            <li></li>
            <li><button title="Undo" onclick="memory.undo()" class="storage">UNDO</button></li>
            <li><button title="Redo" onclick="memory.redo()" class="storage">REDO</button></li>
        </ul>
    </div>
    <div id="toolbar_B">
        <input title="Tool: Pixel Size" id="input-tool-size" type="range" min="0" max="11" value="4" step="1" oninput="document.getElementById('pixelsize').innerHTML = toolSizeList[this.value]">
    </div>
    <div class="menu" id="menumain">
        <div class="category">FILE</div>
        <button onclick="reset()">New</button>
        <button onclick="document.getElementById('openfile_pmk').click()">Load</button>
        <button onclick="saveProject()">Save</button>
        <button onclick="exportPNG()">Export PNG</button>
        <button onclick="copyBase64Image()">Copy Base64</button>
        <div class="category">PIXELATOR</div>
        <ul class="sector">
            <li><input id="input-pixelate-alpha" type="checkbox" checked> <label for="input-pixelate-alpha">Use Alpha</label></li>
            <li><input id="input-pixelate-fit" type="checkbox" checked oninput="document.getElementById('input-pixelate-ratio').disabled = this.checked"> <label for="input-pixelate-fit">Fit Screen</label></li>
            <li><label>Ratio</label> <input id="input-pixelate-ratio" type="number" value="2.0" min="0.1" max="10.0" step="0.1" disabled title="Lower value equals to more details and more time to process" onmousewheel=""></li>
            <li><label>Pixel Size: 10</label> <input id="input-pixelate-size" type="range" min="0" max="11" value="4" step="1" oninput="this.previousElementSibling.innerHTML='Pixel Size: '+toolSizeList[this.value]"></li>
        </ul>
        <button onclick="document.getElementById('openfile_img').click()">Upload Image</button>        
        <button onclick="pasteBase64Image()">Paste Base64</button>
        <div class="category">HELP</div>
        <button onclick="document.getElementById('about').style.display='unset'">About</button>
    </div>
    <div class="menu" id="menuprefs">
        <div class="category">PREFERENCES</div>
        <ul class="sector">
            <li title="Project Name"><label>Project Name</label><input id="input-projectname" type="text" value="null" minlength="3" maxlength="20"></li>
            <li title="Background Color"><label>Background Color</label> <input id="input-color-bg" type="color" value="#2F333C"></li>
            <li><input id="input-fullscreen" type="checkbox" onclick="toggleFullscreen()"> <label for="input-fullscreen" title="Toggle Fullscreen">Fullscreen</label></li>
        </ul>
        <div class="category">PROPERTIES</div>
        <ul class="sector">
            <li><input id="input-tool-preservealpha" type="checkbox" checked> <label for="input-tool-preservealpha" title="Preserve Alpha (Paint & Bucket)">Preserve Alpha</label></li>
            <li><label>Spray Radius</label><input id="input-tool-sprayradius" type="range" min="10" max="100" value="30" step="1"></li>
            <li><label>Cyclone Velocity</label> <input id="input-tool-cyclonevelocity" type="range" value="0.05" min="0.01" max="0.1" step="0.01"></li>
            <li><label>Gravity Velocity</label> <input id="input-tool-gravityvelocity" type="range" value="0.05" min="0.01" max="0.1" step="0.01"></li>
        </ul>
        <div class="category">DEBUG</div>
        <ul class="sector">
            <li><label for="input-debug">Debug Board</label> <input id="input-debug" type="checkbox" oninput="isDebugBoard=!isDebugBoard"></li>
        </ul>
    </div>
    <div class="menu" id="menustorage">
        <div class="category">STORAGE</div>
        <ul class="sector">
            <li class="storage"><img><div><button>Clear</button><button>Save</button></div></li>
            <li class="spacer"></li>
            <li class="storage"><img><div><button>Clear</button><button>Save</button></div></li>
            <li class="spacer"></li>
            <li class="storage"><img><div><button>Clear</button><button>Save</button></div></li>
        </ul>
    </div>
    <div class="menu" id="menufilters">
        <div class="category">FILTERS</div>
        <ul class="sector">
            <li><button onclick="filterGrayscale(board.pixels)">Grayscale</button></li>
            <li><button onclick="filterSepia(board.pixels)">Sepia</button></li>
            <li><button onclick="filterInvert(board.pixels)">Invert</button></li>
        </ul>
    </div>
    <div id="hover">
        <ul>
            <li onpointerdown="dragElement(this)"><i class="material-icons">grid_view</i></li>
            <li class="sep"></li>
            <li><button title="Tool: Pencil" onclick="tool.selectButton('pencil', this)" id="startuptool" class="selected"><i class="material-icons">create</i></button></li>
            <li><button title="Tool: Line" onclick="tool.selectButton('line', this)"><i class="material-icons">linear_scale</i></button></li>
            <li><button title="Tool: Symmetry" onclick="tool.selectButton('symmetry', this)"><i class="material-icons">align_horizontal_center</i></button></li>
            <li><button title="Tool: Quad Symmetry" onclick="tool.selectButton('symmetry-quad', this)"><i class="material-icons">flare</i></button></li>
            <li><button title="Tool: Spray (prop: Spray Size)" onclick="tool.selectButton('spray', this)"><i class="material-icons">bubble_chart</i></button></li>
            <li class="sep"></li>
            <li><button title="Tool: Eraser" onclick="tool.selectButton('eraser', this)"><i class="material-icons">clear</i></button></li>
            <li><button title="Tool: Rectangle Eraser" onclick="tool.selectButton('rect-eraser', this)"><i class="material-icons">cancel_presentation</i></button></li>
            <li class="sep"></li>
            <li><button title="Tool: Paint (prop: Preserve Alpha)" onclick="tool.selectButton('paint', this)"><i class="material-icons">brush</i></button></li>
            <li><button title="Tool: Bucket (prop: Preserve Alpha)" onclick="tool.selectButton('bucket', this)"><i class="material-icons">format_color_fill</i></button></li>
            <li><button title="Tool: Eyedropper" onclick="tool.selectButton('eyedrop', this)"><i class="material-icons">colorize</i></button></li>
        </ul>
    </div>
    <div id="palette">
        <ul id="palettecolors"></ul>
        <button title="Shade-Fill Palette" onclick="palette.shadeFill()"><i class="material-icons">brightness_medium</i></button>
    </div>
    <div id="dynamicpalette"><ul id="dynamiccolors"></ul></div>
    <div id="colorpicker"><input id="input-color" type="color" value="#CD5C5C"></div>
    <input style="display: none" type="file" id="openfile_pmk" accept=".pmk" onclick="this.value = null">
    <input style="display: none" type="file" id="openfile_img" accept=".jpg,.png" onclick="this.value = null">
    <div id="pixelsize">10</div>
    <div id="info"><span></span> Pixels<br><span></span></div>
    <div id="intro">
        <h1>PIXEL MONK</h1>
        <b>1.5.0 ALPHA</b>
    </div>
    <div id="about" onclick="this.style.display='none'">
        <h1><i class="material-icons">grid_view</i> PIXEL MONK</h1>
        <br>VECTOR-BASED PIXEL EDITOR
        <br>1.5.0 ALPHA 2022
        <br><a href="https://github.com/nimadez/">GITHUB REPOSITORY</a>
        <br><a href="https://nimadez.github.io/">©2022 @nimadez</a></div>
</body>
<script>
    const inputProjectName = document.getElementById('input-projectname');
    const inputColor = document.getElementById('input-color');
    const inputBgColor = document.getElementById('input-color-bg');
    const inputToolSize = document.getElementById('input-tool-size');
    const inputToolQuadSymm = document.getElementById('input-tool-quadsymm');
    const inputToolPreserveAlpha = document.getElementById('input-tool-preservealpha');
    const inputToolSprayRadius = document.getElementById('input-tool-sprayradius');
    const inputToolCycloneVelocity = document.getElementById('input-tool-cyclonevelocity');
    const inputToolGravityVelocity = document.getElementById('input-tool-gravityvelocity');
    const storages = document.querySelectorAll('.sector .storage');
    const info = document.getElementById('info');

    const cg = document.getElementById('canvas_grid');
    const ctxg = cg.getContext('2d', { willReadFrequently: true });
    cg.width = window.innerWidth;
    cg.height = window.innerHeight;

    const cb = document.getElementById('canvas_board');
    const ctx = cb.getContext('2d', { willReadFrequently: true });
    cb.width = window.innerWidth;
    cb.height = window.innerHeight;

    const ch = document.getElementById('canvas_helper');
    const ctxh = ch.getContext('2d', { willReadFrequently: true });
    ch.width = window.innerWidth;
    ch.height = window.innerHeight;

    const cm = document.getElementById('canvas_magnifier');
    const ctxm = cm.getContext('2d', { willReadFrequently: true });
    cm.width = 100;
    cm.height = 100;


    // -------------------------------------------------------
    // Initialize


    const storageHolder = 'data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 90%22><text y=%22.9em%22 font-size=%2280%22 style="filter: grayscale(); opacity: 0.1;">❌</text></svg>';
    const PI2 = Math.PI * 2;
    const mouse = { x: 0, y: 0, lastX: 0, lastY: 0, pivotX: 0, pivotY: 0, isDown: false };
    const toolSizeList = [2,4,6,8,10,12,14,16,18,20,22,24]; // using the slider-input as the indexer for the inputPixelSize
    
    let canvasOffset = { x: 0, y: 0 };
    let currentColor = inputColor.value.toUpperCase();
    let isDebugBoard = false;
    let isShowMagnifier = false;
    let isShowDynamicPalette = false;

    let board = new Board();
    let tool = new Tool();
    let helper = new Helper();
    let memory = new Memory();
    let dynamicPalette = new DynamicPalette();
    let palette = new Palette();
    
    renderLoop();
    drawGridCanvas();

    function renderLoop() {
        board.render();
        info.firstChild.innerHTML = board.pixels.length;
        info.lastChild.innerHTML = board.w + ' x ' + board.h;
        requestAnimationFrame(renderLoop);
    }

    // outro
    setTimeout(()=>{document.getElementById('intro').style.opacity=0},5000);


    // -------------------------------------------------------
    // Pixel


    function Pixel(x, y, size, color, alpha = 1.0) {
        this.x = x;
        this.y = y;
        this.size = size;
        this.color = color;
        this.alpha = alpha;
        this.velocity = { x: 0, y: 0 };
        this.isAnimate = false;

        this.update = function() {
            this.x += this.velocity.x;
            this.y += this.velocity.y;
            setTimeout(() => {
                this.velocity = { x: 0, y: 0 };
                this.isAnimate = false;
            }, 2000);
        }
    }


    // -------------------------------------------------------
    // Board


    function Board() {
        this.pixels = [];
        this.x = 0;
        this.y = 0;
        this.w = 0;
        this.h = 0;
        this.minX = 0;
        this.minY = 0;
        this.maxX = 0;
        this.maxY = 0;
        this.origin = { x: 0, y: 0 }; // origin position
        this.pivot =  { x: 0, y: 0 }; // pivot (start at 0,0), translate the board as well
        this.center = { x: 0, y: 0 }; // absolute board center on screen
        this.snapPrecision = 2;

        this.render = function() {
            ctx.clearRect(0, 0, cb.width, cb.height);

            // in this case, fillRect is faster than beginPath() in my tests
            for (let i = 0; i < this.pixels.length; i++) {
                ctx.globalAlpha = this.pixels[i].alpha;
                ctx.fillStyle = this.pixels[i].color;
                ctx.fillRect(
                    this.pivot.x + this.pixels[i].x,
                    this.pivot.y + this.pixels[i].y,
                    this.pixels[i].size,
                    this.pixels[i].size);
                if (this.pixels[i].isAnimate)
                    this.pixels[i].update();
            }
            ctx.globalAlpha = 1;
            
            if (isDebugBoard)
                debugBoard();
        }

        this.add = function(pixel) {
            this.removeByShape(pixel); // no duplicate by shape
            this.pixels.push(pixel);
        }

        this.addFloor = function(pixel) {
            const pos = floor(pixel.x, pixel.y, pixel.size);
            pixel.x = pos.x;
            pixel.y = pos.y;
            this.removeByShape(pixel);
            this.pixels.push(pixel);
        }

        this.removeByPixel = function(pixel) { // find and remove by exact pixel object
            const index = this.findIndexByPixel(pixel);
            if (index > -1) this.pixels.splice(index, 1);
        }

        this.removeByShape = function(pixel) { // find and remove by shape only, no colors
            const index = this.findIndexByShape(pixel.x, pixel.y, pixel.size);
            if (index > -1) this.pixels.splice(index, 1);
        }

        this.removeByIndex = function(index) { // remove by index
            if (this.pixels[index])
                this.pixels.splice(index, 1);
        }

        this.translate = function(xDiff, yDiff) {
            if (this.pixels.length == 0) return;
            this.calcBoundingBox();
            //xDiff /= 2;
            //yDiff /= 2;
            xDiff *= this.snapPrecision;
            yDiff *= this.snapPrecision;
            const pos = floor(xDiff, yDiff, 1);
            this.pivot.x -= pos.x;
            this.pivot.y -= pos.y;
            this.calcBoundingBox();
        }

        this.recenter = function() {
            if (this.pixels.length == 0) return;
            this.calcBoundingBox();
            const x = ~~(cb.width/2) - this.origin.x;
            const y = ~~(cb.height/2) - this.origin.y;
            const pos = floor(x, y, tool.size);
            for (let i = 0; i < board.pixels.length; i++) {
                board.pixels[i].x += pos.x;
                board.pixels[i].y += pos.y;
            }
            this.pivot.x = 0;
            this.pivot.y = 0;
            this.calcBoundingBox();
        }

        this.calcBoundingBox = function() {
            this.minX = Math.min(...this.pixels.map(pixel => pixel.x));
            this.minY = Math.min(...this.pixels.map(pixel => pixel.y));
            this.maxX = Math.max(...this.pixels.map(pixel => pixel.x + pixel.size));
            this.maxY = Math.max(...this.pixels.map(pixel => pixel.y + pixel.size));
            this.x = this.minX;
            this.y = this.minY;
            this.w = this.maxX - this.minX;
            this.h = this.maxY - this.minY;
            this.origin = {
                x: this.maxX - (this.w / 2),
                y: this.maxY - (this.h / 2)
            }
            this.center = {
                x: this.pivot.x + this.origin.x,
                y: this.pivot.y + this.origin.y
            }
        }

        this.findPixelsByRectIntersect = function(x, y, size) {
            const selected = [];
            for (let i = 0; i < board.pixels.length; i++) {
                if (isIntersectRects({
                    x: board.pixels[i].x,
                    y: board.pixels[i].y,
                    w: board.pixels[i].x + (board.pixels[i].size/2),
                    h: board.pixels[i].y + (board.pixels[i].size/2)
                }, {
                    x: x,
                    y: y,
                    w: x + (size-1),
                    h: y + (size-1)
                })) {
                    selected.push(board.pixels[i]);
                }
            }
            return selected;
        }

        this.findPixelsByPointInArea = function(x, y, size) {
            const selected = [];
            for (let i = 0; i < board.pixels.length; i++) {
                if (isPointInRect({
                    x: board.pixels[i].x - size,
                    y: board.pixels[i].y - size,
                    w: size * 2,
                    h: size * 2},
                    { x: x, y: y })) {
                    selected.push(board.pixels[i]);
                }
            }
            return selected;
        }
        
        this.findIndexByPixel = function(pixel) {
            return this.pixels.findIndex(i =>
                i.x === pixel.x &&
                i.y === pixel.y &&
                i.size === pixel.size &&
                i.color === pixel.color &&
                i.alpha === pixel.alpha);
        }

        this.findIndexByShape = function(x, y, size) {
            return this.pixels.findIndex(
                i => i.x === x &&
                i.y === y &&
                i.size === size);
        }
    }


    // -------------------------------------------------------
    // Tool


    function Tool() {
        this.name = 'pencil';
        this.size = toolSizeList[parseInt(inputToolSize.value)];
        this.startPoint = { x: 0, y: 0 };
        this.endPoint = { x: 0, y: 0 };
        this.selectRect = {};

        this.pencil = function() {
            pixel = new Pixel(mouse.pivotX, mouse.pivotY, this.size, currentColor);
            board.addFloor(pixel);
        }

        this.line = function() { // 25 = interpolation tolerance
            let points = plotLine(25, this.startPoint.x, this.startPoint.y, this.endPoint.x, this.endPoint.y);
            if (points.length > 2 && points.length < 200) { // limit length for performance
                for (let i = 0; i < points.length; i++) {
                    pixel = new Pixel(
                        points[i].x - board.pivot.x,
                        points[i].y - board.pivot.y,
                        this.size, currentColor);
                    board.addFloor(pixel);
                }
            }
            points = null;
        }

        this.symmetry = function() {
            let pixel = new Pixel(mouse.pivotX, mouse.pivotY, this.size, currentColor);
            board.addFloor(pixel);

            pixel = new Pixel(cb.width-pixel.x-this.size, pixel.y, pixel.size, pixel.color);
            board.addFloor(pixel);
        }

        this.symmetryQuad = function() {
            let pixel = new Pixel(mouse.pivotX, mouse.pivotY, this.size, currentColor);
            board.addFloor(pixel);

            pixel = new Pixel(cb.width-pixel.x-this.size, cb.height-pixel.y-this.size, pixel.size, pixel.color);
            board.addFloor(pixel);

            pixel = new Pixel(cb.width-pixel.x-this.size, pixel.y, pixel.size, pixel.color);
            board.addFloor(pixel);

            pixel = new Pixel(cb.width-pixel.x-this.size, cb.height-pixel.y-this.size, pixel.size, pixel.color);
            board.addFloor(pixel);
        }

        this.spray = function() {
            const a = Math.random() * (PI2);
            const r = randomRangeFloat(0, parseFloat(inputToolSprayRadius.value));
            const x = mouse.pivotX + (Math.sin(a) * r);
            const y = mouse.pivotY + (Math.cos(a) * r);
            pixel = new Pixel(x, y, this.size, currentColor);
            board.addFloor(pixel);
        }

        this.eraser = function() {
            const pos = floor(mouse.pivotX, mouse.pivotY, this.size);
            const selected = board.findPixelsByRectIntersect(pos.x, pos.y, this.size);
            for (let i = 0; i < selected.length; i++)
                board.removeByPixel(selected[i]);
        }

        this.rectEraser = function() {
            this.calcSelectRect();
            let selected = [];
            for (let i = 0; i < board.pixels.length; i++) {
                const rect = {
                    x: board.pixels[i].x,
                    y: board.pixels[i].y,
                    w: board.pixels[i].size,
                    h: board.pixels[i].size};
                if (isIntersectRectsSize(this.selectRect, rect))
                    selected.push(board.pixels[i]);
            }
            for (let i = 0; i < selected.length; i++)
                board.removeByPixel(selected[i]);
            selected = null;
        }

        this.paint = function() {
            const preserveAlpha = inputToolPreserveAlpha.checked;
            const pos = floor(mouse.pivotX, mouse.pivotY, this.size);
            const selected = board.findPixelsByRectIntersect(pos.x, pos.y, this.size);
            for (let i = 0; i < selected.length; i++) {
                selected[i].color = currentColor;
                if (!preserveAlpha)
                    selected[i].alpha = 1.0;
            }
        }

        this.bucket = function() {
            const preserveAlpha = inputToolPreserveAlpha.checked;
            const color = getCanvasColor(ctx, mouse.x, mouse.y);
            for (let i = 0; i < board.pixels.length; i++) {
                if (board.pixels[i].color === color)
                    board.pixels[i].color = currentColor;
                    if (!preserveAlpha)
                        board.pixels[i].alpha = 1.0;
            }
        }

        this.eyedrop = function() { // TODO: get premultiplied-alpha color
            if (getCanvasAlpha(ctx, mouse.x, mouse.y) > 0) {
                const hex = getCanvasColor(ctx, mouse.x, mouse.y);
                setColor(hex);
                palette.setColor(hex);
            }
        }

        this.translate = function() {
            board.translate(mouse.lastX - mouse.x, mouse.lastY - mouse.y);
            //translateCanvas(cb); // may translate the canvas at some point
            //translateCanvas(cg);
            //translateCanvas(ch);
        }

        this.cyclone = function() {
            const velocity = parseFloat(inputToolCycloneVelocity.value);
            const selected = board.findPixelsByPointInArea(mouse.pivotX, mouse.pivotY, this.size);
            for (let i = 0; i < selected.length; i++) {
                const a = Math.random() * (PI2);
                selected[i].isAnimate = true;
                selected[i].velocity.x -= Math.sin(a) * velocity;
                selected[i].velocity.y -= Math.cos(a) * velocity;
            }
        }

        this.gravity = function() {
            const velocity = parseFloat(inputToolGravityVelocity.value);
            const selected = board.findPixelsByPointInArea(mouse.pivotX, mouse.pivotY, this.size);
            for (let i = 0; i < selected.length; i++) {
                selected[i].isAnimate = true;
                selected[i].velocity.y += Math.random() * velocity;
            }
        }

        this.onToolDown = function(isTouch) {
            switch (this.name) { // isTouch prevent accidental draw on touch-down
                case 'pencil':
                    if (!isTouch) this.pencil();
                    break;
                case 'line':
                    this.startPoint = { x: mouse.x, y: mouse.y };
                    break;
                case 'symmetry':
                    if (!isTouch) this.symmetry();
                    break;
                case 'symmetry-quad':
                    if (!isTouch) this.symmetryQuad();
                    break;
                case 'spray':
                    if (!isTouch) this.spray();
                    break;
                case 'eraser':
                    if (!isTouch) this.eraser();
                    break;
                case 'rect-eraser':
                    this.startPoint = { x: mouse.x, y: mouse.y };
                    break;
                case 'paint':
                    this.paint();
                    break;
                case 'bucket':
                    this.bucket();
                    break;
                case 'eyedrop':
                    this.eyedrop();
                    break;
                case 'translate':
                    break;
                case 'cyclone':
                    if (!isTouch) this.cyclone();
                    break;
                case 'gravity':
                    if (!isTouch) this.gravity();
                    break;
            }
        }

        this.onToolMove = function() {
            switch (this.name) {
                case 'pencil':
                    helper.rectangleSize(mouse.pivotX, mouse.pivotY, this.size);
                    if (mouse.isDown)
                        this.pencil();
                    break;
                case 'line':
                    helper.rectangleSize(mouse.pivotX, mouse.pivotY, this.size);
                    if (mouse.isDown)
                        helper.line(floor(this.startPoint.x, this.startPoint.y, 1), mouse);
                    break;
                case 'symmetry':
                    helper.symmLine();
                    if (mouse.isDown)
                        this.symmetry();
                    break;
                case 'symmetry-quad':
                    helper.symmLine();
                    if (mouse.isDown)
                        this.symmetryQuad();
                    break;
                case 'spray':
                    helper.circleStroke(mouse.x, mouse.y, parseInt(inputToolSprayRadius.value));
                    if (mouse.isDown)
                        this.spray();
                    break;
                case 'eraser':
                    helper.rectangleSize(mouse.pivotX, mouse.pivotY, this.size);
                    if (mouse.isDown)
                        this.eraser();
                    break;
                case 'rect-eraser':
                    helper.rectangleSize(mouse.pivotX, mouse.pivotY, 5);
                    if (mouse.isDown)
                        helper.rectangleRect(this.startPoint.x, this.startPoint.y, mouse.x, mouse.y);
                    break;
                case 'paint':
                    helper.point(mouse.x, mouse.y, this.size, currentColor);
                    if (mouse.isDown)
                        this.paint();
                    break;
                case 'bucket':
                    helper.point(mouse.x, mouse.y, 5, currentColor);
                    if (mouse.isDown)
                        this.bucket();
                    break;
                case 'eyedrop':
                    helper.point(mouse.x, mouse.y, 5);
                    if (mouse.isDown)
                        this.eyedrop();
                    break;
                case 'translate':
                    if (mouse.isDown)
                        this.translate();
                    helper.bbox();
                    break;
                case 'cyclone':
                    helper.circleStroke(mouse.x, mouse.y, this.size);
                    if (mouse.isDown)
                        this.cyclone();
                    break;
                case 'gravity':
                    helper.circleStroke(mouse.x, mouse.y, this.size);
                    if (mouse.isDown)
                        this.gravity();
                    break;
            }
        }

        this.onToolUp = function() {
            switch (this.name) {
                case 'pencil':
                    memory.record();
                    helper.clear();
                    break;
                case 'line':
                    this.endPoint = { x: mouse.x, y: mouse.y };
                    this.line();
                    memory.record();
                    helper.clear();
                    break;
                case 'symmetry':
                    memory.record();
                    break;
                case 'symmetry-quad':
                    memory.record();
                    break;
                case 'spray':
                    memory.record();
                    helper.clear();
                    break;
                case 'eraser':
                    this.eraser();
                    memory.record();
                    helper.clear();
                    break;
                case 'rect-eraser':
                    this.endPoint = { x: mouse.x, y: mouse.y };
                    this.rectEraser();
                    memory.record();
                    helper.clear();
                    break;
                case 'paint':
                    memory.record();
                    helper.clear();
                    break;
                case 'bucket':
                    memory.record();
                    helper.clear();
                    break;
                case 'eyedrop':
                    helper.clear();
                    break;
                case 'translate':
                    // using pivot system, no memory record needed
                    break;
                case 'cyclone':
                    this.cyclone();
                    memory.record();
                    helper.clear();
                    break;
                case 'gravity':
                    this.gravity();
                    memory.record();
                    helper.clear();
                    break;
            }

            selected = [];
            board.calcBoundingBox();
            if (isShowDynamicPalette)
                dynamicPalette.create(); // TODO: freeze/slowdown drawing on 9K+ pixels
        }

        this.selectButton = function(name, elem) {
            this.name = name;
            for (let i of document.getElementsByClassName('selected'))
                i.classList.remove('selected');
            elem.classList.add('selected');

            helper.clear();
            board.calcBoundingBox(); // important
            switch (this.name) { // they need a helper on button click
                case 'symmetry':
                    helper.symmLine();
                    break;
                case 'translate':
                    helper.bbox();
                    break;
            }
        }

        this.calcSelectRect = function() {
            let x, y, w, h;
            if (this.endPoint.x < this.startPoint.x && this.endPoint.y < this.startPoint.y) {
                x = this.endPoint.x - board.pivot.x; // bottom-right to top-left
                y = this.endPoint.y - board.pivot.y;
                w = this.startPoint.x - this.endPoint.x;
                h = this.startPoint.y - this.endPoint.y;
            }
            if (this.endPoint.x > this.startPoint.x && this.endPoint.y > this.startPoint.y) {
                x = this.startPoint.x - board.pivot.x; // top-left to bottom-right
                y = this.startPoint.y - board.pivot.y;
                w = this.endPoint.x - this.startPoint.x;
                h = this.endPoint.y - this.startPoint.y;
            }
            if (this.endPoint.x > this.startPoint.x && this.endPoint.y < this.startPoint.y) {
                x = this.startPoint.x - board.pivot.x; // bottom-left to top-right
                y = this.endPoint.y - board.pivot.y;
                w = Math.abs(this.startPoint.x - this.endPoint.x);
                h = Math.abs(this.endPoint.y - this.startPoint.y);
            }
            if (this.endPoint.x < this.startPoint.x && this.endPoint.y > this.startPoint.y) {
                x = this.endPoint.x - board.pivot.x; // top-right to bottom-left
                y = this.startPoint.y - board.pivot.y;
                w = Math.abs(this.endPoint.x - this.startPoint.x);
                h = Math.abs(this.startPoint.y - this.endPoint.y);
            }
            this.selectRect = { x: x, y: y, w: w, h: h };
        }
    }


    // -------------------------------------------------------
    // Helper
    

    function Helper() {
        this.color = '#00ffff'; // aqua

        this.point = function(x, y, size, color=this.color) {
            const pos = floor(x, y, size);
            this.clear();
            ctxh.fillStyle = color;
            ctxh.fillRect(pos.x, pos.y, size, size);
        }

        this.line = function(p1, p2) {
            this.clear();
            ctxh.beginPath();
            ctxh.moveTo(p1.x, p1.y);
            ctxh.lineTo(p2.x, p2.y);
            ctxh.strokeStyle = this.color;
            ctxh.stroke();
            ctxh.beginPath();
            ctxh.arc(p1.x, p1.y, tool.size/2, 0, PI2);
            ctxh.arc(p2.x, p2.y, tool.size/2, 0, PI2);
            ctxh.fillStyle = this.color;
            ctxh.fill();
        }

        this.rectangleRect = function(x, y, w, h) {
            this.clear();
            ctxh.save();
            ctxh.translate(0.5, 0.5); //res fix
            ctxh.setLineDash([2, 2]);
            ctxh.strokeStyle = this.color;
            ctxh.strokeRect(x, y, w-x, h-y);
            ctxh.setLineDash([]);
            ctxh.translate(-0.5, -0.5);
            ctxh.restore();
        }

        this.rectangleSize = function(x, y, size, color=this.color) {
            const pos = floor(x, y, size);
            this.clear();
            ctxh.save();
            ctxh.translate(board.pivot.x + 0.5, board.pivot.y + 0.5); //res fix
            ctxh.beginPath();
            ctxh.moveTo(pos.x, pos.y);
            ctxh.lineTo(pos.x, pos.y+size);
            ctxh.lineTo(pos.x+size, pos.y+size);
            ctxh.lineTo(pos.x+size, pos.y);
            ctxh.lineTo(pos.x, pos.y);
            ctxh.strokeStyle = color;
            ctxh.stroke();
            ctxh.translate(board.pivot.x - 0.5, board.pivot.y - 0.5);
            ctxh.restore();
        }

        this.circleStroke = function(x, y, r) {
            this.clear();
            ctxh.beginPath();
            ctxh.arc(x, y, r, 0, PI2);
            ctxh.strokeStyle = this.color + '30';
            ctxh.stroke();
        }

        this.bbox = function() {
            const x = board.pivot.x + board.x;
            const y = board.pivot.y + board.y;
            this.clear();
            ctxh.save();
            ctxh.translate(0.5, 0.5); //res fix
            ctxh.setLineDash([2, 2]);
            ctxh.beginPath();
            ctxh.moveTo(x, y);
            ctxh.lineTo(x, y+board.h);
            ctxh.lineTo(x+board.w, y+board.h);
            ctxh.lineTo(x+board.w, y);
            ctxh.lineTo(x, y);
            ctxh.strokeStyle = this.color;
            ctxh.stroke();
            ctxh.setLineDash([]);
            ctxh.translate(-0.5, -0.5);
            ctxh.restore();
        }

        this.symmLine = function() {
            const pos = floor(board.pivot.x + (cb.width/2), 1, tool.size/2);
            this.clear();
            ctxh.save();
            ctxh.translate(0.5, 0.5); //res fix
            ctxh.beginPath();
            ctxh.moveTo(pos.x, 30);
            ctxh.lineTo(pos.x, cb.height-30);
            ctxh.setLineDash([4, 4]);
            ctxh.strokeStyle = this.color + '30';
            ctxh.stroke();
            ctxh.setLineDash([]);
            ctxh.translate(-0.5, -0.5);
            ctxh.restore();
        }

        this.clear = function() {
            ctxh.clearRect(0, 0, ch.width, ch.height);
        }
    }


    // -------------------------------------------------------
    // Memory


    function Memory() {
        this.stack = [];
        this.block = -1;

        this.record = function() {
            this.stack[++this.block] = getData(board);
            this.stack.splice(this.block + 1); // delete anything forward
        }

        this.undo = function() {
            --this.block;
            if (this.stack[this.block]) {
                setData(this.stack[this.block]);
            } else {
                ++this.block;
            }
        }

        this.redo = function() {
            ++this.block;
            if (this.stack[this.block]) {
                setData(this.stack[this.block]);
            } else {
                --this.block;
            }
        }

        this.clear = function() {
            this.stack = [];
            this.block = -1;
            this.record(); // init memory block 0
        }
    }


    // -------------------------------------------------------
    // Dynamic Palette


    function DynamicPalette() {
        this.dom = document.getElementById('dynamiccolors');
        this.uniqueColors = [];

        this.create = function() {
            this.dom.innerHTML = "";
            this.uniqueColors = [];
            for (let i = 0; i < board.pixels.length; i++) {
                if (this.uniqueColors.indexOf(board.pixels[i].color) == -1) {
                    this.addLayer(board.pixels[i].color);
                    this.uniqueColors.push(board.pixels[i].color);
                }
            }
        }

        this.addLayer = function(hex) {
            const li = document.createElement('li');
            li.style.backgroundColor = hex;
            li.title = hex;

            li.addEventListener("click", function(ev) {
                currentColor = hex;
                inputColor.value = hex;
                palette.setColor(hex);
            }, false);

            this.dom.appendChild(li);
        }
    }


    // -------------------------------------------------------
    // Palette


    function Palette() {
        const dom = document.getElementById('palettecolors');
        let colors = [];
        let uls = [];
        let currentIndex = 0;
    
        this.init = function() {
            for (let i = 0; i < 8; i++) // generate shades
                colors.push(shadeColor(inputColor.value, i * 15));
            this.restore();
            this.createPalette();
            this.highlightSelected(0);
        }

        this.createPalette = function() {
            for (let i = 0; i < colors.length; i++) {
                const li = document.createElement('li');
                li.style.backgroundColor = colors[i];
                li.addEventListener('click', (evt) => {
                    const rgb = li.style.backgroundColor;
                    currentColor = rgbaIntToHex(rgb).toUpperCase();
                    inputColor.value = currentColor;
                    currentIndex = i;
                    palette.highlightSelected(i);
                }, false);
                dom.appendChild(li);
            }
            uls = document.querySelectorAll('#palettecolors li');
        }

        this.setColor = function(hex) {
            uls[currentIndex].style.backgroundColor = hex;
            colors[currentIndex] = hex;
            this.store();
        }

        this.shadeFill = function() {
            if (!confirm("Are you sure?")) return;
            for (let i = 0; i < colors.length; i++) {
                const c = shadeColor(inputColor.value, i * 15);
                uls[i].style.backgroundColor = c;
                colors[i] = c;
            }
            this.store();
        }

        this.highlightSelected = function(index) {
            for (let i = 0; i < uls.length; i++)
                uls[i].style.borderColor = '#333333';
            uls[index].style.borderColor = 'white';
        }

        this.store = function() {
            localStorage.setItem("pmcolor", JSON.stringify(colors));
        }

        this.restore = function() {
            let array = JSON.parse(localStorage.getItem("pmcolor"));
            if (!array) return;
            colors = array;
        }

        this.init();
    }


    // -------------------------------------------------------
    // Projects


    function saveProject() {
        downloadText(getFileData(board), inputProjectName.value + '.pmk');
    }

    function loadProject(data) {
        tool.selectButton('pencil', document.getElementById('startuptool'));
        setFileData(data);
        board.recenter();
        memory.clear();
    }

    function importImage(imgData) {
        tool.selectButton('pencil', document.getElementById('startuptool'));
        board = new Board();
        pixelateImage(imgData);
    }

    function exportPNG() {
        const baked = bakeCanvas(cb);
        downloadPNG(baked, `${inputProjectName.value}_${baked.width}x${baked.height}.png`);
    }

    function getData(board) {
        let data = `${board.pivot.x},${board.pivot.y}\n`;
        for (let i = 0; i < board.pixels.length; i++)
            data += `${board.pixels[i].x},${board.pixels[i].y},${board.pixels[i].size},${board.pixels[i].color},${board.pixels[i].alpha}\n`;
        return data;
    }

    function setData(data) { // for internal use, local storage and memory steps
        helper.clear();
        board = new Board();
        let line = '';
        let chunk = [];
        let lines = data.split('\n');
        board.pivot.x = parseInt(lines[0].split(',')[0]);
        board.pivot.y = parseInt(lines[0].split(',')[1]);
        for (let i = 1; i < lines.length - 1; i++) {
            if (lines[i]) { // ignore empty lines
                line = lines[i].replace(/\s+/g, ''); // strip whitespaces
                chunk = line.split(',');
                board.add(new Pixel(parseInt(chunk[0]), parseInt(chunk[1]), parseInt(chunk[2]), chunk[3], parseFloat(chunk[4])));
            }
        }
        lines = null;
        chunk = null;
        data = null;
        dynamicPalette.create();
    }

    function getFileData(board) {
        let data = '#' + inputProjectName.value + '\n';
        for (let i = 0; i < board.pixels.length; i++)
            data += `${board.pixels[i].x},${board.pixels[i].y},${board.pixels[i].size},${board.pixels[i].color},${board.pixels[i].alpha}\n`;
        return data;
    }

    function setFileData(data) {
        inputProjectName.value = 'Untitled';
        helper.clear();
        board = new Board();
        let line = '';
        let chunk = [];
        let lines = data.split('\n');
        for (let i = 0; i < lines.length; i++) {
            if (lines[i]) {
                if (lines[i].startsWith('#')) {
                    inputProjectName.value = lines[i].substring(1); // strip #
                } else {
                    line = lines[i].replace(/\s+/g, ''); // strip whitespaces
                    chunk = line.split(',');
                    board.add(new Pixel(parseInt(chunk[0]), parseInt(chunk[1]), parseInt(chunk[2]), chunk[3], parseFloat(chunk[4])));
                }
            }
        }
        lines = null;
        chunk = null;
        data = null;
        dynamicPalette.create();
    }

    function pixelateImage(imgData) {
        const ratio = parseFloat(document.getElementById('input-pixelate-ratio').value);
        const pixelSize = toolSizeList[parseInt(document.getElementById('input-pixelate-size').value)];
        const isFitScreen = document.getElementById('input-pixelate-fit').checked;
        const useAlpha = document.getElementById('input-pixelate-alpha').checked;
        const img = new Image();
        img.src = imgData;
        img.onload = () => {
            const c = document.createElement('canvas');
            const cx = c.getContext('2d');

            let dim = aspectRatioFit(img.width, img.height, cb.width/ratio, cb.height/ratio);
            c.width = dim.width;
            c.height = dim.height;
            if (isFitScreen) {
                dim = aspectRatioFit(img.width, img.height, cb.width/1.5, cb.height/1.5);
                c.width = dim.width;
                c.height = dim.height;
            }

            cx.msImageSmoothingEnabled = false;
            cx.mozImageSmoothingEnabled = false;
            cx.webkitImageSmoothingEnabled = false;
            cx.imageSmoothingEnabled = false;
            cx.drawImage(img, 0, 0, c.width, c.height);

            const cenX = (cb.width/2 - c.width/2);
            const cenY = (cb.height/2 - c.height/2);
            let imageData = cx.getImageData(0, 0, c.width, c.height).data;
            let pixelData = [];
            let x,y,r,g,b,a,f;
            for (let i = 0; i < imageData.length; i += 4) {
                a = imageData[i + 3];
                if (!useAlpha && a < 255) a = 0;
                if (a > 0) {
                    r = imageData[i];
                    g = imageData[i + 1];
                    b = imageData[i + 2];
                    x = (i / 4) % c.width;
                    y = ~~(i / 4 / c.width);
                    f = floor(cenX + x, cenY + y, pixelSize);
                    board.add(new Pixel(
                        f.x, f.y, pixelSize,
                        rgbIntToHex(r, g, b),
                        (a / 255).toFixed(1)));
                }
            }
            board.recenter();
            memory.clear();
            dynamicPalette.create();
            helper.clear();

            imgData = null;
            imageData = null;
            pixelData = null;
        }
    }

    function loadFile(url) {
        fetch(url).then(
            function(response) {
                if (response.status !== 200) {
                    console.log('loadFile', response.status);
                    return;
                }
                response.text().then(function(data) {
                    loadProject(data);
                });
            }
        ).catch(function(err) {
            console.log('loadFile', err);
        });
    }

    function copyBase64Image() {
        const baked = bakeCanvas(cb);
        clipboardBase64(baked);
    }

    function pasteBase64Image() {
        navigator.clipboard.readText()
            .then(text => {
                if (text.startsWith('data:image/')) {
                    importImage(text);
                } else {
                    console.log('invalid base64 image');
                }
            })
            .catch(err => {
                console.log('failed to read clipboard data');
            });
    }


    // -------------------------------------------------------
    // Storages


    function store() {
        localStorage.setItem("pixelmonk", JSON.stringify(getData(board, true)));
    }

    function restore() {
        let str = localStorage.getItem("pixelmonk");
        if (!str) return;
        setData(JSON.parse(str), true);
        memory.clear();
    }
    
    function createStorages() {
        for (let i = 0; i < storages.length; i++) {
            const img = storages[i].children[0];
            const clear = storages[i].children[1].firstChild;
            const save = storages[i].children[1].lastChild;
            img.src = storageHolder;
            img.id = 'storage' + i;

            //get storage images on startup
            const storage = localStorage.getItem('pmstorageimg' + img.id);
            if (storage) img.src = storage;

            clear.addEventListener("click", function (ev) {
                img.src = storageHolder;
                localStorage.removeItem('pmstorageimg' + img.id);
                localStorage.removeItem('pmstorage' + img.id);
            }, false);

            save.addEventListener("click", function (ev) {
                if (board.pixels.length == 0) return;
                const c = bakeCanvasScaled(cb, img.clientWidth, img.clientHeight).toDataURL("image/png");
                localStorage.setItem('pmstorageimg' + img.id, c);
                localStorage.setItem('pmstorage' + img.id, JSON.stringify(getData(board, true)));
                img.src = c;
            }, false);

            img.addEventListener("click", function (ev) {
                const data = localStorage.getItem('pmstorage' + img.id);
                if (data) {
                    setData(JSON.parse(data), true);
                    board.recenter();
                    memory.record();
                }
            }, false);
        }
    }


    // -------------------------------------------------------
    // Events


    inputColor.addEventListener("input", function (ev) {
        currentColor = this.value.toUpperCase();
        palette.setColor(currentColor);
    }, false);

    inputBgColor.addEventListener("input", function (ev) {
        cg.style.background = this.value;
    }, false);

    inputToolSize.addEventListener("input", function (ev) {
        tool.size = toolSizeList[parseInt(this.value)];
        drawGridCanvas();
    }, false);

    cb.addEventListener('mousemove', function(ev) {
        mouse.x = ev.clientX;
        mouse.y = ev.clientY;
        mouse.pivotX = mouse.x - board.pivot.x - canvasOffset.x;
        mouse.pivotY = mouse.y - board.pivot.y - canvasOffset.y;
        tool.onToolMove();
        mouse.lastX = mouse.x;
        mouse.lastY = mouse.y;
        if (isShowMagnifier)
            drawMagnifierCanvas();
    }, false);

    cb.addEventListener('mousedown', function(ev) {
        mouse.x = ev.clientX;
        mouse.y = ev.clientY;
        mouse.lastX = mouse.x;
        mouse.lastY = mouse.y;
        mouse.pivotX = mouse.x - board.pivot.x - canvasOffset.x;
        mouse.pivotY = mouse.y - board.pivot.y - canvasOffset.y;
        mouse.isDown = true;
        tool.onToolDown(false);
        toggleLayout(false);
    }, false);

    cb.addEventListener('mouseup', function(ev) {
        mouse.lastX = 0;
        mouse.lastY = 0;
        mouse.isDown = false;
        tool.onToolUp();
        if (isShowMagnifier)
            drawMagnifierCanvas();
        toggleLayout(true);
    }, false);

    cb.addEventListener('contextmenu', function(ev) {
        ev.preventDefault();
    }, false);
    
    cb.addEventListener('touchmove', function(ev) {
        mouse.x = ev.touches[0].clientX;
        mouse.y = ev.touches[0].clientY;
        mouse.pivotX = mouse.x - board.pivot.x - canvasOffset.x;
        mouse.pivotY = mouse.y - board.pivot.y - canvasOffset.y;
        tool.onToolMove();
        mouse.lastX = mouse.x;
        mouse.lastY = mouse.y;
        if (isShowMagnifier)
            drawMagnifierCanvas();
    }, false);

    cb.addEventListener('touchstart', function(ev) {
        mouse.x = ev.touches[0].clientX;
        mouse.y = ev.touches[0].clientY;
        mouse.lastX = mouse.x;
        mouse.lastY = mouse.y;
        mouse.pivotX = mouse.x - board.pivot.x - canvasOffset.x;
        mouse.pivotY = mouse.y - board.pivot.y - canvasOffset.y;
        mouse.isDown = true;
        tool.onToolDown(true);
        toggleLayout(false);
    }, false);

    cb.addEventListener('touchend', function(ev) {
        mouse.lastX = 0;
        mouse.lastY = 0;
        mouse.isDown = false;
        tool.onToolUp();
        if (isShowMagnifier)
            drawMagnifierCanvas();
        toggleLayout(true);
    }, false);

    window.addEventListener("resize", function() {
        cg.width = window.innerWidth;
        cg.height = window.innerHeight;
        cb.width = window.innerWidth;
        cb.height = window.innerHeight;
        ch.width = window.innerWidth;
        ch.height = window.innerHeight;
        drawGridCanvas();
        board.recenter();
    }, false);

    function fileHandler(file) {
        const ext = file.name.split('.').pop().toLowerCase(); //ext|exts
        const url = URL.createObjectURL(file);
        const reader = new FileReader();
        reader.onload = function() {
            if (ext === 'pmk') loadProject(reader.result);
            if (['jpg','png'].includes(ext)) importImage(reader.result);
            URL.revokeObjectURL(url);
        }
        if (ext === 'pmk') {
            reader.readAsText(file);
        } else {
            reader.readAsDataURL(file);
        }
    }

    function dropHandler(ev) {
        ev.preventDefault();
        fileHandler(ev.dataTransfer.files[0]);
    }
    function dragHandler(ev) {
        ev.preventDefault();
    }
    function dragLeaveHandler(ev) {
        ev.preventDefault();
    }

    document.getElementById('openfile_pmk').addEventListener("change", function (ev) {
        if (ev.target.files.length > 0)
            fileHandler(ev.target.files[0]);
    }, false);

    document.getElementById('openfile_img').addEventListener("change", function (ev) {
        if (ev.target.files.length > 0)
            fileHandler(ev.target.files[0]);
    }, false);

    window.addEventListener("keyup", function (ev) {
        switch (ev.key) {
            case 'm':
                toggleMagnifier();
                break;
        }
        if (ev.ctrlKey && ev.key === 'z') memory.undo();
        if (ev.ctrlKey && ev.key === 'x') memory.redo();
    }, false);


    // -------------------------------------------------------
    // Canvases


    function drawGridCanvas() {
        cg.style.background = inputBgColor.value;
        ctxg.clearRect(0, 0, cg.width, cg.height);
        ctxg.save();
        ctxg.translate(0.5, 0.5);
        ctxg.beginPath();
        for (let x = 0; x <= cg.width; x += tool.size) {
            ctxg.moveTo(Math.floor(x), 0);
            ctxg.lineTo(Math.floor(x), cg.height);
        }
        for (let y = 0; y <= cg.height; y += tool.size) {
            ctxg.moveTo(0, Math.floor(y));
            ctxg.lineTo(cg.width, Math.floor(y));
        }
        ctxg.strokeStyle = '#ffffff05';
        ctxg.lineWidth = 1;
        ctxg.stroke();
        ctxg.translate(-0.5, -0.5);
        ctxg.restore();
    }

    function drawMagnifierCanvas() {
        const zoom = 300;
        const pos = { x: mouse.x - (zoom / 18.5), y: mouse.y - (zoom / 18.5) };
        ctxm.clearRect(0, 0, cm.width, cm.height);
        ctxm.msImageSmoothingEnabled = false;
        ctxm.mozImageSmoothingEnabled = false;
        ctxm.webkitImageSmoothingEnabled = false;
        ctxm.imageSmoothingEnabled = false;
        ctxm.drawImage(cg, pos.x, pos.y, cm.width,cm.height, 0,0, zoom,zoom);
        ctxm.drawImage(cb, pos.x, pos.y, cm.width,cm.height, 0,0, zoom,zoom);
        ctxm.drawImage(ch, pos.x, pos.y, cm.width,cm.height, 0,0, zoom,zoom);
    }

    function toggleMagnifier() {
        isShowMagnifier = !isShowMagnifier;
        if (isShowMagnifier) {
            cm.style.display = 'unset';
        } else {
            cm.style.display = 'none';
        }
    }

    function toggleDynamicPalette() {
        isShowDynamicPalette = !isShowDynamicPalette;
        if (isShowDynamicPalette) {
            document.getElementById('dynamicpalette').style.display = 'unset';
        } else {
            document.getElementById('dynamicpalette').style.display = 'none';
        }
    }


    // -------------------------------------------------------
    // User Interface


    let isShowMenu = false;
    let isShowPrefs = false;
    let isShowStorage = false;
    let isShowFilters = false;

    function showMenu() {
        const menu = document.getElementById('menumain');
        isShowMenu = !isShowMenu;
        if (isShowMenu) {
            if (isShowPrefs) showPrefs();
            if (isShowStorage) showStorage();
            if (isShowFilters) showFilters();
            menu.style.transform = 'translate(37px, 0)';
        } else {
            menu.style.transform = 'translate(37px, -500px)';
        }
    }

    function showPrefs() {
        const menu = document.getElementById('menuprefs');
        isShowPrefs = !isShowPrefs;
        if (isShowPrefs) {
            if (isShowMenu) showMenu();
            if (isShowStorage) showStorage();
            if (isShowFilters) showFilters();
            menu.style.transform = 'translate(37px, 0)';
        } else {
            menu.style.transform = 'translate(37px, -500px)';
        }
    }

    function showStorage() {
        const menu = document.getElementById('menustorage');
        isShowStorage = !isShowStorage;
        if (isShowStorage) {
            if (isShowMenu) showMenu();
            if (isShowPrefs) showPrefs();
            if (isShowFilters) showFilters();
            menu.style.transform = 'translate(37px, 0)';
            createStorages();
        } else {
            menu.style.transform = 'translate(37px, -500px)';
        }
    }

    function showFilters() {
        const menu = document.getElementById('menufilters');
        isShowFilters = !isShowFilters;
        if (isShowFilters) {
            if (isShowMenu) showMenu();
            if (isShowPrefs) showPrefs();
            if (isShowStorage) showStorage();
            menu.style.transform = 'translate(37px, 0)';
        } else {
            menu.style.transform = 'translate(37px, -500px)';
        }
    }

    let xOffset = 0, yOffset = 0;
    function dragElement(elem) {
        let active = false;
        let currentX, currentY, initialX, initialY;

        // prevent fast-dragging problem with background elements
        document.body.addEventListener("mousedown", dragStart, false);
        document.body.addEventListener("mouseup", dragEnd, false);
        document.body.addEventListener("mousemove", drag, false);
        document.body.addEventListener("touchstart", dragStart, false);
        document.body.addEventListener("touchend", dragEnd, false);
        document.body.addEventListener("touchmove", drag, false);
        function dragStart(e) {
            if (e.type === "touchstart") {
                initialX = e.touches[0].clientX - xOffset;
                initialY = e.touches[0].clientY - yOffset;
            } else {
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;
            }
            if (e.target === elem) active = true;
        }
        function dragEnd(e) {
            initialX = currentX;
            initialY = currentY;
            active = false;
            document.body.removeEventListener("mousedown", dragStart, false);
            document.body.removeEventListener("mouseup", dragEnd, false);
            document.body.removeEventListener("mousemove", drag, false);
            document.body.removeEventListener("touchstart", dragStart, false);
            document.body.removeEventListener("touchend", dragEnd, false);
            document.body.removeEventListener("touchmove", drag, false);
        }
        function drag(e) {
            if (active) {
                if (e.type === "touchmove") {
                    currentX = e.touches[0].clientX - initialX;
                    currentY = e.touches[0].clientY - initialY;
                } else {
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;
                }
                xOffset = currentX;
                yOffset = currentY;
                setTranslate(currentX, currentY, elem.parentElement);
            }
        }
        function setTranslate(xPos, yPos, el) {
            el.style.transform = "translate3d(" + xPos + "px, " + yPos + "px, 0)";
        }
    }


    // -------------------------------------------------------
    // Functions


    function reset() {
        board = new Board();
        tool = new Tool();
        helper = new Helper();
        memory = new Memory();
        dynamicPalette.create();
        tool.selectButton('pencil', document.getElementById('startuptool'));
        tool.size = 10;
        inputProjectName.value = 'Untitled';
        inputToolSize.value = 4; // index of 10
        inputToolSize.previousElementSibling.innerHTML = 10;
        inputToolQuadSymm.checked = false;
        inputToolPreserveAlpha.checked = true;
        inputToolSprayRadius.value = 30;
        inputToolCycloneVelocity.value = 0.03;
        inputToolGravityVelocity.value = 0.015;
        inputBgColor.value = '#2F333C';
        cg.style.background = inputBgColor.value;
        setColor('#CD5C5C');
    }

    function clearBoard() {
        if (!confirm("Are you sure?")) return;
        board = new Board();
        helper = new Helper();
        memory = new Memory();
        dynamicPalette.create();
    }

    function recenter() {
        board.recenter();
        helper.bbox();
    }

    function setColor(hex) {
        currentColor = hex;
        inputColor.value = hex;
    }

    function filterSepia(pixels) {
        let rgb = null;
        for (let i = 0; i < pixels.length; i++) {
            rgb = hexToRgbInt(pixels[i].color);
            pixels[i].color = rgbIntToHex(
                Math.min(Math.round(0.393 * rgb.r + 0.769 * rgb.g + 0.189 * rgb.b), 255),
                Math.min(Math.round(0.349 * rgb.r + 0.686 * rgb.g + 0.168 * rgb.b), 255),
                Math.min(Math.round(0.272 * rgb.r + 0.534 * rgb.g + 0.131 * rgb.b), 255)
            );
        }
        memory.record();
    }

    function filterGrayscale(pixels) {
        let rgb = null;
        for (let i = 0; i < pixels.length; i++) {
            rgb = hexToRgbInt(pixels[i].color);
            pixels[i].color = rgbIntToHex(
                (rgb.r + rgb.g + rgb.b) / 3,
                (rgb.r + rgb.g + rgb.b) / 3,
                (rgb.r + rgb.g + rgb.b) / 3
            );
        }
        memory.record();
    }

    function filterInvert(pixels) {
        let rgb = null;
        for (let i = 0; i < pixels.length; i++) {
            rgb = hexToRgbInt(pixels[i].color);
            pixels[i].color = rgbIntToHex(
                255 - rgb.r,
                255 - rgb.g,
                255 - rgb.b
            );
        }
        memory.record();
    }

    function translateCanvas(canvas) {
        canvasOffset.x = canvas.offsetLeft - (mouse.lastX - mouse.x);
        canvasOffset.y = canvas.offsetTop - (mouse.lastY - mouse.y);
        canvas.style.left = canvasOffset.x + "px";
        canvas.style.top = canvasOffset.y + "px";
        canvas.style.border = 'dotted 1px aqua';
        setTimeout(() => {
            canvas.style.border = 'none';
        }, 1000);
    }

    function bakeCanvas(canvas) {
        board.calcBoundingBox();
        const c = document.createElement('canvas');
        c.width = board.w;
        c.height = board.h; // crop to content
        c.getContext('2d').drawImage(canvas, board.pivot.x+board.x, board.pivot.y+board.y, board.w, board.h, 0, 0, c.width, c.height);
        return c;
    }

    function bakeCanvasScaled(canvas, width, height) {
        const org = bakeCanvas(canvas);
        const c = document.createElement('canvas');
        const aspect = aspectRatioFit(org.width, org.height, width-5, height-5); // -padding
        c.width = width;
        c.height = height; // resize to aspect
        c.getContext('2d').drawImage(org, (width-aspect.width)/2, (height-aspect.height)/2, aspect.width, aspect.height);
        return c;
    }

    function debugBoard() {
        board.calcBoundingBox();

        ctx.fillStyle = 'red';
        ctx.fillRect(board.origin.x-5/2, board.origin.y-5/2, 5, 5);
        ctx.fillStyle = 'green';
        ctx.fillRect(board.pivot.x-5/2, board.pivot.y-5/2, 5, 5);
        ctx.fillStyle = 'blue';
        ctx.fillRect(board.center.x-5/2, board.center.y-5/2, 5, 5);

        ctx.save();
        ctx.translate(0.5, 0.5); //res fix
        ctx.setLineDash([2, 2]);
        ctx.strokeStyle = 'yellow';
        ctx.strokeRect(board.pivot.x+board.x, board.pivot.y+board.y, board.w, board.h);
        ctx.setLineDash([]);
        ctx.translate(-0.5, -0.5);
    }

    function toggleLayout(visible) {
        if (visible) {
            document.getElementById('hover').style.pointerEvents = 'unset';
            document.getElementById('input-color').style.pointerEvents = 'unset';
            document.getElementById('dynamicpalette').style.pointerEvents = 'unset';
            document.getElementById('palette').style.pointerEvents = 'unset';
            document.getElementById('toolbar_R').style.pointerEvents = 'unset';
            document.getElementById('toolbar_C').style.pointerEvents = 'unset';
            document.getElementById('toolbar_L').style.pointerEvents = 'unset';
            document.getElementById('toolbar_B').style.pointerEvents = 'unset';
            document.getElementById('info').style.pointerEvents = 'unset';
        } else {
            document.getElementById('hover').style.pointerEvents = 'none';
            document.getElementById('input-color').style.pointerEvents = 'none';
            document.getElementById('dynamicpalette').style.pointerEvents = 'none';
            document.getElementById('palette').style.pointerEvents = 'none';
            document.getElementById('toolbar_R').style.pointerEvents = 'none';
            document.getElementById('toolbar_C').style.pointerEvents = 'none';
            document.getElementById('toolbar_L').style.pointerEvents = 'none';
            document.getElementById('toolbar_B').style.pointerEvents = 'none';
            document.getElementById('info').style.pointerEvents = 'none';
        }
    }
    
    
    // -------------------------------------------------------
    // Utils


    function floor(x, y, size) {
        return {
            x: size * ~~(x / size),
            y: size * ~~(y / size)
        }
    }

    function isIntersectRectsSize(rect1, rect2) {
        return (rect1.x < rect2.x + rect2.w &&
                rect1.x + rect1.w > rect2.x &&
                rect1.y < rect2.y + rect2.h &&
                rect1.y + rect1.h > rect2.y);
    }

    function isIntersectRects(rect1, rect2) {
        return !(rect2.x > rect1.w ||
                 rect2.w < rect1.x ||
                 rect2.y > rect1.h ||
                 rect2.h < rect1.y);
    }

    function isPointInRect(rect, point) {
        return (point.x > rect.x &&
                point.x < rect.x + rect.w &&
                point.y > rect.y &&
                point.y < rect.y + rect.h);
    }

    function isIntersectColor(x, y, color) {
        return getCanvasColor(ctx, x, y) === color;
    }

    function getCanvasColorPremultiplied(context, x, y) {
        const data = context.getImageData(x, y, 1, 1).data;
        const a = data[3] / 255 + 0.1; // TODO prototype
        const r = data[0] * a;
        const g = data[1] * a;
        const b = data[2] * a;
        return rgbIntToHex(r, g, b);
    }

    function getCanvasColor(context, x, y) {
        const data = context.getImageData(x, y, 1, 1).data;
        return rgbIntToHex(data[0], data[1], data[2]);
    }

    function getCanvasAlpha(context, x, y) {
        const data = context.getImageData(x, y, 1, 1).data;
        return (data[3] / 255).toFixed(1);
    }

    function rgbIntToHex(r, g, b) {
        return '#' + (0x1000000 + b | (g << 8) | (r << 16)).toString(16).slice(1).toUpperCase();
    }

    const rgbaIntToHex = (rgba) => `#${rgba.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+\.{0,1}\d*))?\)$/).slice(1).map((n, i) => (i === 3 ? Math.round(parseFloat(n) * 255) : parseFloat(n)).toString(16).padStart(2, '0').replace('NaN', '')).join('')}`

    function hexToRgbInt(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    function shadeColor(color, percent) {
        let R = parseInt(color.substring(1,3),16);
        let G = parseInt(color.substring(3,5),16);
        let B = parseInt(color.substring(5,7),16);

        R = parseInt(R * (100 + percent) / 100);
        G = parseInt(G * (100 + percent) / 100);
        B = parseInt(B * (100 + percent) / 100);
        R = (R<255)?R:255;  
        G = (G<255)?G:255;  
        B = (B<255)?B:255;  

        let RR = ((R.toString(16).length==1)?"0"+R.toString(16):R.toString(16));
        let GG = ((G.toString(16).length==1)?"0"+G.toString(16):G.toString(16));
        let BB = ((B.toString(16).length==1)?"0"+B.toString(16):B.toString(16));
        return "#"+RR+GG+BB;
    }

    function randomRangeFloat(min, max) {
        return Math.random() * (max - min) + min;
    }

    function aspectRatioFit(srcW, srcH, maxW, maxH) {
        const ratio = Math.min(maxW / srcW, maxH / srcH);
        return { width: srcW * ratio, height: srcH * ratio };
    }

    function plotLine(tolerance, Ax,Ay, Bx,By) {
        const dx = Bx - Ax;
        const dy = By - Ay;
        const ptCount = parseInt(Math.sqrt(dx*dx + dy*dy)) * 3;
        let lastX = -10000;
        let lastY = -10000;
        let pts = [{ x: Ax, y: Ay }];
        let t, x, y, dx1, dy1;
        for (let i = 1; i <= ptCount; i++) {
            t = i / ptCount;
            x = Ax + dx * t;
            y = Ay + dy * t;
            dx1 = x - lastX;
            dy1 = y - lastY;
            if (dx1*dx1 + dy1*dy1 > tolerance) {
                pts.push({ x: x, y: y });
                lastX = x;
                lastY = y;
            }
        }
        pts.push({ x: Bx, y: By });
        return pts;
    }

    function downloadPNG(canvas, filename) {
        const link = document.createElement("a");
        link.download = filename;
        link.href = canvas.toDataURL("image/png");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    function downloadText(txt, filename) {
        const blob = new Blob([ txt ], { type: "text/plain" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
    }

    function clipboardBase64(canvas) {
        const input = document.createElement('input');
        input.type = 'image/png';
        input.value = canvas.toDataURL('image/png');
        document.body.appendChild(input);
        input.select();
        document.execCommand('copy');
        document.body.removeChild(input);
    }

    function clipboard(str) {
        const input = document.createElement('input');
        input.type = 'text';
        input.value = str;
        document.body.appendChild(input);
        input.select();
        document.execCommand('copy');
        document.body.removeChild(input);
    }

    function toggleFullscreen() {
        if (document.fullscreenElement) {
            document.exitFullscreen();
        } else {
            document.body.requestFullscreen();
        }
    }


    // -------------------------------------------------------
    // Startup


    loadProject(`410,260,10,#CD5C5C,1
                420,260,10,#CD5C5C,1
                430,260,10,#CD5C5C,1
                440,260,10,#CD5C5C,1
                440,270,10,#CD5C5C,1
                440,290,10,#CD5C5C,1
                430,290,10,#CD5C5C,1
                420,290,10,#CD5C5C,1
                410,290,10,#CD5C5C,1
                410,280,10,#CD5C5C,1
                410,270,10,#CD5C5C,1
                390,260,10,#CD5C5C,1
                380,260,10,#CD5C5C,1
                370,260,10,#CD5C5C,1
                360,260,10,#CD5C5C,1
                360,270,10,#CD5C5C,1
                360,280,10,#CD5C5C,1
                360,290,10,#CD5C5C,1
                410,310,10,#CD5C5C,1
                420,310,10,#CD5C5C,1
                430,310,10,#CD5C5C,1
                440,310,10,#CD5C5C,1
                440,320,10,#CD5C5C,1
                440,330,10,#CD5C5C,1
                440,340,10,#CD5C5C,1
                430,340,10,#CD5C5C,1
                420,340,10,#CD5C5C,1
                410,340,10,#CD5C5C,1
                410,330,10,#CD5C5C,1
                410,320,10,#CD5C5C,1
                390,340,10,#CD5C5C,1
                380,340,10,#CD5C5C,1
                370,340,10,#CD5C5C,1
                360,340,10,#CD5C5C,1
                360,330,10,#CD5C5C,1
                360,320,10,#CD5C5C,1
                360,310,10,#CD5C5C,1
                440,280,10,#CD5C5C,1
                390,330,10,#CD5C5C,1
                390,320,10,#CD5C5C,1
                390,280,10,#CD5C5C,1
                390,270,10,#CD5C5C,1
                390,290,10,#CD5C5C,1
                380,290,10,#CD5C5C,1
                370,290,10,#CD5C5C,1
                390,310,10,#CD5C5C,1
                380,310,10,#CD5C5C,1
                370,310,10,#CD5C5C,1
                380,270,10,#FFA3A3,1
                430,270,10,#FFA3A3,1
                430,320,10,#FFA3A3,1
                380,320,10,#FFA3A3,1
                420,270,10,#F37F7F,1
                420,280,10,#F37F7F,1
                430,280,10,#F37F7F,1
                370,270,10,#F37F7F,1
                370,280,10,#F37F7F,1
                380,280,10,#F37F7F,1
                370,320,10,#F37F7F,1
                370,330,10,#F37F7F,1
                380,330,10,#F37F7F,1
                420,320,10,#F37F7F,1
                420,330,10,#F37F7F,1
                430,330,10,#F37F7F,1
                360,300,10,#292929,1
                370,300,10,#292929,1
                380,300,10,#292929,1
                390,300,10,#292929,1
                410,300,10,#292929,1
                420,300,10,#292929,1
                430,300,10,#292929,1
                440,300,10,#292929,1
                400,260,10,#292929,1
                400,270,10,#292929,1
                400,280,10,#292929,1
                400,290,10,#292929,1
                400,300,10,#292929,1
                400,310,10,#292929,1
                400,320,10,#292929,1
                400,330,10,#292929,1
                400,340,10,#292929,1
                360,250,10,#292929,1
                370,250,10,#292929,1
                380,250,10,#292929,1
                390,250,10,#292929,1
                400,250,10,#292929,1
                410,250,10,#292929,1
                420,250,10,#292929,1
                430,250,10,#292929,1
                440,250,10,#292929,1
                450,250,10,#292929,1
                450,260,10,#292929,1
                450,270,10,#292929,1
                450,280,10,#292929,1
                450,290,10,#292929,1
                450,300,10,#292929,1
                450,310,10,#292929,1
                450,320,10,#292929,1
                450,330,10,#292929,1
                450,340,10,#292929,1
                450,350,10,#292929,1
                440,350,10,#292929,1
                430,350,10,#292929,1
                420,350,10,#292929,1
                410,350,10,#292929,1
                400,350,10,#292929,1
                390,350,10,#292929,1
                380,350,10,#292929,1
                370,350,10,#292929,1
                360,350,10,#292929,1
                350,350,10,#292929,1
                350,340,10,#292929,1
                350,330,10,#292929,1
                350,320,10,#292929,1
                350,310,10,#292929,1
                350,300,10,#292929,1
                350,290,10,#292929,1
                350,280,10,#292929,1
                350,270,10,#292929,1
                350,260,10,#292929,1
                350,250,10,#292929,1`);

</script>
</html>
