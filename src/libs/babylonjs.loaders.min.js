!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("babylonjs")):"function"==typeof define&&define.amd?define("babylonjs-loaders",["babylonjs"],t):"object"==typeof exports?exports["babylonjs-loaders"]=t(require("babylonjs")):e.LOADERS=t(e.BABYLON)}("undefined"!=typeof self?self:"undefined"!=typeof global?global:this,(e=>(()=>{"use strict";var t,n,r={597:t=>{t.exports=e}},o={};function a(e){var t=o[e];if(void 0!==t)return t.exports;var n=o[e]={exports:{}};return r[e](n,n.exports,a),n.exports}n=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,a.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var o=Object.create(null);a.r(o);var i={};t=t||[null,n({}),n([]),n(n)];for(var s=2&r&&e;"object"==typeof s&&!~t.indexOf(s);s=n(s))Object.getOwnPropertyNames(s).forEach((t=>i[t]=()=>e[t]));return i.default=()=>e,a.d(o,i),o},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};a.d(i,{default:()=>wn});var s={};a.r(s),a.d(s,{GLTFValidation:()=>w});var l={};a.r(l),a.d(l,{GLTFFileLoader:()=>G,GLTFLoaderAnimationStartMode:()=>E,GLTFLoaderCoordinateSystemMode:()=>O,GLTFLoaderState:()=>M});var c={};a.r(c),a.d(c,{EBlendingFunction:()=>k,EComponentType:()=>R,ECullingType:()=>V,EParameterType:()=>F,EShaderType:()=>P,ETextureFilterType:()=>B,ETextureFormat:()=>D,ETextureWrapMode:()=>I,GLTFBinaryExtension:()=>he,GLTFLoader:()=>ue,GLTFLoaderBase:()=>ce,GLTFLoaderExtension:()=>de,GLTFMaterialsCommonExtension:()=>fe,GLTFUtils:()=>H});var u={};a.r(u);var d={};a.r(d),a.d(d,{EXT_lights_image_based:()=>we,EXT_mesh_gpu_instancing:()=>Le,EXT_meshopt_compression:()=>Se,EXT_texture_avif:()=>Ie,EXT_texture_webp:()=>Pe,ExtrasAsMetadata:()=>on,KHR_animation_pointer:()=>Vt,KHR_draco_mesh_compression:()=>De,KHR_interactivity:()=>en,KHR_lights:()=>ke,KHR_materials_anisotropy:()=>Xe,KHR_materials_clearcoat:()=>We,KHR_materials_diffuse_transmission:()=>ut,KHR_materials_dispersion:()=>pt,KHR_materials_emissive_strength:()=>Je,KHR_materials_ior:()=>rt,KHR_materials_iridescence:()=>qe,KHR_materials_pbrSpecularGlossiness:()=>Ue,KHR_materials_sheen:()=>$e,KHR_materials_specular:()=>tt,KHR_materials_transmission:()=>lt,KHR_materials_unlit:()=>je,KHR_materials_variants:()=>at,KHR_materials_volume:()=>ht,KHR_mesh_quantization:()=>mt,KHR_node_visibility:()=>nn,KHR_texture_basisu:()=>vt,KHR_texture_transform:()=>gt,KHR_xmp_json_ld:()=>Tt,MSFT_audio_emitter:()=>Gt,MSFT_lod:()=>Ht,MSFT_minecraftMesh:()=>Kt,MSFT_sRGBFactors:()=>Yt});var h={};a.r(h),a.d(h,{ArrayItem:()=>Oe,EXT_lights_image_based:()=>we,EXT_mesh_gpu_instancing:()=>Le,EXT_meshopt_compression:()=>Se,EXT_texture_avif:()=>Ie,EXT_texture_webp:()=>Pe,ExtrasAsMetadata:()=>on,GLTFFileLoader:()=>G,GLTFLoader:()=>Ee,KHR_animation_pointer:()=>Vt,KHR_draco_mesh_compression:()=>De,KHR_interactivity:()=>en,KHR_lights:()=>ke,KHR_materials_anisotropy:()=>Xe,KHR_materials_clearcoat:()=>We,KHR_materials_diffuse_transmission:()=>ut,KHR_materials_dispersion:()=>pt,KHR_materials_emissive_strength:()=>Je,KHR_materials_ior:()=>rt,KHR_materials_iridescence:()=>qe,KHR_materials_pbrSpecularGlossiness:()=>Ue,KHR_materials_sheen:()=>$e,KHR_materials_specular:()=>tt,KHR_materials_transmission:()=>lt,KHR_materials_unlit:()=>je,KHR_materials_variants:()=>at,KHR_materials_volume:()=>ht,KHR_mesh_quantization:()=>mt,KHR_node_visibility:()=>nn,KHR_texture_basisu:()=>vt,KHR_texture_transform:()=>gt,KHR_xmp_json_ld:()=>Tt,MSFT_audio_emitter:()=>Gt,MSFT_lod:()=>Ht,MSFT_minecraftMesh:()=>Kt,MSFT_sRGBFactors:()=>Yt,registerGLTFExtension:()=>Ae,registeredGLTFExtensions:()=>ge,unregisterGLTFExtension:()=>Te});var f={};a.r(f),a.d(f,{MTLFileLoader:()=>an,OBJFileLoader:()=>ln,SolidParser:()=>sn});var p={};a.r(p),a.d(p,{STLFileLoader:()=>un});var _={};a.r(_),a.d(_,{GLTF1:()=>c,GLTF2:()=>h,GLTFFileLoader:()=>G,GLTFLoaderAnimationStartMode:()=>E,GLTFLoaderCoordinateSystemMode:()=>O,GLTFLoaderState:()=>M,GLTFValidation:()=>w,MTLFileLoader:()=>an,OBJFileLoader:()=>ln,SPLATFileLoader:()=>pn,STLFileLoader:()=>un,SolidParser:()=>sn});var m=function(e,t){return m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},m(e,t)};function y(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}m(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var v=function(){return v=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},v.apply(this,arguments)};function b(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{l(r.next(e))}catch(e){a(e)}}function s(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))}function g(e,t){var n,r,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(l){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],r=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var A=a(597);function T(e,t,n,r){var o={externalResourceFunction:r};return n&&(o.uri="file:"===t?n:t+n),ArrayBuffer.isView(e)?GLTFValidator.validateBytes(e,o):GLTFValidator.validateString(e,o)}function x(){var e=[];onmessage=function(t){var n=t.data;switch(n.id){case"init":importScripts(n.url);break;case"validate":T(n.data,n.rootUrl,n.fileName,(function(t){return new Promise((function(n,r){var o=e.length;e.push({resolve:n,reject:r}),postMessage({id:"getExternalResource",index:o,uri:t})}))})).then((function(e){postMessage({id:"validate.resolve",value:e})}),(function(e){postMessage({id:"validate.reject",reason:e})}));break;case"getExternalResource.resolve":e[n.index].resolve(n.value);break;case"getExternalResource.reject":e[n.index].reject(n.reason)}}}var O,E,M,w=function(){function e(){}return e.ValidateAsync=function(e,t,n,r){var o=this;return"function"==typeof Worker?new Promise((function(a,i){var s="".concat(T,"(").concat(x,")()"),l=URL.createObjectURL(new Blob([s],{type:"application/javascript"})),c=new Worker(l),u=function(e){c.removeEventListener("error",u),c.removeEventListener("message",d),i(e)},d=function(e){var t=e.data;switch(t.id){case"getExternalResource":r(t.uri).then((function(e){c.postMessage({id:"getExternalResource.resolve",index:t.index,value:e},[e.buffer])}),(function(e){c.postMessage({id:"getExternalResource.reject",index:t.index,reason:e})}));break;case"validate.resolve":c.removeEventListener("error",u),c.removeEventListener("message",d),a(t.value),c.terminate();break;case"validate.reject":c.removeEventListener("error",u),c.removeEventListener("message",d),i(t.reason),c.terminate()}};if(c.addEventListener("error",u),c.addEventListener("message",d),c.postMessage({id:"init",url:A.Tools.GetBabylonScriptURL(o.Configuration.url)}),ArrayBuffer.isView(e)){var h=e.slice();c.postMessage({id:"validate",data:h,rootUrl:t,fileName:n},[h.buffer])}else c.postMessage({id:"validate",data:e,rootUrl:t,fileName:n})})):(this._LoadScriptPromise||(this._LoadScriptPromise=A.Tools.LoadBabylonScriptAsync(this.Configuration.url)),this._LoadScriptPromise.then((function(){return T(e,t,n,r)})))},e.Configuration={url:"".concat(A.Tools._DefaultCdnUrl,"/gltf_validator.js")},e}(),C="Z2xURg",L="gltf",N={".gltf":{isBinary:!1,mimeType:"model/gltf+json"},".glb":{isBinary:!0,mimeType:"model/gltf-binary"}};function S(e,t,n){try{return Promise.resolve(new Uint8Array(e,t,n))}catch(e){return Promise.reject(e)}}!function(e){e[e.AUTO=0]="AUTO",e[e.FORCE_RIGHT_HANDED=1]="FORCE_RIGHT_HANDED"}(O||(O={})),function(e){e[e.NONE=0]="NONE",e[e.FIRST=1]="FIRST",e[e.ALL=2]="ALL"}(E||(E={})),function(e){e[e.LOADING=0]="LOADING",e[e.READY=1]="READY",e[e.COMPLETE=2]="COMPLETE"}(M||(M={}));var R,P,F,I,B,D,V,k,G=function(e){function t(t){var n=e.call(this)||this;return n.onParsedObservable=new A.Observable,n.onMeshLoadedObservable=new A.Observable,n.onSkinLoadedObservable=new A.Observable,n.onTextureLoadedObservable=new A.Observable,n.onMaterialLoadedObservable=new A.Observable,n.onCameraLoadedObservable=new A.Observable,n.onCompleteObservable=new A.Observable,n.onErrorObservable=new A.Observable,n.onDisposeObservable=new A.Observable,n.onExtensionLoadedObservable=new A.Observable,n.validate=!1,n.onValidatedObservable=new A.Observable,n._loader=null,n._state=null,n._requests=new Array,n.name=L,n.extensions=N,n.onLoaderStateChangedObservable=new A.Observable,n._logIndentLevel=0,n._loggingEnabled=!1,n._log=n._logDisabled,n._capturePerformanceCounters=!1,n._startPerformanceCounter=n._startPerformanceCounterDisabled,n._endPerformanceCounter=n._endPerformanceCounterDisabled,n.copyFrom(t),n}return y(t,e),Object.defineProperty(t.prototype,"onParsed",{set:function(e){this._onParsedObserver&&this.onParsedObservable.remove(this._onParsedObserver),e&&(this._onParsedObserver=this.onParsedObservable.add(e))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"onMeshLoaded",{set:function(e){this._onMeshLoadedObserver&&this.onMeshLoadedObservable.remove(this._onMeshLoadedObserver),e&&(this._onMeshLoadedObserver=this.onMeshLoadedObservable.add(e))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"onSkinLoaded",{set:function(e){this._onSkinLoadedObserver&&this.onSkinLoadedObservable.remove(this._onSkinLoadedObserver),e&&(this._onSkinLoadedObserver=this.onSkinLoadedObservable.add((function(t){return e(t.node,t.skinnedNode)})))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"onTextureLoaded",{set:function(e){this._onTextureLoadedObserver&&this.onTextureLoadedObservable.remove(this._onTextureLoadedObserver),e&&(this._onTextureLoadedObserver=this.onTextureLoadedObservable.add(e))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"onMaterialLoaded",{set:function(e){this._onMaterialLoadedObserver&&this.onMaterialLoadedObservable.remove(this._onMaterialLoadedObserver),e&&(this._onMaterialLoadedObserver=this.onMaterialLoadedObservable.add(e))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"onCameraLoaded",{set:function(e){this._onCameraLoadedObserver&&this.onCameraLoadedObservable.remove(this._onCameraLoadedObserver),e&&(this._onCameraLoadedObserver=this.onCameraLoadedObservable.add(e))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"onComplete",{set:function(e){this._onCompleteObserver&&this.onCompleteObservable.remove(this._onCompleteObserver),this._onCompleteObserver=this.onCompleteObservable.add(e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"onError",{set:function(e){this._onErrorObserver&&this.onErrorObservable.remove(this._onErrorObserver),this._onErrorObserver=this.onErrorObservable.add(e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"onDispose",{set:function(e){this._onDisposeObserver&&this.onDisposeObservable.remove(this._onDisposeObserver),this._onDisposeObserver=this.onDisposeObservable.add(e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"onExtensionLoaded",{set:function(e){this._onExtensionLoadedObserver&&this.onExtensionLoadedObservable.remove(this._onExtensionLoadedObserver),this._onExtensionLoadedObserver=this.onExtensionLoadedObservable.add(e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"loggingEnabled",{get:function(){return this._loggingEnabled},set:function(e){this._loggingEnabled!==e&&(this._loggingEnabled=e,this._loggingEnabled?this._log=this._logEnabled:this._log=this._logDisabled)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"capturePerformanceCounters",{get:function(){return this._capturePerformanceCounters},set:function(e){this._capturePerformanceCounters!==e&&(this._capturePerformanceCounters=e,this._capturePerformanceCounters?(this._startPerformanceCounter=this._startPerformanceCounterEnabled,this._endPerformanceCounter=this._endPerformanceCounterEnabled):(this._startPerformanceCounter=this._startPerformanceCounterDisabled,this._endPerformanceCounter=this._endPerformanceCounterDisabled))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"onValidated",{set:function(e){this._onValidatedObserver&&this.onValidatedObservable.remove(this._onValidatedObserver),this._onValidatedObserver=this.onValidatedObservable.add(e)},enumerable:!1,configurable:!0}),t.prototype.dispose=function(){this._loader&&(this._loader.dispose(),this._loader=null);for(var e=0,t=this._requests;e<t.length;e++)t[e].abort();this._requests.length=0,delete this._progressCallback,this.preprocessUrlAsync=function(e){return Promise.resolve(e)},this.onMeshLoadedObservable.clear(),this.onSkinLoadedObservable.clear(),this.onTextureLoadedObservable.clear(),this.onMaterialLoadedObservable.clear(),this.onCameraLoadedObservable.clear(),this.onCompleteObservable.clear(),this.onExtensionLoadedObservable.clear(),this.onDisposeObservable.notifyObservers(void 0),this.onDisposeObservable.clear()},t.prototype.loadFile=function(e,t,n,r,o,a,i,s){var l=this;if(ArrayBuffer.isView(t))return this._loadBinary(e,t,n,r,i,s),null;this._progressCallback=o;var c=t.name||A.Tools.GetFilename(t);if(a){if(this.useRangeRequests){this.validate&&A.Logger.Warn("glTF validation is not supported when range requests are enabled");var u={abort:function(){},onCompleteObservable:new A.Observable},d={readAsync:function(n,r){return new Promise((function(o,a){l._loadFile(e,t,(function(e){o(new Uint8Array(e))}),!0,(function(e){a(e)}),(function(e){e.setRequestHeader("Range","bytes=".concat(n,"-").concat(n+r-1))}))}))},byteLength:0};return this._unpackBinaryAsync(new A.DataReader(d)).then((function(e){u.onCompleteObservable.notifyObservers(u),r(e)}),i?function(e){return i(void 0,e)}:void 0),u}return this._loadFile(e,t,(function(t){l._validate(e,new Uint8Array(t,0,t.byteLength),n,c),l._unpackBinaryAsync(new A.DataReader({readAsync:function(e,n){return S(t,e,n)},byteLength:t.byteLength})).then((function(e){r(e)}),i?function(e){return i(void 0,e)}:void 0)}),!0,i)}return this._loadFile(e,t,(function(t){try{l._validate(e,t,n,c),r({json:l._parseJson(t)})}catch(e){i&&i()}}),!1,i)},t.prototype._loadBinary=function(e,t,n,r,o,a){this._validate(e,new Uint8Array(t.buffer,t.byteOffset,t.byteLength),n,a),this._unpackBinaryAsync(new A.DataReader({readAsync:function(e,n){return function(e,t,n){try{if(t<0||t>=e.byteLength)throw new RangeError("Offset is out of range.");if(t+n>e.byteLength)throw new RangeError("Length is out of range.");return Promise.resolve(new Uint8Array(e.buffer,e.byteOffset+t,n))}catch(e){return Promise.reject(e)}}(t,e,n)},byteLength:t.byteLength})).then((function(e){r(e)}),o?function(e){return o(void 0,e)}:void 0)},t.prototype.importMeshAsync=function(e,t,n,r,o,a){var i=this;return Promise.resolve().then((function(){return i.onParsedObservable.notifyObservers(n),i.onParsedObservable.clear(),i._log("Loading ".concat(a||"")),i._loader=i._getLoader(n),i._loader.importMeshAsync(e,t,null,n,r,o,a)}))},t.prototype.loadAsync=function(e,t,n,r,o){var a=this;return Promise.resolve().then((function(){return a.onParsedObservable.notifyObservers(t),a.onParsedObservable.clear(),a._log("Loading ".concat(o||"")),a._loader=a._getLoader(t),a._loader.loadAsync(e,t,n,r,o)}))},t.prototype.loadAssetContainerAsync=function(e,t,n,r,o){var a=this;return Promise.resolve().then((function(){a.onParsedObservable.notifyObservers(t),a.onParsedObservable.clear(),a._log("Loading ".concat(o||"")),a._loader=a._getLoader(t);var i=new A.AssetContainer(e),s=[];a.onMaterialLoadedObservable.add((function(e){s.push(e)}));var l=[];a.onTextureLoadedObservable.add((function(e){l.push(e)}));var c=[];a.onCameraLoadedObservable.add((function(e){c.push(e)}));var u=[];return a.onMeshLoadedObservable.add((function(e){e.morphTargetManager&&u.push(e.morphTargetManager)})),a._loader.importMeshAsync(null,e,i,t,n,r,o).then((function(e){return Array.prototype.push.apply(i.geometries,e.geometries),Array.prototype.push.apply(i.meshes,e.meshes),Array.prototype.push.apply(i.particleSystems,e.particleSystems),Array.prototype.push.apply(i.skeletons,e.skeletons),Array.prototype.push.apply(i.animationGroups,e.animationGroups),Array.prototype.push.apply(i.materials,s),Array.prototype.push.apply(i.textures,l),Array.prototype.push.apply(i.lights,e.lights),Array.prototype.push.apply(i.transformNodes,e.transformNodes),Array.prototype.push.apply(i.cameras,c),Array.prototype.push.apply(i.morphTargetManagers,u),i}))}))},t.prototype.canDirectLoad=function(e){return function(e){return-1!==e.indexOf("asset")&&-1!==e.indexOf("version")||e.startsWith("data:base64,"+C)||e.startsWith("data:;base64,"+C)||e.startsWith("data:application/octet-stream;base64,"+C)||e.startsWith("data:model/gltf-binary;base64,"+C)}(e)},t.prototype.directLoad=function(e,t){if(t.startsWith("base64,"+C)||t.startsWith(";base64,"+C)||t.startsWith("application/octet-stream;base64,"+C)||t.startsWith("model/gltf-binary;base64,"+C)){var n=(0,A.DecodeBase64UrlToBinary)(t);return this._validate(e,new Uint8Array(n,0,n.byteLength)),this._unpackBinaryAsync(new A.DataReader({readAsync:function(e,t){return S(n,e,t)},byteLength:n.byteLength}))}return this._validate(e,t),Promise.resolve({json:this._parseJson(t)})},t.prototype.createPlugin=function(e){return new t(e[L])},Object.defineProperty(t.prototype,"loaderState",{get:function(){return this._state},enumerable:!1,configurable:!0}),t.prototype.whenCompleteAsync=function(){var e=this;return new Promise((function(t,n){e.onCompleteObservable.addOnce((function(){t()})),e.onErrorObservable.addOnce((function(e){n(e)}))}))},t.prototype._setState=function(e){this._state!==e&&(this._state=e,this.onLoaderStateChangedObservable.notifyObservers(this._state),this._log(M[this._state]))},t.prototype._loadFile=function(e,t,n,r,o,a){var i=this,s=e._loadFile(t,n,(function(e){i._onProgress(e,s)}),!0,r,o,a);return s.onCompleteObservable.add((function(){s._lengthComputable=!0,s._total=s._loaded})),this._requests.push(s),s},t.prototype._onProgress=function(e,t){if(this._progressCallback){t._lengthComputable=e.lengthComputable,t._loaded=e.loaded,t._total=e.total;for(var n=!0,r=0,o=0,a=0,i=this._requests;a<i.length;a++){var s=i[a];if(void 0===s._lengthComputable||void 0===s._loaded||void 0===s._total)return;n=n&&s._lengthComputable,r+=s._loaded,o+=s._total}this._progressCallback({lengthComputable:n,loaded:r,total:n?o:0})}},t.prototype._validate=function(e,t,n,r){var o=this;void 0===n&&(n=""),void 0===r&&(r=""),this.validate&&(this._startPerformanceCounter("Validate JSON"),w.ValidateAsync(t,n,r,(function(t){return o.preprocessUrlAsync(n+t).then((function(t){return e._loadFileAsync(t,void 0,!0,!0).then((function(e){return new Uint8Array(e,0,e.byteLength)}))}))})).then((function(e){o._endPerformanceCounter("Validate JSON"),o.onValidatedObservable.notifyObservers(e),o.onValidatedObservable.clear()}),(function(e){o._endPerformanceCounter("Validate JSON"),A.Tools.Warn("Failed to validate: ".concat(e.message)),o.onValidatedObservable.clear()})))},t.prototype._getLoader=function(e){var n=e.json.asset||{};this._log("Asset version: ".concat(n.version)),n.minVersion&&this._log("Asset minimum version: ".concat(n.minVersion)),n.generator&&this._log("Asset generator: ".concat(n.generator));var r=t._parseVersion(n.version);if(!r)throw new Error("Invalid version: "+n.version);if(void 0!==n.minVersion){var o=t._parseVersion(n.minVersion);if(!o)throw new Error("Invalid minimum version: "+n.minVersion);if(t._compareVersion(o,{major:2,minor:0})>0)throw new Error("Incompatible minimum version: "+n.minVersion)}var a={1:t._CreateGLTF1Loader,2:t._CreateGLTF2Loader}[r.major];if(!a)throw new Error("Unsupported version: "+n.version);return a(this)},t.prototype._parseJson=function(e){this._startPerformanceCounter("Parse JSON"),this._log("JSON length: ".concat(e.length));var t=JSON.parse(e);return this._endPerformanceCounter("Parse JSON"),t},t.prototype._unpackBinaryAsync=function(e){var t=this;return this._startPerformanceCounter("Unpack Binary"),e.loadAsync(20).then((function(){var n=e.readUint32();if(1179937895!==n)throw new A.RuntimeError("Unexpected magic: "+n,A.ErrorCodes.GLTFLoaderUnexpectedMagicError);var r=e.readUint32();t.loggingEnabled&&t._log("Binary version: ".concat(r));var o,a=e.readUint32();switch(t.useRangeRequests||a===e.buffer.byteLength||A.Logger.Warn("Length in header does not match actual data length: ".concat(a," != ").concat(e.buffer.byteLength)),r){case 1:o=t._unpackBinaryV1Async(e,a);break;case 2:o=t._unpackBinaryV2Async(e,a);break;default:throw new Error("Unsupported version: "+r)}return t._endPerformanceCounter("Unpack Binary"),o}))},t.prototype._unpackBinaryV1Async=function(e,t){var n=e.readUint32(),r=e.readUint32();if(0!==r)throw new Error("Unexpected content format: ".concat(r));var o=t-e.byteOffset,a={json:this._parseJson(e.readString(n)),bin:null};if(0!==o){var i=e.byteOffset;a.bin={readAsync:function(t,n){return e.buffer.readAsync(i+t,n)},byteLength:o}}return Promise.resolve(a)},t.prototype._unpackBinaryV2Async=function(e,t){var n=this,r=1313821514,o=e.readUint32();if(e.readUint32()!==r)throw new Error("First chunk format is not JSON");return e.byteOffset+o===t?e.loadAsync(o).then((function(){return{json:n._parseJson(e.readString(o)),bin:null}})):e.loadAsync(o+8).then((function(){var a={json:n._parseJson(e.readString(o)),bin:null},i=function(){var n=e.readUint32();switch(e.readUint32()){case r:throw new Error("Unexpected JSON chunk");case 5130562:var o=e.byteOffset;a.bin={readAsync:function(t,n){return e.buffer.readAsync(o+t,n)},byteLength:n},e.skipBytes(n);break;default:e.skipBytes(n)}return e.byteOffset!==t?e.loadAsync(8).then(i):Promise.resolve(a)};return i()}))},t._parseVersion=function(e){if("1.0"===e||"1.0.1"===e)return{major:1,minor:0};var t=(e+"").match(/^(\d+)\.(\d+)/);return t?{major:parseInt(t[1]),minor:parseInt(t[2])}:null},t._compareVersion=function(e,t){return e.major>t.major?1:e.major<t.major?-1:e.minor>t.minor?1:e.minor<t.minor?-1:0},t.prototype._logOpen=function(e){this._log(e),this._logIndentLevel++},t.prototype._logClose=function(){--this._logIndentLevel},t.prototype._logEnabled=function(e){var n=t._logSpaces.substring(0,2*this._logIndentLevel);A.Logger.Log("".concat(n).concat(e))},t.prototype._logDisabled=function(e){},t.prototype._startPerformanceCounterEnabled=function(e){A.Tools.StartPerformanceCounter(e)},t.prototype._startPerformanceCounterDisabled=function(e){},t.prototype._endPerformanceCounterEnabled=function(e){A.Tools.EndPerformanceCounter(e)},t.prototype._endPerformanceCounterDisabled=function(e){},t.IncrementalLoading=!0,t.HomogeneousCoordinates=!1,t._logSpaces="                                ",t}(function(){function e(){this.coordinateSystemMode=O.AUTO,this.animationStartMode=E.FIRST,this.loadNodeAnimations=!0,this.loadSkins=!0,this.loadMorphTargets=!0,this.compileMaterials=!1,this.useClipPlane=!1,this.compileShadowGenerators=!1,this.transparencyAsCoverage=!1,this.useRangeRequests=!1,this.createInstances=!0,this.alwaysComputeBoundingBox=!1,this.loadAllMaterials=!1,this.loadOnlyMaterials=!1,this.skipMaterials=!1,this.useSRGBBuffers=!0,this.targetFps=60,this.alwaysComputeSkeletonRootNode=!1,this.useGltfTextureNames=!1,this.preprocessUrlAsync=function(e){return Promise.resolve(e)},this.extensionOptions={}}return e.prototype.copyFrom=function(e){var t,n,r,o,a,i,s,l,c,u,d,h,f,p,_,m,y,v,b,g,A;e&&(this.onParsed=e.onParsed,this.coordinateSystemMode=null!==(t=e.coordinateSystemMode)&&void 0!==t?t:this.coordinateSystemMode,this.animationStartMode=null!==(n=e.animationStartMode)&&void 0!==n?n:this.animationStartMode,this.loadNodeAnimations=null!==(r=e.loadNodeAnimations)&&void 0!==r?r:this.loadNodeAnimations,this.loadSkins=null!==(o=e.loadSkins)&&void 0!==o?o:this.loadSkins,this.loadMorphTargets=null!==(a=e.loadMorphTargets)&&void 0!==a?a:this.loadMorphTargets,this.compileMaterials=null!==(i=e.compileMaterials)&&void 0!==i?i:this.compileMaterials,this.useClipPlane=null!==(s=e.useClipPlane)&&void 0!==s?s:this.useClipPlane,this.compileShadowGenerators=null!==(l=e.compileShadowGenerators)&&void 0!==l?l:this.compileShadowGenerators,this.transparencyAsCoverage=null!==(c=e.transparencyAsCoverage)&&void 0!==c?c:this.transparencyAsCoverage,this.useRangeRequests=null!==(u=e.useRangeRequests)&&void 0!==u?u:this.useRangeRequests,this.createInstances=null!==(d=e.createInstances)&&void 0!==d?d:this.createInstances,this.alwaysComputeBoundingBox=null!==(h=e.alwaysComputeBoundingBox)&&void 0!==h?h:this.alwaysComputeBoundingBox,this.loadAllMaterials=null!==(f=e.loadAllMaterials)&&void 0!==f?f:this.loadAllMaterials,this.loadOnlyMaterials=null!==(p=e.loadOnlyMaterials)&&void 0!==p?p:this.loadOnlyMaterials,this.skipMaterials=null!==(_=e.skipMaterials)&&void 0!==_?_:this.skipMaterials,this.useSRGBBuffers=null!==(m=e.useSRGBBuffers)&&void 0!==m?m:this.useSRGBBuffers,this.targetFps=null!==(y=e.targetFps)&&void 0!==y?y:this.targetFps,this.alwaysComputeSkeletonRootNode=null!==(v=e.alwaysComputeSkeletonRootNode)&&void 0!==v?v:this.alwaysComputeSkeletonRootNode,this.useGltfTextureNames=null!==(b=e.useGltfTextureNames)&&void 0!==b?b:this.useGltfTextureNames,this.preprocessUrlAsync=null!==(g=e.preprocessUrlAsync)&&void 0!==g?g:this.preprocessUrlAsync,this.customRootNode=e.customRootNode,this.onMeshLoaded=e.onMeshLoaded,this.onSkinLoaded=e.onSkinLoaded,this.onTextureLoaded=e.onTextureLoaded,this.onMaterialLoaded=e.onMaterialLoaded,this.onCameraLoaded=e.onCameraLoaded,this.extensionOptions=null!==(A=e.extensionOptions)&&void 0!==A?A:this.extensionOptions)},e}());(0,A.registerSceneLoaderPlugin)(new G),function(e){e[e.BYTE=5120]="BYTE",e[e.UNSIGNED_BYTE=5121]="UNSIGNED_BYTE",e[e.SHORT=5122]="SHORT",e[e.UNSIGNED_SHORT=5123]="UNSIGNED_SHORT",e[e.FLOAT=5126]="FLOAT"}(R||(R={})),function(e){e[e.FRAGMENT=35632]="FRAGMENT",e[e.VERTEX=35633]="VERTEX"}(P||(P={})),function(e){e[e.BYTE=5120]="BYTE",e[e.UNSIGNED_BYTE=5121]="UNSIGNED_BYTE",e[e.SHORT=5122]="SHORT",e[e.UNSIGNED_SHORT=5123]="UNSIGNED_SHORT",e[e.INT=5124]="INT",e[e.UNSIGNED_INT=5125]="UNSIGNED_INT",e[e.FLOAT=5126]="FLOAT",e[e.FLOAT_VEC2=35664]="FLOAT_VEC2",e[e.FLOAT_VEC3=35665]="FLOAT_VEC3",e[e.FLOAT_VEC4=35666]="FLOAT_VEC4",e[e.INT_VEC2=35667]="INT_VEC2",e[e.INT_VEC3=35668]="INT_VEC3",e[e.INT_VEC4=35669]="INT_VEC4",e[e.BOOL=35670]="BOOL",e[e.BOOL_VEC2=35671]="BOOL_VEC2",e[e.BOOL_VEC3=35672]="BOOL_VEC3",e[e.BOOL_VEC4=35673]="BOOL_VEC4",e[e.FLOAT_MAT2=35674]="FLOAT_MAT2",e[e.FLOAT_MAT3=35675]="FLOAT_MAT3",e[e.FLOAT_MAT4=35676]="FLOAT_MAT4",e[e.SAMPLER_2D=35678]="SAMPLER_2D"}(F||(F={})),function(e){e[e.CLAMP_TO_EDGE=33071]="CLAMP_TO_EDGE",e[e.MIRRORED_REPEAT=33648]="MIRRORED_REPEAT",e[e.REPEAT=10497]="REPEAT"}(I||(I={})),function(e){e[e.NEAREST=9728]="NEAREST",e[e.LINEAR=9728]="LINEAR",e[e.NEAREST_MIPMAP_NEAREST=9984]="NEAREST_MIPMAP_NEAREST",e[e.LINEAR_MIPMAP_NEAREST=9985]="LINEAR_MIPMAP_NEAREST",e[e.NEAREST_MIPMAP_LINEAR=9986]="NEAREST_MIPMAP_LINEAR",e[e.LINEAR_MIPMAP_LINEAR=9987]="LINEAR_MIPMAP_LINEAR"}(B||(B={})),function(e){e[e.ALPHA=6406]="ALPHA",e[e.RGB=6407]="RGB",e[e.RGBA=6408]="RGBA",e[e.LUMINANCE=6409]="LUMINANCE",e[e.LUMINANCE_ALPHA=6410]="LUMINANCE_ALPHA"}(D||(D={})),function(e){e[e.FRONT=1028]="FRONT",e[e.BACK=1029]="BACK",e[e.FRONT_AND_BACK=1032]="FRONT_AND_BACK"}(V||(V={})),function(e){e[e.ZERO=0]="ZERO",e[e.ONE=1]="ONE",e[e.SRC_COLOR=768]="SRC_COLOR",e[e.ONE_MINUS_SRC_COLOR=769]="ONE_MINUS_SRC_COLOR",e[e.DST_COLOR=774]="DST_COLOR",e[e.ONE_MINUS_DST_COLOR=775]="ONE_MINUS_DST_COLOR",e[e.SRC_ALPHA=770]="SRC_ALPHA",e[e.ONE_MINUS_SRC_ALPHA=771]="ONE_MINUS_SRC_ALPHA",e[e.DST_ALPHA=772]="DST_ALPHA",e[e.ONE_MINUS_DST_ALPHA=773]="ONE_MINUS_DST_ALPHA",e[e.CONSTANT_COLOR=32769]="CONSTANT_COLOR",e[e.ONE_MINUS_CONSTANT_COLOR=32770]="ONE_MINUS_CONSTANT_COLOR",e[e.CONSTANT_ALPHA=32771]="CONSTANT_ALPHA",e[e.ONE_MINUS_CONSTANT_ALPHA=32772]="ONE_MINUS_CONSTANT_ALPHA",e[e.SRC_ALPHA_SATURATE=776]="SRC_ALPHA_SATURATE"}(k||(k={}));var U,H=function(){function e(){}return e.SetMatrix=function(e,t,n,r,o){var a=null;if("MODEL"===n.semantic?a=t.getWorldMatrix():"PROJECTION"===n.semantic?a=e.getProjectionMatrix():"VIEW"===n.semantic?a=e.getViewMatrix():"MODELVIEWINVERSETRANSPOSE"===n.semantic?a=A.Matrix.Transpose(t.getWorldMatrix().multiply(e.getViewMatrix()).invert()):"MODELVIEW"===n.semantic?a=t.getWorldMatrix().multiply(e.getViewMatrix()):"MODELVIEWPROJECTION"===n.semantic?a=t.getWorldMatrix().multiply(e.getTransformMatrix()):"MODELINVERSE"===n.semantic?a=t.getWorldMatrix().invert():"VIEWINVERSE"===n.semantic?a=e.getViewMatrix().invert():"PROJECTIONINVERSE"===n.semantic?a=e.getProjectionMatrix().invert():"MODELVIEWINVERSE"===n.semantic?a=t.getWorldMatrix().multiply(e.getViewMatrix()).invert():"MODELVIEWPROJECTIONINVERSE"===n.semantic?a=t.getWorldMatrix().multiply(e.getTransformMatrix()).invert():"MODELINVERSETRANSPOSE"===n.semantic&&(a=A.Matrix.Transpose(t.getWorldMatrix().invert())),a)switch(n.type){case F.FLOAT_MAT2:o.setMatrix2x2(r,A.Matrix.GetAsMatrix2x2(a));break;case F.FLOAT_MAT3:o.setMatrix3x3(r,A.Matrix.GetAsMatrix3x3(a));break;case F.FLOAT_MAT4:o.setMatrix(r,a)}},e.SetUniform=function(e,t,n,r){switch(r){case F.FLOAT:return e.setFloat(t,n),!0;case F.FLOAT_VEC2:return e.setVector2(t,A.Vector2.FromArray(n)),!0;case F.FLOAT_VEC3:return e.setVector3(t,A.Vector3.FromArray(n)),!0;case F.FLOAT_VEC4:return e.setVector4(t,A.Vector4.FromArray(n)),!0;default:return!1}},e.GetWrapMode=function(e){switch(e){case I.CLAMP_TO_EDGE:return A.Texture.CLAMP_ADDRESSMODE;case I.MIRRORED_REPEAT:return A.Texture.MIRROR_ADDRESSMODE;case I.REPEAT:default:return A.Texture.WRAP_ADDRESSMODE}},e.GetByteStrideFromType=function(e){switch(e.type){case"VEC2":return 2;case"VEC3":return 3;case"VEC4":case"MAT2":return 4;case"MAT3":return 9;case"MAT4":return 16;default:return 1}},e.GetTextureFilterMode=function(e){switch(e){case B.LINEAR:case B.LINEAR_MIPMAP_NEAREST:case B.LINEAR_MIPMAP_LINEAR:return A.Texture.TRILINEAR_SAMPLINGMODE;case B.NEAREST:case B.NEAREST_MIPMAP_NEAREST:return A.Texture.NEAREST_SAMPLINGMODE;default:return A.Texture.BILINEAR_SAMPLINGMODE}},e.GetBufferFromBufferView=function(e,t,n,r,o){n=t.byteOffset+n;var a=e.loadedBufferViews[t.buffer];if(n+r>a.byteLength)throw new Error("Buffer access is out of range");var i=a.buffer;switch(n+=a.byteOffset,o){case R.BYTE:return new Int8Array(i,n,r);case R.UNSIGNED_BYTE:return new Uint8Array(i,n,r);case R.SHORT:return new Int16Array(i,n,r);case R.UNSIGNED_SHORT:return new Uint16Array(i,n,r);default:return new Float32Array(i,n,r)}},e.GetBufferFromAccessor=function(t,n){var r=t.bufferViews[n.bufferView],o=n.count*e.GetByteStrideFromType(n);return e.GetBufferFromBufferView(t,r,n.byteOffset,o,n.componentType)},e.DecodeBufferToText=function(e){for(var t="",n=e.byteLength,r=0;r<n;++r)t+=String.fromCharCode(e[r]);return t},e.GetDefaultMaterial=function(t){if(!e._DefaultMaterial){A.Effect.ShadersStore.GLTFDefaultMaterialVertexShader=["precision highp float;","","uniform mat4 worldView;","uniform mat4 projection;","","attribute vec3 position;","","void main(void)","{","    gl_Position = projection * worldView * vec4(position, 1.0);","}"].join("\n"),A.Effect.ShadersStore.GLTFDefaultMaterialPixelShader=["precision highp float;","","uniform vec4 u_emission;","","void main(void)","{","    gl_FragColor = u_emission;","}"].join("\n");var n={attributes:["position"],uniforms:["worldView","projection","u_emission"],samplers:new Array,needAlphaBlending:!1};e._DefaultMaterial=new A.ShaderMaterial("GLTFDefaultMaterial",t,{vertex:"GLTFDefaultMaterial",fragment:"GLTFDefaultMaterial"},n),e._DefaultMaterial.setColor4("u_emission",new A.Color4(.5,.5,.5,1))}return e._DefaultMaterial},e._DefaultMaterial=null,e}();!function(e){e[e.IDENTIFIER=1]="IDENTIFIER",e[e.UNKNOWN=2]="UNKNOWN",e[e.END_OF_INPUT=3]="END_OF_INPUT"}(U||(U={}));var j=function(){function e(e){this._pos=0,this.currentToken=U.UNKNOWN,this.currentIdentifier="",this.currentString="",this.isLetterOrDigitPattern=/^[a-zA-Z0-9]+$/,this._toParse=e,this._maxPos=e.length}return e.prototype.getNextToken=function(){if(this.isEnd())return U.END_OF_INPUT;if(this.currentString=this.read(),this.currentToken=U.UNKNOWN,"_"===this.currentString||this.isLetterOrDigitPattern.test(this.currentString))for(this.currentToken=U.IDENTIFIER,this.currentIdentifier=this.currentString;!this.isEnd()&&(this.isLetterOrDigitPattern.test(this.currentString=this.peek())||"_"===this.currentString);)this.currentIdentifier+=this.currentString,this.forward();return this.currentToken},e.prototype.peek=function(){return this._toParse[this._pos]},e.prototype.read=function(){return this._toParse[this._pos++]},e.prototype.forward=function(){this._pos++},e.prototype.isEnd=function(){return this._pos>=this._maxPos},e}(),K=["MODEL","VIEW","PROJECTION","MODELVIEW","MODELVIEWPROJECTION","JOINTMATRIX"],W=["world","view","projection","worldView","worldViewProjection","mBones"],Y=["translation","rotation","scale"],q=["position","rotationQuaternion","scaling"],z=function(e,t,n){for(var r in e){var o=e[r];n[t][r]=o}},X=function(e){if(e)for(var t=0;t<e.length/2;t++)e[2*t+1]=1-e[2*t+1]},Z=function(e){if("NORMAL"===e.semantic)return"normal";if("POSITION"===e.semantic)return"position";if("JOINT"===e.semantic)return"matricesIndices";if("WEIGHT"===e.semantic)return"matricesWeights";if("COLOR"===e.semantic)return"color";if(e.semantic&&-1!==e.semantic.indexOf("TEXCOORD_")){var t=Number(e.semantic.split("_")[1]);return"uv"+(0===t?"":t+1)}return null},J=function(e){var t=null;if(e.translation||e.rotation||e.scale){var n=A.Vector3.FromArray(e.scale||[1,1,1]),r=A.Quaternion.FromArray(e.rotation||[0,0,0,1]),o=A.Vector3.FromArray(e.translation||[0,0,0]);t=A.Matrix.Compose(n,r,o)}else t=A.Matrix.FromArray(e.matrix);return t},Q=function(e,t,n,r){for(var o=0;o<r.bones.length;o++)if(r.bones[o].name===n)return r.bones[o];var a=e.nodes;for(var i in a){var s=a[i];if(s.jointName){var l=s.children;for(o=0;o<l.length;o++){var c=e.nodes[l[o]];if(c.jointName&&c.jointName===n){var u=J(s),d=new A.Bone(s.name||"",r,Q(e,t,s.jointName,r),u);return d.id=i,d}}}}return null},$=function(e,t){for(var n=0;n<e.length;n++)for(var r=e[n],o=0;o<r.node.children.length;o++)if(r.node.children[o]===t)return r.bone;return null},ee=function(e,t){var n=e.nodes,r=n[t];if(r)return{node:r,id:t};for(var o in n)if((r=n[o]).jointName===t)return{node:r,id:o};return null},te=function(e,t){for(var n=0;n<e.jointNames.length;n++)if(e.jointNames[n]===t)return!0;return!1},ne=function(e,t,n,r,o){if(o||(e.scene._blockEntityCollection=!!e.assetContainer,(o=new A.Mesh(t.name||"",e.scene))._parentContainer=e.assetContainer,e.scene._blockEntityCollection=!1,o.id=r),!t.babylonNode)return o;for(var a,i=[],s=null,l=[],c=[],u=[],d=[],h=0;h<n.length;h++){var f=n[h];if(L=e.meshes[f])for(var p=0;p<L.primitives.length;p++){var _=new A.VertexData,m=L.primitives[p];m.mode;var y=m.attributes,v=null,b=null;for(var g in y)if(v=e.accessors[y[g]],b=H.GetBufferFromAccessor(e,v),"NORMAL"===g)_.normals=new Float32Array(b.length),_.normals.set(b);else if("POSITION"===g){if(G.HomogeneousCoordinates){_.positions=new Float32Array(b.length-b.length/4);for(var T=0;T<b.length;T+=4)_.positions[T]=b[T],_.positions[T+1]=b[T+1],_.positions[T+2]=b[T+2]}else _.positions=new Float32Array(b.length),_.positions.set(b);c.push(_.positions.length)}else if(-1!==g.indexOf("TEXCOORD_")){var x=Number(g.split("_")[1]),O=A.VertexBuffer.UVKind+(0===x?"":x+1),E=new Float32Array(b.length);E.set(b),X(E),_.set(E,O)}else"JOINT"===g?(_.matricesIndices=new Float32Array(b.length),_.matricesIndices.set(b)):"WEIGHT"===g?(_.matricesWeights=new Float32Array(b.length),_.matricesWeights.set(b)):"COLOR"===g&&(_.colors=new Float32Array(b.length),_.colors.set(b));if(v=e.accessors[m.indices])b=H.GetBufferFromAccessor(e,v),_.indices=new Int32Array(b.length),_.indices.set(b),d.push(_.indices.length);else{var M=[];for(T=0;T<_.positions.length/3;T++)M.push(T);_.indices=new Int32Array(M),d.push(_.indices.length)}s?s.merge(_):s=_;var w=e.scene.getMaterialById(m.material);i.push(null===w?H.GetDefaultMaterial(e.scene):w),l.push(0===l.length?0:l[l.length-1]+c[c.length-2]),u.push(0===u.length?0:u[u.length-1]+d[d.length-2])}}e.scene._blockEntityCollection=!!e.assetContainer,i.length>1?(a=new A.MultiMaterial("multimat"+r,e.scene)).subMaterials=i:a=new A.StandardMaterial("multimat"+r,e.scene),1===i.length&&(a=i[0]),a._parentContainer=e.assetContainer,o.material||(o.material=a),new A.Geometry(r,e.scene,s,!1,o),o.computeWorldMatrix(!0),e.scene._blockEntityCollection=!1,o.subMeshes=[];var C=0;for(h=0;h<n.length;h++){var L;if(f=n[h],L=e.meshes[f])for(p=0;p<L.primitives.length;p++)L.primitives[p].mode,A.SubMesh.AddToMesh(C,l[C],c[C],u[C],d[C],o,o,!0),C++}return o},re=function(e,t,n,r){e.position&&(e.position=t),(e.rotationQuaternion||e.rotation)&&(e.rotationQuaternion=n),e.scaling&&(e.scaling=r)},oe=function(e,t,n){var r=null;if(e.importOnlyMeshes&&(t.skin||t.meshes)&&e.importMeshesNames&&e.importMeshesNames.length>0&&-1===e.importMeshesNames.indexOf(t.name||""))return null;if(t.skin){if(t.meshes){var o=e.skins[t.skin];(a=ne(e,t,t.meshes,n,t.babylonNode)).skeleton=e.scene.getLastSkeletonById(t.skin),null===a.skeleton&&(a.skeleton=function(e,t,n,r){if(r||(r=new A.Skeleton(t.name||"","",e.scene)),!t.babylonSkeleton)return r;var o=[],a=[];!function(e,t,n,r){for(var o in e.nodes){var a=e.nodes[o],i=o;if(a.jointName&&!te(n,a.jointName)){var s=J(a),l=new A.Bone(a.name||"",t,null,s);l.id=i,r.push({bone:l,node:a,id:i})}}for(var c=0;c<r.length;c++)for(var u=r[c],d=u.node.children,h=0;h<d.length;h++){for(var f=null,p=0;p<r.length;p++)if(r[p].id===d[h]){f=r[p];break}f&&(f.bone._parent=u.bone,u.bone.children.push(f.bone))}}(e,r,t,o),r.bones=[];for(var i=0;i<t.jointNames.length;i++)if(b=ee(e,t.jointNames[i])){var s=b.node;if(s){var l=b.id,c=e.scene.getBoneById(l);if(c)r.bones.push(c);else{for(var u=!1,d=null,h=0;h<i;h++){var f=ee(e,t.jointNames[h]);if(f){var p=f.node;if(p){var _=p.children;if(_){u=!1;for(var m=0;m<_.length;m++)if(_[m]===l){d=Q(e,t,t.jointNames[h],r),u=!0;break}if(u)break}}else A.Tools.Warn("Joint named "+t.jointNames[h]+" does not exist when looking for parent")}}var y=J(s);!d&&o.length>0&&(d=$(o,l))&&-1===a.indexOf(d)&&a.push(d),new A.Bone(s.jointName||"",r,d,y).id=l}}else A.Tools.Warn("Joint named "+t.jointNames[i]+" does not exist")}var v=r.bones;for(r.bones=[],i=0;i<t.jointNames.length;i++){var b;if(b=ee(e,t.jointNames[i]))for(h=0;h<v.length;h++)if(v[h].id===b.id){r.bones.push(v[h]);break}}for(r.prepare(),i=0;i<a.length;i++)r.bones.push(a[i]);return r}(e,o,0,o.babylonSkeleton),o.babylonSkeleton||(o.babylonSkeleton=a.skeleton)),r=a}}else if(t.meshes){var a;r=a=ne(e,t,t.mesh?[t.mesh]:t.meshes,n,t.babylonNode)}else if(!t.light||t.babylonNode||e.importOnlyMeshes){if(t.camera&&!t.babylonNode&&!e.importOnlyMeshes){var i=e.cameras[t.camera];if(i){if(e.scene._blockEntityCollection=!!e.assetContainer,"orthographic"===i.type){var s=new A.FreeCamera(t.camera,A.Vector3.Zero(),e.scene,!1);s.name=t.name||"",s.mode=A.Camera.ORTHOGRAPHIC_CAMERA,s.attachControl(),r=s,s._parentContainer=e.assetContainer}else if("perspective"===i.type){var l=i[i.type],c=new A.FreeCamera(t.camera,A.Vector3.Zero(),e.scene,!1);c.name=t.name||"",c.attachControl(),l.aspectRatio||(l.aspectRatio=e.scene.getEngine().getRenderWidth()/e.scene.getEngine().getRenderHeight()),l.znear&&l.zfar&&(c.maxZ=l.zfar,c.minZ=l.znear),r=c,c._parentContainer=e.assetContainer}e.scene._blockEntityCollection=!1}}}else{var u=e.lights[t.light];if(u)if("ambient"===u.type){var d=u[u.type],h=new A.HemisphericLight(t.light,A.Vector3.Zero(),e.scene);h.name=t.name||"",d.color&&(h.diffuse=A.Color3.FromArray(d.color)),r=h}else if("directional"===u.type){var f=u[u.type],p=new A.DirectionalLight(t.light,A.Vector3.Zero(),e.scene);p.name=t.name||"",f.color&&(p.diffuse=A.Color3.FromArray(f.color)),r=p}else if("point"===u.type){var _=u[u.type],m=new A.PointLight(t.light,A.Vector3.Zero(),e.scene);m.name=t.name||"",_.color&&(m.diffuse=A.Color3.FromArray(_.color)),r=m}else if("spot"===u.type){var y=u[u.type],v=new A.SpotLight(t.light,A.Vector3.Zero(),A.Vector3.Zero(),0,0,e.scene);v.name=t.name||"",y.color&&(v.diffuse=A.Color3.FromArray(y.color)),y.fallOfAngle&&(v.angle=y.fallOfAngle),y.fallOffExponent&&(v.exponent=y.fallOffExponent),r=v}}if(!t.jointName){if(t.babylonNode)return t.babylonNode;if(null===r){e.scene._blockEntityCollection=!!e.assetContainer;var b=new A.Mesh(t.name||"",e.scene);b._parentContainer=e.assetContainer,e.scene._blockEntityCollection=!1,t.babylonNode=b,r=b}}if(null!==r){if(t.matrix&&r instanceof A.Mesh)!function(e,t){if(t.matrix){var n=new A.Vector3(0,0,0),r=new A.Quaternion,o=new A.Vector3(0,0,0);A.Matrix.FromArray(t.matrix).decompose(o,r,n),re(e,n,r,o)}else t.translation&&t.rotation&&t.scale&&re(e,A.Vector3.FromArray(t.translation),A.Quaternion.FromArray(t.rotation),A.Vector3.FromArray(t.scale));e.computeWorldMatrix(!0)}(r,t);else{var g=t.translation||[0,0,0],T=t.rotation||[0,0,0,1],x=t.scale||[1,1,1];re(r,A.Vector3.FromArray(g),A.Quaternion.FromArray(T),A.Vector3.FromArray(x))}r.updateCache(!0),t.babylonNode=r}return r},ae=function(e,t,n,r){void 0===r&&(r=!1);var o=e.nodes[t],a=null;if(r=!(e.importOnlyMeshes&&!r&&e.importMeshesNames)||-1!==e.importMeshesNames.indexOf(o.name||"")||0===e.importMeshesNames.length,!o.jointName&&r&&null!==(a=oe(e,o,t))&&(a.id=t,a.parent=n),o.children)for(var i=0;i<o.children.length;i++)ae(e,o.children[i],a,r)},ie=function(e){var t=e.currentScene;if(t)for(var n=0;n<t.nodes.length;n++)ae(e,t.nodes[n],null);else for(var r in e.scenes)for(t=e.scenes[r],n=0;n<t.nodes.length;n++)ae(e,t.nodes[n],null);for(function(e){for(var t in e.animations){var n=e.animations[t];if(n.channels&&n.samplers)for(var r=null,o=0;o<n.channels.length;o++){var a=n.channels[o],i=n.samplers[a.sampler];if(i){var s=null,l=null;n.parameters?(s=n.parameters[i.input],l=n.parameters[i.output]):(s=i.input,l=i.output);var c=H.GetBufferFromAccessor(e,e.accessors[s]),u=H.GetBufferFromAccessor(e,e.accessors[l]),d=a.target.id,h=e.scene.getNodeById(d);if(null===h&&(h=e.scene.getNodeByName(d)),null!==h){var f=h instanceof A.Bone,p=a.target.path,_=Y.indexOf(p);-1!==_&&(p=q[_]);var m=A.Animation.ANIMATIONTYPE_MATRIX;f||("rotationQuaternion"===p?(m=A.Animation.ANIMATIONTYPE_QUATERNION,h.rotationQuaternion=new A.Quaternion):m=A.Animation.ANIMATIONTYPE_VECTOR3);var y=null,v=[],b=0,g=!1;f&&r&&r.getKeys().length===c.length&&(y=r,g=!0),g||(e.scene._blockEntityCollection=!!e.assetContainer,y=new A.Animation(t,f?"_matrix":p,1,m,A.Animation.ANIMATIONLOOPMODE_CYCLE),e.scene._blockEntityCollection=!1);for(var T=0;T<c.length;T++){var x=null;if("rotationQuaternion"===p?(x=A.Quaternion.FromArray([u[b],u[b+1],u[b+2],u[b+3]]),b+=4):(x=A.Vector3.FromArray([u[b],u[b+1],u[b+2]]),b+=3),f){var O=h,E=A.Vector3.Zero(),M=new A.Quaternion,w=A.Vector3.Zero(),C=O.getBaseMatrix();g&&r&&(C=r.getKeys()[T].value),C.decompose(w,M,E),"position"===p?E=x:"rotationQuaternion"===p?M=x:w=x,x=A.Matrix.Compose(w,M,E)}g?r&&(r.getKeys()[T].value=x):v.push({frame:c[T],value:x})}!g&&y&&(y.setKeys(v),h.animations.push(y)),r=y,e.scene.stopAnimation(h),e.scene.beginAnimation(h,0,c[c.length-1],!0,1)}else A.Tools.Warn("Creating animation named "+t+". But cannot find node named "+d+" to attach to")}}}}(e),n=0;n<e.scene.skeletons.length;n++){var o=e.scene.skeletons[n];e.scene.beginAnimation(o,0,Number.MAX_VALUE,!0,1)}},se=function(e,t,n){for(var r in t.uniforms){var o=t.uniforms[r],a=t.parameters[o];if(e.currentIdentifier===r&&a.semantic&&!a.source&&!a.node){var i=K.indexOf(a.semantic);if(-1!==i)return delete n[r],W[i]}}return e.currentIdentifier},le=function(e){for(var t in e.materials)de.LoadMaterialAsync(e,t,(function(){}),(function(){}))},ce=function(){function e(){}return e.CreateRuntime=function(e,t,n){var r={extensions:{},accessors:{},buffers:{},bufferViews:{},meshes:{},lights:{},cameras:{},nodes:{},images:{},textures:{},shaders:{},programs:{},samplers:{},techniques:{},materials:{},animations:{},skins:{},extensionsUsed:[],scenes:{},buffersCount:0,shaderscount:0,scene:t,rootUrl:n,loadedBufferCount:0,loadedBufferViews:{},loadedShaderCount:0,importOnlyMeshes:!1,dummyNodes:[],assetContainer:null};return e.extensions&&z(e.extensions,"extensions",r),e.extensionsUsed&&z(e.extensionsUsed,"extensionsUsed",r),e.buffers&&function(e,t){for(var n in e){var r=e[n];t.buffers[n]=r,t.buffersCount++}}(e.buffers,r),e.bufferViews&&z(e.bufferViews,"bufferViews",r),e.accessors&&z(e.accessors,"accessors",r),e.meshes&&z(e.meshes,"meshes",r),e.lights&&z(e.lights,"lights",r),e.cameras&&z(e.cameras,"cameras",r),e.nodes&&z(e.nodes,"nodes",r),e.images&&z(e.images,"images",r),e.textures&&z(e.textures,"textures",r),e.shaders&&function(e,t){for(var n in e){var r=e[n];t.shaders[n]=r,t.shaderscount++}}(e.shaders,r),e.programs&&z(e.programs,"programs",r),e.samplers&&z(e.samplers,"samplers",r),e.techniques&&z(e.techniques,"techniques",r),e.materials&&z(e.materials,"materials",r),e.animations&&z(e.animations,"animations",r),e.skins&&z(e.skins,"skins",r),e.scenes&&(r.scenes=e.scenes),e.scene&&e.scenes&&(r.currentScene=e.scenes[e.scene]),r},e.LoadBufferAsync=function(e,t,n,r,o){var a=e.buffers[t];A.Tools.IsBase64(a.uri)?setTimeout((function(){return n(new Uint8Array(A.Tools.DecodeBase64(a.uri)))})):A.Tools.LoadFile(e.rootUrl+a.uri,(function(e){return n(new Uint8Array(e))}),o,void 0,!0,(function(e){e&&r(e.status+" "+e.statusText)}))},e.LoadTextureBufferAsync=function(e,t,n,r){var o=e.textures[t];if(o&&o.source)if(o.babylonTexture)n(null);else{var a=e.images[o.source];A.Tools.IsBase64(a.uri)?setTimeout((function(){return n(new Uint8Array(A.Tools.DecodeBase64(a.uri)))})):A.Tools.LoadFile(e.rootUrl+a.uri,(function(e){return n(new Uint8Array(e))}),void 0,void 0,!0,(function(e){e&&r(e.status+" "+e.statusText)}))}else r("")},e.CreateTextureAsync=function(e,t,n,r){var o=e.textures[t];if(o.babylonTexture)r(o.babylonTexture);else{var a=e.samplers[o.sampler],i=a.minFilter===B.NEAREST_MIPMAP_NEAREST||a.minFilter===B.NEAREST_MIPMAP_LINEAR||a.minFilter===B.LINEAR_MIPMAP_NEAREST||a.minFilter===B.LINEAR_MIPMAP_LINEAR,s=A.Texture.BILINEAR_SAMPLINGMODE,l=null==n?new Blob:new Blob([n]),c=URL.createObjectURL(l),u=function(){return URL.revokeObjectURL(c)},d=new A.Texture(c,e.scene,!i,!0,s,u,u);void 0!==a.wrapS&&(d.wrapU=H.GetWrapMode(a.wrapS)),void 0!==a.wrapT&&(d.wrapV=H.GetWrapMode(a.wrapT)),d.name=t,o.babylonTexture=d,r(d)}},e.LoadShaderStringAsync=function(e,t,n,r){var o=e.shaders[t];if(A.Tools.IsBase64(o.uri)){var a=atob(o.uri.split(",")[1]);n&&n(a)}else A.Tools.LoadFile(e.rootUrl+o.uri,n,void 0,void 0,!1,(function(e){e&&r&&r(e.status+" "+e.statusText)}))},e.LoadMaterialAsync=function(e,t,n,r){var o=e.materials[t];if(o.technique){var a=e.techniques[o.technique];if(!a){e.scene._blockEntityCollection=!!e.assetContainer;var i=new A.StandardMaterial(t,e.scene);return i._parentContainer=e.assetContainer,e.scene._blockEntityCollection=!1,i.diffuseColor=new A.Color3(.5,.5,.5),i.sideOrientation=A.Material.CounterClockWiseSideOrientation,void n(i)}var s=e.programs[a.program],l=a.states,c=A.Effect.ShadersStore[s.vertexShader+"VertexShader"],u=A.Effect.ShadersStore[s.fragmentShader+"PixelShader"],d="",h="",f=new j(c),p=new j(u),_={},m=[],y=[],v=[];for(var b in a.uniforms){var g=a.uniforms[b],T=a.parameters[g];if(_[b]=T,!T.semantic||T.node||T.source)T.type===F.SAMPLER_2D?v.push(b):m.push(b);else{var x=K.indexOf(T.semantic);-1!==x?(m.push(W[x]),delete _[b]):m.push(b)}}for(var O in a.attributes){var E=a.attributes[O];if((C=a.parameters[E]).semantic){var M=Z(C);M&&y.push(M)}}for(;!f.isEnd()&&f.getNextToken();)if(f.currentToken===U.IDENTIFIER){var w=!1;for(var O in a.attributes){E=a.attributes[O];var C=a.parameters[E];if(f.currentIdentifier===O&&C.semantic){d+=Z(C),w=!0;break}}w||(d+=se(f,a,_))}else d+=f.currentString;for(;!p.isEnd()&&p.getNextToken();)p.currentToken===U.IDENTIFIER?h+=se(p,a,_):h+=p.currentString;var L={vertex:s.vertexShader+t,fragment:s.fragmentShader+t},N={attributes:y,uniforms:m,samplers:v,needAlphaBlending:l&&l.enable&&-1!==l.enable.indexOf(3042)};A.Effect.ShadersStore[s.vertexShader+t+"VertexShader"]=d,A.Effect.ShadersStore[s.fragmentShader+t+"PixelShader"]=h;var S=new A.ShaderMaterial(t,e.scene,L,N);if(S.onError=function(e,t,n){return function(r,o){t.dispose(!0),n("Cannot compile program named "+e.name+". Error: "+o+". Default material will be applied")}}(s,S,r),S.onCompiled=function(e,t,n,r,o,a){return function(i){!function(e,t,n,r,o){var a=r.values||n.parameters,i=n.uniforms,s=function(n){var s=o[n],l=s.type,c=a[i[n]];if(void 0===c&&(c=s.value),!c)return"continue";var u=function(e){return function(n){s.value&&e&&(t.setTexture(e,n),delete o[e])}};l===F.SAMPLER_2D?de.LoadTextureAsync(e,r.values?c:s.value,u(n),(function(){return u(null)})):s.value&&H.SetUniform(t,n,r.values?c:s.value,l)&&delete o[n]};for(var l in o)s(l)}(e,t,n,r,o),t.onBind=function(i){!function(e,t,n,r,o,a,i){var s=a.values||o.parameters;for(var l in n){var c=n[l],u=c.type;if(u===F.FLOAT_MAT2||u===F.FLOAT_MAT3||u===F.FLOAT_MAT4)if(!c.semantic||c.source||c.node){if(c.semantic&&(c.source||c.node)){var d=t.scene.getNodeByName(c.source||c.node||"");if(null===d&&(d=t.scene.getNodeById(c.source||c.node||"")),null===d)continue;H.SetMatrix(t.scene,d,c,l,r.getEffect())}}else H.SetMatrix(t.scene,e,c,l,r.getEffect());else{var h=s[o.uniforms[l]];if(!h)continue;if(u===F.SAMPLER_2D){var f=t.textures[a.values?h:c.value].babylonTexture;if(null==f)continue;r.getEffect().setTexture(l,f)}else H.SetUniform(r.getEffect(),l,h,u)}}i(r)}(i,e,o,t,n,r,a)}}}(e,S,a,o,_,n),S.sideOrientation=A.Material.CounterClockWiseSideOrientation,l&&l.functions){var R=l.functions;R.cullFace&&R.cullFace[0]!==V.BACK&&(S.backFaceCulling=!1);var P=R.blendFuncSeparate;P&&(P[0]===k.SRC_ALPHA&&P[1]===k.ONE_MINUS_SRC_ALPHA&&P[2]===k.ONE&&P[3]===k.ONE?S.alphaMode=A.Constants.ALPHA_COMBINE:P[0]===k.ONE&&P[1]===k.ONE&&P[2]===k.ZERO&&P[3]===k.ONE?S.alphaMode=A.Constants.ALPHA_ONEONE:P[0]===k.SRC_ALPHA&&P[1]===k.ONE&&P[2]===k.ZERO&&P[3]===k.ONE?S.alphaMode=A.Constants.ALPHA_ADD:P[0]===k.ZERO&&P[1]===k.ONE_MINUS_SRC_COLOR&&P[2]===k.ONE&&P[3]===k.ONE?S.alphaMode=A.Constants.ALPHA_SUBTRACT:P[0]===k.DST_COLOR&&P[1]===k.ZERO&&P[2]===k.ONE&&P[3]===k.ONE?S.alphaMode=A.Constants.ALPHA_MULTIPLY:P[0]===k.SRC_ALPHA&&P[1]===k.ONE_MINUS_SRC_COLOR&&P[2]===k.ONE&&P[3]===k.ONE&&(S.alphaMode=A.Constants.ALPHA_MAXIMIZED))}}else r&&r("No technique found.")},e}(),ue=function(){function e(){}return e.RegisterExtension=function(t){e.Extensions[t.name]?A.Tools.Error('Tool with the same name "'+t.name+'" already exists'):e.Extensions[t.name]=t},e.prototype.dispose=function(){},e.prototype._importMeshAsync=function(e,t,n,r,o,a,i,s){var l=this;return t.useRightHandedSystem=!0,de.LoadRuntimeAsync(t,n,r,(function(t){t.assetContainer=o,t.importOnlyMeshes=!0,""===e?t.importMeshesNames=[]:"string"==typeof e?t.importMeshesNames=[e]:!e||e instanceof Array?(t.importMeshesNames=[],A.Tools.Warn("Argument meshesNames must be of type string or string[]")):t.importMeshesNames=[e],l._createNodes(t);var n=[],r=[];for(var i in t.nodes){var s=t.nodes[i];s.babylonNode instanceof A.AbstractMesh&&n.push(s.babylonNode)}for(var c in t.skins){var u=t.skins[c];u.babylonSkeleton instanceof A.Skeleton&&r.push(u.babylonSkeleton)}l._loadBuffersAsync(t,(function(){l._loadShadersAsync(t,(function(){le(t),ie(t),!G.IncrementalLoading&&a&&a(n,r)}))})),G.IncrementalLoading&&a&&a(n,r)}),s),!0},e.prototype.importMeshAsync=function(e,t,n,r,o,a){var i=this;return new Promise((function(s,l){i._importMeshAsync(e,t,r,o,n,(function(e,t){s({meshes:e,particleSystems:[],skeletons:t,animationGroups:[],lights:[],transformNodes:[],geometries:[],spriteManagers:[]})}),a,(function(e){l(new Error(e))}))}))},e.prototype._loadAsync=function(e,t,n,r,o,a){var i=this;e.useRightHandedSystem=!0,de.LoadRuntimeAsync(e,t,n,(function(e){de.LoadRuntimeExtensionsAsync(e,(function(){i._createNodes(e),i._loadBuffersAsync(e,(function(){i._loadShadersAsync(e,(function(){le(e),ie(e),G.IncrementalLoading||r()}))})),G.IncrementalLoading&&r()}),a)}),a)},e.prototype.loadAsync=function(e,t,n,r){var o=this;return new Promise((function(a,i){o._loadAsync(e,t,n,(function(){a()}),r,(function(e){i(new Error(e))}))}))},e.prototype._loadShadersAsync=function(e,t){var n=!1,r=function(n,r){de.LoadShaderStringAsync(e,n,(function(o){o instanceof ArrayBuffer||(e.loadedShaderCount++,o&&(A.Effect.ShadersStore[n+(r.type===P.VERTEX?"VertexShader":"PixelShader")]=o),e.loadedShaderCount===e.shaderscount&&t())}),(function(){A.Tools.Error("Error when loading shader program named "+n+" located at "+r.uri)}))};for(var o in e.shaders){n=!0;var a=e.shaders[o];a?r.bind(this,o,a)():A.Tools.Error("No shader named: "+o)}n||t()},e.prototype._loadBuffersAsync=function(e,t){var n=!1,r=function(n,r){de.LoadBufferAsync(e,n,(function(o){e.loadedBufferCount++,o&&(o.byteLength!=e.buffers[n].byteLength&&A.Tools.Error("Buffer named "+n+" is length "+o.byteLength+". Expected: "+r.byteLength),e.loadedBufferViews[n]=o),e.loadedBufferCount===e.buffersCount&&t()}),(function(){A.Tools.Error("Error when loading buffer named "+n+" located at "+r.uri)}))};for(var o in e.buffers){n=!0;var a=e.buffers[o];a?r.bind(this,o,a)():A.Tools.Error("No buffer named: "+o)}n||t()},e.prototype._createNodes=function(e){var t=e.currentScene;if(t)for(var n=0;n<t.nodes.length;n++)ae(e,t.nodes[n],null);else for(var r in e.scenes)for(t=e.scenes[r],n=0;n<t.nodes.length;n++)ae(e,t.nodes[n],null)},e.Extensions={},e}(),de=function(){function e(e){this._name=e}return Object.defineProperty(e.prototype,"name",{get:function(){return this._name},enumerable:!1,configurable:!0}),e.prototype.loadRuntimeAsync=function(e,t,n,r,o){return!1},e.prototype.loadRuntimeExtensionsAsync=function(e,t,n){return!1},e.prototype.loadBufferAsync=function(e,t,n,r,o){return!1},e.prototype.loadTextureBufferAsync=function(e,t,n,r){return!1},e.prototype.createTextureAsync=function(e,t,n,r,o){return!1},e.prototype.loadShaderStringAsync=function(e,t,n,r){return!1},e.prototype.loadMaterialAsync=function(e,t,n,r){return!1},e.LoadRuntimeAsync=function(t,n,r,o,a){e._ApplyExtensions((function(e){return e.loadRuntimeAsync(t,n,r,o,a)}),(function(){setTimeout((function(){o&&o(ce.CreateRuntime(n.json,t,r))}))}))},e.LoadRuntimeExtensionsAsync=function(t,n,r){e._ApplyExtensions((function(e){return e.loadRuntimeExtensionsAsync(t,n,r)}),(function(){setTimeout((function(){n()}))}))},e.LoadBufferAsync=function(t,n,r,o,a){e._ApplyExtensions((function(e){return e.loadBufferAsync(t,n,r,o,a)}),(function(){ce.LoadBufferAsync(t,n,r,o,a)}))},e.LoadTextureAsync=function(t,n,r,o){e._LoadTextureBufferAsync(t,n,(function(a){a&&e._CreateTextureAsync(t,n,a,r,o)}),o)},e.LoadShaderStringAsync=function(t,n,r,o){e._ApplyExtensions((function(e){return e.loadShaderStringAsync(t,n,r,o)}),(function(){ce.LoadShaderStringAsync(t,n,r,o)}))},e.LoadMaterialAsync=function(t,n,r,o){e._ApplyExtensions((function(e){return e.loadMaterialAsync(t,n,r,o)}),(function(){ce.LoadMaterialAsync(t,n,r,o)}))},e._LoadTextureBufferAsync=function(t,n,r,o){e._ApplyExtensions((function(e){return e.loadTextureBufferAsync(t,n,r,o)}),(function(){ce.LoadTextureBufferAsync(t,n,r,o)}))},e._CreateTextureAsync=function(t,n,r,o,a){e._ApplyExtensions((function(e){return e.createTextureAsync(t,n,r,o,a)}),(function(){ce.CreateTextureAsync(t,n,r,o)}))},e._ApplyExtensions=function(e,t){for(var n in ue.Extensions)if(e(ue.Extensions[n]))return;t()},e}();G._CreateGLTF1Loader=function(){return new ue};var he=function(e){function t(){return e.call(this,"KHR_binary_glTF")||this}return y(t,e),t.prototype.loadRuntimeAsync=function(e,t,n,r){var o=t.json.extensionsUsed;return!(!o||-1===o.indexOf(this.name)||!t.bin||(this._bin=t.bin,r(ce.CreateRuntime(t.json,e,n)),0))},t.prototype.loadBufferAsync=function(e,t,n,r){return-1!==e.extensionsUsed.indexOf(this.name)&&"binary_glTF"===t&&(this._bin.readAsync(0,this._bin.byteLength).then(n,(function(e){return r(e.message)})),!0)},t.prototype.loadTextureBufferAsync=function(e,t,n){var r=e.textures[t],o=e.images[r.source];if(!o.extensions||!(this.name in o.extensions))return!1;var a=o.extensions[this.name],i=e.bufferViews[a.bufferView];return n(H.GetBufferFromBufferView(e,i,0,i.byteLength,R.UNSIGNED_BYTE)),!0},t.prototype.loadShaderStringAsync=function(e,t,n){var r=e.shaders[t];if(!r.extensions||!(this.name in r.extensions))return!1;var o=r.extensions[this.name],a=e.bufferViews[o.bufferView],i=H.GetBufferFromBufferView(e,a,0,a.byteLength,R.UNSIGNED_BYTE);return setTimeout((function(){var e=H.DecodeBufferToText(i);n(e)})),!0},t}(de);ue.RegisterExtension(new he);var fe=function(e){function t(){return e.call(this,"KHR_materials_common")||this}return y(t,e),t.prototype.loadRuntimeExtensionsAsync=function(e){if(!e.extensions)return!1;var t=e.extensions[this.name];if(!t)return!1;var n=t.lights;if(n)for(var r in n){var o=n[r];switch(o.type){case"ambient":var a=new A.HemisphericLight(o.name,new A.Vector3(0,1,0),e.scene),i=o.ambient;i&&(a.diffuse=A.Color3.FromArray(i.color||[1,1,1]));break;case"point":var s=new A.PointLight(o.name,new A.Vector3(10,10,10),e.scene),l=o.point;l&&(s.diffuse=A.Color3.FromArray(l.color||[1,1,1]));break;case"directional":var c=new A.DirectionalLight(o.name,new A.Vector3(0,-1,0),e.scene),u=o.directional;u&&(c.diffuse=A.Color3.FromArray(u.color||[1,1,1]));break;case"spot":var d=o.spot;d&&(new A.SpotLight(o.name,new A.Vector3(0,10,0),new A.Vector3(0,-1,0),d.fallOffAngle||Math.PI,d.fallOffExponent||0,e.scene).diffuse=A.Color3.FromArray(d.color||[1,1,1]));break;default:A.Tools.Warn('GLTF Material Common extension: light type "'+o.type+"” not supported")}}return!1},t.prototype.loadMaterialAsync=function(e,t,n,r){var o=e.materials[t];if(!o||!o.extensions)return!1;var a=o.extensions[this.name];if(!a)return!1;var i=new A.StandardMaterial(t,e.scene);return i.sideOrientation=A.Material.CounterClockWiseSideOrientation,"CONSTANT"===a.technique&&(i.disableLighting=!0),i.backFaceCulling=void 0!==a.doubleSided&&!a.doubleSided,i.alpha=void 0===a.values.transparency?1:a.values.transparency,i.specularPower=void 0===a.values.shininess?0:a.values.shininess,"string"==typeof a.values.ambient?this._loadTexture(e,a.values.ambient,i,"ambientTexture",r):i.ambientColor=A.Color3.FromArray(a.values.ambient||[0,0,0]),"string"==typeof a.values.diffuse?this._loadTexture(e,a.values.diffuse,i,"diffuseTexture",r):i.diffuseColor=A.Color3.FromArray(a.values.diffuse||[0,0,0]),"string"==typeof a.values.emission?this._loadTexture(e,a.values.emission,i,"emissiveTexture",r):i.emissiveColor=A.Color3.FromArray(a.values.emission||[0,0,0]),"string"==typeof a.values.specular?this._loadTexture(e,a.values.specular,i,"specularTexture",r):i.specularColor=A.Color3.FromArray(a.values.specular||[0,0,0]),!0},t.prototype._loadTexture=function(e,t,n,r,o){ce.LoadTextureBufferAsync(e,t,(function(o){ce.CreateTextureAsync(e,t,o,(function(e){return n[r]=e}))}),o)},t}(de);function pe(e,t,n,r){return A.Vector3.FromArray(t,n).scaleInPlace(r)}ue.RegisterExtension(new fe);var _e=function(){function e(e,t,n,r){this.type=e,this.name=t,this.getValue=n,this.getStride=r}return e.prototype._buildAnimation=function(e,t,n){var r=new A.Animation(e,this.name,t,this.type);return r.setKeys(n),r},e}(),me=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return y(t,e),t.prototype.buildAnimations=function(e,t,n,r,o){o(e._babylonTransformNode,this._buildAnimation(t,n,r))},t}(_e),ye=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return y(t,e),t.prototype.buildAnimations=function(e,t,n,r,o){if(e._numMorphTargets)for(var a=function(a){var s=new A.Animation("".concat(t,"_").concat(a),i.name,n,i.type);if(s.setKeys(r.map((function(e){return{frame:e.frame,inTangent:e.inTangent?e.inTangent[a]:void 0,value:e.value[a],outTangent:e.outTangent?e.outTangent[a]:void 0,interpolation:e.interpolation}}))),e._primitiveBabylonMeshes)for(var l=0,c=e._primitiveBabylonMeshes;l<c.length;l++){var u=c[l];if(u.morphTargetManager){var d=u.morphTargetManager.getTarget(a),h=s.clone();d.animations.push(h),o(d,h)}}},i=this,s=0;s<e._numMorphTargets;s++)a(s)},t}(_e),ve={translation:[new me(A.Animation.ANIMATIONTYPE_VECTOR3,"position",pe,(function(){return 3}))],rotation:[new me(A.Animation.ANIMATIONTYPE_QUATERNION,"rotationQuaternion",(function(e,t,n,r){return A.Quaternion.FromArray(t,n).scaleInPlace(r)}),(function(){return 4}))],scale:[new me(A.Animation.ANIMATIONTYPE_VECTOR3,"scaling",pe,(function(){return 3}))],weights:[new ye(A.Animation.ANIMATIONTYPE_FLOAT,"influence",(function(e,t,n,r){for(var o=new Array(e._numMorphTargets),a=0;a<o.length;a++)o[a]=t[n++]*r;return o}),(function(e){return e._numMorphTargets}))]},be=new Map,ge=be;function Ae(e,t,n){Te(e)&&A.Logger.Warn("Extension with the name '".concat(e,"' already exists")),be.set(e,{isGLTFExtension:t,factory:n})}function Te(e){return be.delete(e)}function xe(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=function(e){return e&&"object"==typeof e};return e.reduce((function(e,t){return Object.keys(t).forEach((function(r){var o=e[r],a=t[r];Array.isArray(o)&&Array.isArray(a)?e[r]=o.concat.apply(o,a):n(o)&&n(a)?e[r]=xe(o,a):e[r]=a})),e}),{})}var Oe=function(){function e(){}return e.Get=function(e,t,n){if(!t||null==n||!t[n])throw new Error("".concat(e,": Failed to find index (").concat(n,")"));return t[n]},e.TryGet=function(e,t){return e&&null!=t&&e[t]?e[t]:null},e.Assign=function(e){if(e)for(var t=0;t<e.length;t++)e[t].index=t},e}(),Ee=function(){function e(e){this._completePromises=new Array,this._assetContainer=null,this._babylonLights=[],this._disableInstancedMesh=0,this._allMaterialsDirtyRequired=!1,this._extensions=new Array,this._disposed=!1,this._rootUrl=null,this._fileName=null,this._uniqueRootUrl=null,this._bin=null,this._rootBabylonMesh=null,this._defaultBabylonMaterialData={},this._postSceneLoadActions=new Array,this._parent=e}return e.RegisterExtension=function(e,t){Ae(e,!1,t)},e.UnregisterExtension=function(e){return Te(e)},Object.defineProperty(e.prototype,"gltf",{get:function(){if(!this._gltf)throw new Error("glTF JSON is not available");return this._gltf},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"bin",{get:function(){return this._bin},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"parent",{get:function(){return this._parent},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"babylonScene",{get:function(){if(!this._babylonScene)throw new Error("Scene is not available");return this._babylonScene},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"rootBabylonMesh",{get:function(){return this._rootBabylonMesh},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"rootUrl",{get:function(){return this._rootUrl},enumerable:!1,configurable:!0}),e.prototype.dispose=function(){this._disposed||(this._disposed=!0,this._completePromises.length=0,this._extensions.forEach((function(e){return e.dispose&&e.dispose()})),this._extensions.length=0,this._gltf=null,this._bin=null,this._babylonScene=null,this._rootBabylonMesh=null,this._defaultBabylonMaterialData={},this._postSceneLoadActions.length=0,this._parent.dispose())},e.prototype.importMeshAsync=function(e,t,n,r,o,a,i){var s=this;return void 0===i&&(i=""),Promise.resolve().then((function(){s._babylonScene=t,s._assetContainer=n,s._loadData(r);var a=null;if(e){var l={};if(s._gltf.nodes)for(var c=0,u=s._gltf.nodes;c<u.length;c++){var d=u[c];d.name&&(l[d.name]=d.index)}a=(e instanceof Array?e:[e]).map((function(e){var t=l[e];if(void 0===t)throw new Error("Failed to find node '".concat(e,"'"));return t}))}return s._loadAsync(o,i,a,(function(){return{meshes:s._getMeshes(),particleSystems:[],skeletons:s._getSkeletons(),animationGroups:s._getAnimationGroups(),lights:s._babylonLights,transformNodes:s._getTransformNodes(),geometries:s._getGeometries(),spriteManagers:[]}}))}))},e.prototype.loadAsync=function(e,t,n,r,o){var a=this;return void 0===o&&(o=""),Promise.resolve().then((function(){return a._babylonScene=e,a._loadData(t),a._loadAsync(n,o,null,(function(){}))}))},e.prototype._loadAsync=function(e,t,n,r){var o=this;return Promise.resolve().then((function(){return b(o,void 0,void 0,(function(){var o,a,i,s,l,c,u,d,h,f=this;return g(this,(function(p){switch(p.label){case 0:return this._rootUrl=e,this._uniqueRootUrl=!e.startsWith("file:")&&t?e:"".concat(e).concat(Date.now(),"/"),this._fileName=t,this._allMaterialsDirtyRequired=!1,[4,this._loadExtensionsAsync()];case 1:if(p.sent(),o="".concat(M[M.LOADING]," => ").concat(M[M.READY]),a="".concat(M[M.LOADING]," => ").concat(M[M.COMPLETE]),this._parent._startPerformanceCounter(o),this._parent._startPerformanceCounter(a),this._parent._setState(M.LOADING),this._extensionsOnLoading(),i=new Array,s=this._babylonScene.blockMaterialDirtyMechanism,this._babylonScene.blockMaterialDirtyMechanism=!0,this.parent.loadOnlyMaterials||(n?i.push(this.loadSceneAsync("/nodes",{nodes:n,index:-1})):(null!=this._gltf.scene||this._gltf.scenes&&this._gltf.scenes[0])&&(l=Oe.Get("/scene",this._gltf.scenes,this._gltf.scene||0),i.push(this.loadSceneAsync("/scenes/".concat(l.index),l)))),!this.parent.skipMaterials&&this.parent.loadAllMaterials&&this._gltf.materials)for(c=0;c<this._gltf.materials.length;++c)u=this._gltf.materials[c],d="/materials/"+c,h=A.Material.TriangleFillMode,i.push(this._loadMaterialAsync(d,u,null,h,(function(){})));return this._allMaterialsDirtyRequired?this._babylonScene.blockMaterialDirtyMechanism=s:this._babylonScene._forceBlockMaterialDirtyMechanism(s),this._parent.compileMaterials&&i.push(this._compileMaterialsAsync()),this._parent.compileShadowGenerators&&i.push(this._compileShadowGeneratorsAsync()),[2,Promise.all(i).then((function(){return f._rootBabylonMesh&&f._rootBabylonMesh!==f._parent.customRootNode&&f._rootBabylonMesh.setEnabled(!0),f._extensionsOnReady(),f._parent._setState(M.READY),f._startAnimations(),r()})).then((function(e){return f._parent._endPerformanceCounter(o),A.Tools.SetImmediate((function(){f._disposed||Promise.all(f._completePromises).then((function(){f._parent._endPerformanceCounter(a),f._parent._setState(M.COMPLETE),f._parent.onCompleteObservable.notifyObservers(void 0),f._parent.onCompleteObservable.clear(),f.dispose()}),(function(e){f._parent.onErrorObservable.notifyObservers(e),f._parent.onErrorObservable.clear(),f.dispose()}))})),e}))]}}))}))})).catch((function(e){throw o._disposed||(o._parent.onErrorObservable.notifyObservers(e),o._parent.onErrorObservable.clear(),o.dispose()),e}))},e.prototype._loadData=function(e){if(this._gltf=e.json,this._setupData(),e.bin){var t=this._gltf.buffers;if(t&&t[0]&&!t[0].uri){var n=t[0];(n.byteLength<e.bin.byteLength-3||n.byteLength>e.bin.byteLength)&&A.Logger.Warn("Binary buffer length (".concat(n.byteLength,") from JSON does not match chunk length (").concat(e.bin.byteLength,")")),this._bin=e.bin}else A.Logger.Warn("Unexpected BIN chunk")}},e.prototype._setupData=function(){if(Oe.Assign(this._gltf.accessors),Oe.Assign(this._gltf.animations),Oe.Assign(this._gltf.buffers),Oe.Assign(this._gltf.bufferViews),Oe.Assign(this._gltf.cameras),Oe.Assign(this._gltf.images),Oe.Assign(this._gltf.materials),Oe.Assign(this._gltf.meshes),Oe.Assign(this._gltf.nodes),Oe.Assign(this._gltf.samplers),Oe.Assign(this._gltf.scenes),Oe.Assign(this._gltf.skins),Oe.Assign(this._gltf.textures),this._gltf.nodes){for(var e={},t=0,n=this._gltf.nodes;t<n.length;t++)if((l=n[t]).children)for(var r=0,o=l.children;r<o.length;r++)e[o[r]]=l.index;for(var a=this._createRootNode(),i=0,s=this._gltf.nodes;i<s.length;i++){var l,c=e[(l=s[i]).index];l.parent=void 0===c?a:this._gltf.nodes[c]}}},e.prototype._loadExtensionsAsync=function(){return b(this,void 0,void 0,(function(){var e,t,n,r,o,a,i,s,l,c,u,d=this;return g(this,(function(h){switch(h.label){case 0:return e=[],ge.forEach((function(t,n){var r;!1===(null===(r=d.parent.extensionOptions[n])||void 0===r?void 0:r.enabled)?t.isGLTFExtension&&d.isExtensionUsed(n)&&A.Logger.Warn("Extension ".concat(n," is used but has been explicitly disabled.")):t.isGLTFExtension&&!d.isExtensionUsed(n)||e.push(b(d,void 0,void 0,(function(){var e;return g(this,(function(r){switch(r.label){case 0:return[4,t.factory(this)];case 1:return(e=r.sent()).name!==n&&A.Logger.Warn("The name of the glTF loader extension instance does not match the registered name: ".concat(e.name," !== ").concat(n)),this._parent.onExtensionLoadedObservable.notifyObservers(e),[2,e]}}))})))})),n=(t=(c=this._extensions).push).apply,r=[c],[4,Promise.all(e)];case 1:if(n.apply(t,r.concat([h.sent()])),this._extensions.sort((function(e,t){return(e.order||Number.MAX_VALUE)-(t.order||Number.MAX_VALUE)})),this._parent.onExtensionLoadedObservable.clear(),this._gltf.extensionsRequired)for(o=function(e){if(!a._extensions.some((function(t){return t.name===e&&t.enabled}))){if(!1===(null===(u=a.parent.extensionOptions[e])||void 0===u?void 0:u.enabled))throw new Error("Required extension ".concat(e," is disabled"));throw new Error("Required extension ".concat(e," is not available"))}},a=this,i=0,s=this._gltf.extensionsRequired;i<s.length;i++)l=s[i],o(l);return[2]}}))}))},e.prototype._createRootNode=function(){if(void 0!==this._parent.customRootNode)return this._rootBabylonMesh=this._parent.customRootNode,{_babylonTransformNode:null===this._rootBabylonMesh?void 0:this._rootBabylonMesh,index:-1};this._babylonScene._blockEntityCollection=!!this._assetContainer;var t=new A.Mesh("__root__",this._babylonScene);this._rootBabylonMesh=t,this._rootBabylonMesh._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,this._rootBabylonMesh.setEnabled(!1);var n={_babylonTransformNode:this._rootBabylonMesh,index:-1};switch(this._parent.coordinateSystemMode){case O.AUTO:this._babylonScene.useRightHandedSystem||(n.rotation=[0,1,0,0],n.scale=[1,1,-1],e._LoadTransform(n,this._rootBabylonMesh));break;case O.FORCE_RIGHT_HANDED:this._babylonScene.useRightHandedSystem=!0;break;default:throw new Error("Invalid coordinate system mode (".concat(this._parent.coordinateSystemMode,")"))}return this._parent.onMeshLoadedObservable.notifyObservers(t),n},e.prototype.loadSceneAsync=function(e,t){var n=this,r=this._extensionsLoadSceneAsync(e,t);if(r)return r;var o=new Array;if(this.logOpen("".concat(e," ").concat(t.name||"")),t.nodes)for(var a=0,i=t.nodes;a<i.length;a++){var s=i[a],l=Oe.Get("".concat(e,"/nodes/").concat(s),this._gltf.nodes,s);o.push(this.loadNodeAsync("/nodes/".concat(l.index),l,(function(e){e.parent=n._rootBabylonMesh})))}for(var c=0,u=this._postSceneLoadActions;c<u.length;c++)(0,u[c])();return o.push(this._loadAnimationsAsync()),this.logClose(),Promise.all(o).then((function(){}))},e.prototype._forEachPrimitive=function(e,t){if(e._primitiveBabylonMeshes)for(var n=0,r=e._primitiveBabylonMeshes;n<r.length;n++)t(r[n])},e.prototype._getGeometries=function(){var e=[],t=this._gltf.nodes;if(t)for(var n=0,r=t;n<r.length;n++){var o=r[n];this._forEachPrimitive(o,(function(t){var n=t.geometry;n&&-1===e.indexOf(n)&&e.push(n)}))}return e},e.prototype._getMeshes=function(){var e=[];this._rootBabylonMesh instanceof A.AbstractMesh&&e.push(this._rootBabylonMesh);var t=this._gltf.nodes;if(t)for(var n=0,r=t;n<r.length;n++){var o=r[n];this._forEachPrimitive(o,(function(t){e.push(t)}))}return e},e.prototype._getTransformNodes=function(){var e=[],t=this._gltf.nodes;if(t)for(var n=0,r=t;n<r.length;n++){var o=r[n];o._babylonTransformNode&&"TransformNode"===o._babylonTransformNode.getClassName()&&e.push(o._babylonTransformNode),o._babylonTransformNodeForSkin&&e.push(o._babylonTransformNodeForSkin)}return e},e.prototype._getSkeletons=function(){var e=[],t=this._gltf.skins;if(t)for(var n=0,r=t;n<r.length;n++){var o=r[n];o._data&&e.push(o._data.babylonSkeleton)}return e},e.prototype._getAnimationGroups=function(){var e=[],t=this._gltf.animations;if(t)for(var n=0,r=t;n<r.length;n++){var o=r[n];o._babylonAnimationGroup&&e.push(o._babylonAnimationGroup)}return e},e.prototype._startAnimations=function(){switch(this._parent.animationStartMode){case E.NONE:break;case E.FIRST:0!==(e=this._getAnimationGroups()).length&&e[0].start(!0);break;case E.ALL:for(var e,t=0,n=e=this._getAnimationGroups();t<n.length;t++)n[t].start(!0);break;default:return void A.Logger.Error("Invalid animation start mode (".concat(this._parent.animationStartMode,")"))}},e.prototype.loadNodeAsync=function(t,n,r){var o=this;void 0===r&&(r=function(){});var a=this._extensionsLoadNodeAsync(t,n,r);if(a)return a;if(n._babylonTransformNode)throw new Error("".concat(t,": Invalid recursive node hierarchy"));var i=new Array;this.logOpen("".concat(t," ").concat(n.name||""));var s=function(a){if(e.AddPointerMetadata(a,t),e._LoadTransform(n,a),null!=n.camera){var s=Oe.Get("".concat(t,"/camera"),o._gltf.cameras,n.camera);i.push(o.loadCameraAsync("/cameras/".concat(s.index),s,(function(e){e.parent=a})))}if(n.children)for(var l=0,c=n.children;l<c.length;l++){var u=c[l],d=Oe.Get("".concat(t,"/children/").concat(u),o._gltf.nodes,u);i.push(o.loadNodeAsync("/nodes/".concat(d.index),d,(function(e){e.parent=a})))}r(a)},l=null!=n.mesh,c=this._parent.loadSkins&&null!=n.skin;if(!l||c){var u=n.name||"node".concat(n.index);this._babylonScene._blockEntityCollection=!!this._assetContainer;var d=new A.TransformNode(u,this._babylonScene);d._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,null==n.mesh?n._babylonTransformNode=d:n._babylonTransformNodeForSkin=d,s(d)}if(l)if(c){var h=Oe.Get("".concat(t,"/mesh"),this._gltf.meshes,n.mesh);i.push(this._loadMeshAsync("/meshes/".concat(h.index),n,h,(function(e){var r=n._babylonTransformNodeForSkin;e.metadata=xe(r.metadata,e.metadata||{});var a=Oe.Get("".concat(t,"/skin"),o._gltf.skins,n.skin);i.push(o._loadSkinAsync("/skins/".concat(a.index),n,a,(function(t){o._forEachPrimitive(n,(function(e){e.skeleton=t})),o._postSceneLoadActions.push((function(){if(null!=a.skeleton){var t=Oe.Get("/skins/".concat(a.index,"/skeleton"),o._gltf.nodes,a.skeleton).parent;n.index===t.index?e.parent=r.parent:e.parent=t._babylonTransformNode}else e.parent=o._rootBabylonMesh;o._parent.onSkinLoadedObservable.notifyObservers({node:r,skinnedNode:e})}))})))})))}else h=Oe.Get("".concat(t,"/mesh"),this._gltf.meshes,n.mesh),i.push(this._loadMeshAsync("/meshes/".concat(h.index),n,h,s));return this.logClose(),Promise.all(i).then((function(){return o._forEachPrimitive(n,(function(e){e.geometry&&e.geometry.useBoundingInfoFromGeometry?e._updateBoundingInfo():e.refreshBoundingInfo(!0,!0)})),n._babylonTransformNode}))},e.prototype._loadMeshAsync=function(e,t,n,r){var o=n.primitives;if(!o||!o.length)throw new Error("".concat(e,": Primitives are missing"));null==o[0].index&&Oe.Assign(o);var a=new Array;this.logOpen("".concat(e," ").concat(n.name||""));var i=t.name||"node".concat(t.index);if(1===o.length){var s=n.primitives[0];a.push(this._loadMeshPrimitiveAsync("".concat(e,"/primitives/").concat(s.index),i,t,n,s,(function(e){t._babylonTransformNode=e,t._primitiveBabylonMeshes=[e]})))}else{this._babylonScene._blockEntityCollection=!!this._assetContainer,t._babylonTransformNode=new A.TransformNode(i,this._babylonScene),t._babylonTransformNode._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,t._primitiveBabylonMeshes=[];for(var l=0,c=o;l<c.length;l++)s=c[l],a.push(this._loadMeshPrimitiveAsync("".concat(e,"/primitives/").concat(s.index),"".concat(i,"_primitive").concat(s.index),t,n,s,(function(e){e.parent=t._babylonTransformNode,t._primitiveBabylonMeshes.push(e)})))}return r(t._babylonTransformNode),this.logClose(),Promise.all(a).then((function(){return t._babylonTransformNode}))},e.prototype._loadMeshPrimitiveAsync=function(t,n,r,o,a,i){var s=this,l=this._extensionsLoadMeshPrimitiveAsync(t,n,r,o,a,i);if(l)return l;this.logOpen("".concat(t));var c,u,d=0===this._disableInstancedMesh&&this._parent.createInstances&&null==r.skin&&!o.primitives[0].targets;if(d&&a._instanceData)this._babylonScene._blockEntityCollection=!!this._assetContainer,(c=a._instanceData.babylonSourceMesh.createInstance(n))._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,u=a._instanceData.promise;else{var h=new Array;this._babylonScene._blockEntityCollection=!!this._assetContainer;var f=new A.Mesh(n,this._babylonScene);f._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,f.sideOrientation=this._babylonScene.useRightHandedSystem?A.Material.CounterClockWiseSideOrientation:A.Material.ClockWiseSideOrientation,this._createMorphTargets(t,r,o,a,f),h.push(this._loadVertexDataAsync(t,a,f).then((function(e){return s._loadMorphTargetsAsync(t,a,f,e).then((function(){s._disposed||(s._babylonScene._blockEntityCollection=!!s._assetContainer,e.applyToMesh(f),e._parentContainer=s._assetContainer,s._babylonScene._blockEntityCollection=!1)}))})));var p=e._GetDrawMode(t,a.mode);if(null==a.material){var _=this._defaultBabylonMaterialData[p];_||(_=this._createDefaultMaterial("__GLTFLoader._default",p),this._parent.onMaterialLoadedObservable.notifyObservers(_),this._defaultBabylonMaterialData[p]=_),f.material=_}else if(!this.parent.skipMaterials){var m=Oe.Get("".concat(t,"/material"),this._gltf.materials,a.material);h.push(this._loadMaterialAsync("/materials/".concat(m.index),m,f,p,(function(e){f.material=e})))}u=Promise.all(h),d&&(a._instanceData={babylonSourceMesh:f,promise:u}),c=f}return e.AddPointerMetadata(c,t),this._parent.onMeshLoadedObservable.notifyObservers(c),i(c),this.logClose(),u.then((function(){return c}))},e.prototype._loadVertexDataAsync=function(e,t,n){var r=this,o=this._extensionsLoadVertexDataAsync(e,t,n);if(o)return o;var a=t.attributes;if(!a)throw new Error("".concat(e,": Attributes are missing"));var i=new Array,s=new A.Geometry(n.name,this._babylonScene);if(null==t.indices)n.isUnIndexed=!0;else{var l=Oe.Get("".concat(e,"/indices"),this._gltf.accessors,t.indices);i.push(this._loadIndicesAccessorAsync("/accessors/".concat(l.index),l).then((function(e){s.setIndices(e)})))}var c=function(t,o,l){if(null!=a[t]){n._delayInfo=n._delayInfo||[],-1===n._delayInfo.indexOf(o)&&n._delayInfo.push(o);var c=Oe.Get("".concat(e,"/attributes/").concat(t),r._gltf.accessors,a[t]);i.push(r._loadVertexAccessorAsync("/accessors/".concat(c.index),c,o).then((function(e){var t,o;if(e.getKind()===A.VertexBuffer.PositionKind&&!r.parent.alwaysComputeBoundingBox&&!n.skeleton&&c.min&&c.max){var a=(t=A.TmpVectors.Vector3[0]).copyFromFloats.apply(t,c.min),i=(o=A.TmpVectors.Vector3[1]).copyFromFloats.apply(o,c.max);if(c.normalized&&5126!==c.componentType){var l=1;switch(c.componentType){case 5120:l=127;break;case 5121:l=255;break;case 5122:l=32767;break;case 5123:l=65535}var u=1/l;a.scaleInPlace(u),i.scaleInPlace(u)}s._boundingInfo=new A.BoundingInfo(a,i),s.useBoundingInfoFromGeometry=!0}s.setVerticesBuffer(e,c.count)}))),o==A.VertexBuffer.MatricesIndicesExtraKind&&(n.numBoneInfluencers=8),l&&l(c)}};return c("POSITION",A.VertexBuffer.PositionKind),c("NORMAL",A.VertexBuffer.NormalKind),c("TANGENT",A.VertexBuffer.TangentKind),c("TEXCOORD_0",A.VertexBuffer.UVKind),c("TEXCOORD_1",A.VertexBuffer.UV2Kind),c("TEXCOORD_2",A.VertexBuffer.UV3Kind),c("TEXCOORD_3",A.VertexBuffer.UV4Kind),c("TEXCOORD_4",A.VertexBuffer.UV5Kind),c("TEXCOORD_5",A.VertexBuffer.UV6Kind),c("JOINTS_0",A.VertexBuffer.MatricesIndicesKind),c("WEIGHTS_0",A.VertexBuffer.MatricesWeightsKind),c("JOINTS_1",A.VertexBuffer.MatricesIndicesExtraKind),c("WEIGHTS_1",A.VertexBuffer.MatricesWeightsExtraKind),c("COLOR_0",A.VertexBuffer.ColorKind,(function(e){"VEC4"===e.type&&(n.hasVertexAlpha=!0)})),Promise.all(i).then((function(){return s}))},e.prototype._createMorphTargets=function(e,t,n,r,o){if(r.targets&&this._parent.loadMorphTargets){if(null==t._numMorphTargets)t._numMorphTargets=r.targets.length;else if(r.targets.length!==t._numMorphTargets)throw new Error("".concat(e,": Primitives do not have the same number of targets"));var a=n.extras?n.extras.targetNames:null;this._babylonScene._blockEntityCollection=!!this._assetContainer,o.morphTargetManager=new A.MorphTargetManager(this._babylonScene),o.morphTargetManager._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,o.morphTargetManager.areUpdatesFrozen=!0;for(var i=0;i<r.targets.length;i++){var s=t.weights?t.weights[i]:n.weights?n.weights[i]:0,l=a?a[i]:"morphTarget".concat(i);o.morphTargetManager.addTarget(new A.MorphTarget(l,s,o.getScene()))}}},e.prototype._loadMorphTargetsAsync=function(e,t,n,r){if(!t.targets||!this._parent.loadMorphTargets)return Promise.resolve();for(var o=new Array,a=n.morphTargetManager,i=0;i<a.numTargets;i++){var s=a.getTarget(i);o.push(this._loadMorphTargetVertexDataAsync("".concat(e,"/targets/").concat(i),r,t.targets[i],s))}return Promise.all(o).then((function(){a.areUpdatesFrozen=!1}))},e.prototype._loadMorphTargetVertexDataAsync=function(e,t,n,r){var o=this,a=new Array,i=function(r,i,s){if(null!=n[r]){var l=t.getVertexBuffer(i);if(l){var c=Oe.Get("".concat(e,"/").concat(r),o._gltf.accessors,n[r]);a.push(o._loadFloatAccessorAsync("/accessors/".concat(c.index),c).then((function(e){s(l,e)})))}}};return i("POSITION",A.VertexBuffer.PositionKind,(function(e,t){var n=new Float32Array(t.length);e.forEach(t.length,(function(e,r){n[r]=t[r]+e})),r.setPositions(n)})),i("NORMAL",A.VertexBuffer.NormalKind,(function(e,t){var n=new Float32Array(t.length);e.forEach(n.length,(function(e,r){n[r]=t[r]+e})),r.setNormals(n)})),i("TANGENT",A.VertexBuffer.TangentKind,(function(e,t){var n=new Float32Array(t.length/3*4),o=0;e.forEach(t.length/3*4,(function(e,r){(r+1)%4!=0&&(n[o]=t[o]+e,o++)})),r.setTangents(n)})),Promise.all(a).then((function(){}))},e._LoadTransform=function(e,t){if(null==e.skin){var n=A.Vector3.Zero(),r=A.Quaternion.Identity(),o=A.Vector3.One();e.matrix?A.Matrix.FromArray(e.matrix).decompose(o,r,n):(e.translation&&(n=A.Vector3.FromArray(e.translation)),e.rotation&&(r=A.Quaternion.FromArray(e.rotation)),e.scale&&(o=A.Vector3.FromArray(e.scale))),t.position=n,t.rotationQuaternion=r,t.scaling=o}},e.prototype._loadSkinAsync=function(e,t,n,r){var o=this;if(!this._parent.loadSkins)return Promise.resolve();var a=this._extensionsLoadSkinAsync(e,t,n);if(a)return a;if(n._data)return r(n._data.babylonSkeleton),n._data.promise;var i="skeleton".concat(n.index);this._babylonScene._blockEntityCollection=!!this._assetContainer;var s=new A.Skeleton(n.name||i,i,this._babylonScene);s._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,this._loadBones(e,n,s);var l=this._loadSkinInverseBindMatricesDataAsync(e,n).then((function(e){o._updateBoneMatrices(s,e)}));return n._data={babylonSkeleton:s,promise:l},r(s),l},e.prototype._loadBones=function(e,t,n){if(null==t.skeleton||this._parent.alwaysComputeSkeletonRootNode){var r=this._findSkeletonRootNode("".concat(e,"/joints"),t.joints);if(r)if(void 0===t.skeleton)t.skeleton=r.index;else{var o=Oe.Get("".concat(e,"/skeleton"),this._gltf.nodes,t.skeleton);o===r||function(e,t){for(;t.parent;t=t.parent)if(t.parent===e)return!0;return!1}(o,r)||(A.Logger.Warn("".concat(e,"/skeleton: Overriding with nearest common ancestor as skeleton node is not a common root")),t.skeleton=r.index)}else A.Logger.Warn("".concat(e,": Failed to find common root"))}for(var a={},i=0,s=t.joints;i<s.length;i++){var l=s[i],c=Oe.Get("".concat(e,"/joints/").concat(l),this._gltf.nodes,l);this._loadBone(c,t,n,a)}},e.prototype._findSkeletonRootNode=function(e,t){if(0===t.length)return null;for(var n={},r=0,o=t;r<o.length;r++){for(var a=o[r],i=[],s=Oe.Get("".concat(e,"/").concat(a),this._gltf.nodes,a);-1!==s.index;)i.unshift(s),s=s.parent;n[a]=i}for(var l=null,c=0;;++c){if(c>=(i=n[t[0]]).length)return l;s=i[c];for(var u=1;u<t.length;++u)if(c>=(i=n[t[u]]).length||s!==i[c])return l;l=s}},e.prototype._loadBone=function(e,t,n,r){e._isJoint=!0;var o=r[e.index];if(o)return o;var a=null;e.index!==t.skeleton&&(e.parent&&-1!==e.parent.index?a=this._loadBone(e.parent,t,n,r):void 0!==t.skeleton&&A.Logger.Warn("/skins/".concat(t.index,"/skeleton: Skeleton node is not a common root")));var i=t.joints.indexOf(e.index);return o=new A.Bone(e.name||"joint".concat(e.index),n,a,this._getNodeMatrix(e),null,null,i),r[e.index]=o,this._postSceneLoadActions.push((function(){o.linkTransformNode(e._babylonTransformNode)})),o},e.prototype._loadSkinInverseBindMatricesDataAsync=function(e,t){if(null==t.inverseBindMatrices)return Promise.resolve(null);var n=Oe.Get("".concat(e,"/inverseBindMatrices"),this._gltf.accessors,t.inverseBindMatrices);return this._loadFloatAccessorAsync("/accessors/".concat(n.index),n)},e.prototype._updateBoneMatrices=function(e,t){for(var n=0,r=e.bones;n<r.length;n++){var o=r[n],a=A.Matrix.Identity(),i=o._index;t&&-1!==i&&(A.Matrix.FromArrayToRef(t,16*i,a),a.invertToRef(a));var s=o.getParent();s&&a.multiplyToRef(s.getAbsoluteInverseBindMatrix(),a),o.updateMatrix(a,!1,!1),o._updateAbsoluteBindMatrices(void 0,!1)}},e.prototype._getNodeMatrix=function(e){return e.matrix?A.Matrix.FromArray(e.matrix):A.Matrix.Compose(e.scale?A.Vector3.FromArray(e.scale):A.Vector3.One(),e.rotation?A.Quaternion.FromArray(e.rotation):A.Quaternion.Identity(),e.translation?A.Vector3.FromArray(e.translation):A.Vector3.Zero())},e.prototype.loadCameraAsync=function(t,n,r){void 0===r&&(r=function(){});var o=this._extensionsLoadCameraAsync(t,n,r);if(o)return o;var a=new Array;this.logOpen("".concat(t," ").concat(n.name||"")),this._babylonScene._blockEntityCollection=!!this._assetContainer;var i=new A.FreeCamera(n.name||"camera".concat(n.index),A.Vector3.Zero(),this._babylonScene,!1);switch(i._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,i.ignoreParentScaling=!0,n._babylonCamera=i,i.rotation.set(0,Math.PI,0),n.type){case"perspective":var s=n.perspective;if(!s)throw new Error("".concat(t,": Camera perspective properties are missing"));i.fov=s.yfov,i.minZ=s.znear,i.maxZ=s.zfar||0;break;case"orthographic":if(!n.orthographic)throw new Error("".concat(t,": Camera orthographic properties are missing"));i.mode=A.Camera.ORTHOGRAPHIC_CAMERA,i.orthoLeft=-n.orthographic.xmag,i.orthoRight=n.orthographic.xmag,i.orthoBottom=-n.orthographic.ymag,i.orthoTop=n.orthographic.ymag,i.minZ=n.orthographic.znear,i.maxZ=n.orthographic.zfar;break;default:throw new Error("".concat(t,": Invalid camera type (").concat(n.type,")"))}return e.AddPointerMetadata(i,t),this._parent.onCameraLoadedObservable.notifyObservers(i),r(i),this.logClose(),Promise.all(a).then((function(){return i}))},e.prototype._loadAnimationsAsync=function(){var e=this._gltf.animations;if(!e)return Promise.resolve();for(var t=new Array,n=0;n<e.length;n++){var r=e[n];t.push(this.loadAnimationAsync("/animations/".concat(r.index),r).then((function(e){0===e.targetedAnimations.length&&e.dispose()})))}return Promise.all(t).then((function(){}))},e.prototype.loadAnimationAsync=function(e,t){var n=this;return this._extensionsLoadAnimationAsync(e,t)||Promise.resolve().then(a.t.bind(a,597,23)).then((function(r){var o=r.AnimationGroup;n._babylonScene._blockEntityCollection=!!n._assetContainer;var a=new o(t.name||"animation".concat(t.index),n._babylonScene);a._parentContainer=n._assetContainer,n._babylonScene._blockEntityCollection=!1,t._babylonAnimationGroup=a;var i=new Array;Oe.Assign(t.channels),Oe.Assign(t.samplers);for(var s=0,l=t.channels;s<l.length;s++){var c=l[s];i.push(n._loadAnimationChannelAsync("".concat(e,"/channels/").concat(c.index),e,t,c,(function(e,t){e.animations=e.animations||[],e.animations.push(t),a.addTargetedAnimation(t,e)})))}return Promise.all(i).then((function(){return a.normalize(0),a}))}))},e.prototype._loadAnimationChannelAsync=function(e,t,n,r,o){var a=this._extensionsLoadAnimationChannelAsync(e,t,n,r,o);if(a)return a;if(null==r.target.node)return Promise.resolve();var i,s=Oe.Get("".concat(e,"/target/node"),this._gltf.nodes,r.target.node),l=r.target.path,c="weights"===l;if(c&&!s._numMorphTargets||!c&&!s._babylonTransformNode)return Promise.resolve();if(!this._parent.loadNodeAnimations&&!c&&!s._isJoint)return Promise.resolve();switch(l){case"translation":i=ve.translation;break;case"rotation":i=ve.rotation;break;case"scale":i=ve.scale;break;case"weights":i=ve.weights;break;default:throw new Error("".concat(e,"/target/path: Invalid value (").concat(r.target.path,")"))}var u={object:s,info:i};return this._loadAnimationChannelFromTargetInfoAsync(e,t,n,r,u,o)},e.prototype._loadAnimationChannelFromTargetInfoAsync=function(e,t,n,r,o,a){var i=this.parent.targetFps,s=1/i,l=Oe.Get("".concat(e,"/sampler"),n.samplers,r.sampler);return this._loadAnimationSamplerAsync("".concat(t,"/samplers/").concat(r.sampler),l).then((function(e){for(var t=0,l=o.object,c=0,u=o.info;c<u.length;c++){var d=u[c],h=d.getStride(l),f=e.input,p=e.output,_=new Array(f.length),m=0;switch(e.interpolation){case"STEP":for(var y=0;y<f.length;y++){var v=d.getValue(l,p,m,1);m+=h,_[y]={frame:f[y]*i,value:v,interpolation:1}}break;case"CUBICSPLINE":for(y=0;y<f.length;y++){var b=d.getValue(l,p,m,s);m+=h,v=d.getValue(l,p,m,1),m+=h;var g=d.getValue(l,p,m,s);m+=h,_[y]={frame:f[y]*i,inTangent:b,value:v,outTangent:g}}break;case"LINEAR":for(y=0;y<f.length;y++)v=d.getValue(l,p,m,1),m+=h,_[y]={frame:f[y]*i,value:v}}if(m>0){var A="".concat(n.name||"animation".concat(n.index),"_channel").concat(r.index,"_").concat(t);d.buildAnimations(l,A,i,_,(function(e,n){++t,a(e,n)}))}}}))},e.prototype._loadAnimationSamplerAsync=function(e,t){if(t._data)return t._data;var n=t.interpolation||"LINEAR";switch(n){case"STEP":case"LINEAR":case"CUBICSPLINE":break;default:throw new Error("".concat(e,"/interpolation: Invalid value (").concat(t.interpolation,")"))}var r=Oe.Get("".concat(e,"/input"),this._gltf.accessors,t.input),o=Oe.Get("".concat(e,"/output"),this._gltf.accessors,t.output);return t._data=Promise.all([this._loadFloatAccessorAsync("/accessors/".concat(r.index),r),this._loadFloatAccessorAsync("/accessors/".concat(o.index),o)]).then((function(e){var t=e[0],r=e[1];return{input:t,interpolation:n,output:r}})),t._data},e.prototype.loadBufferAsync=function(e,t,n,r){var o=this._extensionsLoadBufferAsync(e,t,n,r);if(o)return o;if(!t._data)if(t.uri)t._data=this.loadUriAsync("".concat(e,"/uri"),t,t.uri);else{if(!this._bin)throw new Error("".concat(e,": Uri is missing or the binary glTF is missing its binary chunk"));t._data=this._bin.readAsync(0,t.byteLength)}return t._data.then((function(t){try{return new Uint8Array(t.buffer,t.byteOffset+n,r)}catch(t){throw new Error("".concat(e,": ").concat(t.message))}}))},e.prototype.loadBufferViewAsync=function(e,t){var n=this._extensionsLoadBufferViewAsync(e,t);if(n)return n;if(t._data)return t._data;var r=Oe.Get("".concat(e,"/buffer"),this._gltf.buffers,t.buffer);return t._data=this.loadBufferAsync("/buffers/".concat(r.index),r,t.byteOffset||0,t.byteLength),t._data},e.prototype._loadAccessorAsync=function(t,n,r){var o=this;if(n._data)return n._data;var a=e._GetNumComponents(t,n.type),i=a*A.VertexBuffer.GetTypeByteLength(n.componentType),s=a*n.count;if(null==n.bufferView)n._data=Promise.resolve(new r(s));else{var l=Oe.Get("".concat(t,"/bufferView"),this._gltf.bufferViews,n.bufferView);n._data=this.loadBufferViewAsync("/bufferViews/".concat(l.index),l).then((function(o){if(5126!==n.componentType||n.normalized||l.byteStride&&l.byteStride!==i){var c=new r(s);return A.VertexBuffer.ForEach(o,n.byteOffset||0,l.byteStride||i,a,n.componentType,c.length,n.normalized||!1,(function(e,t){c[t]=e})),c}return e._GetTypedArray(t,n.componentType,o,n.byteOffset,s)}))}if(n.sparse){var c=n.sparse;n._data=n._data.then((function(s){var l=s,u=Oe.Get("".concat(t,"/sparse/indices/bufferView"),o._gltf.bufferViews,c.indices.bufferView),d=Oe.Get("".concat(t,"/sparse/values/bufferView"),o._gltf.bufferViews,c.values.bufferView);return Promise.all([o.loadBufferViewAsync("/bufferViews/".concat(u.index),u),o.loadBufferViewAsync("/bufferViews/".concat(d.index),d)]).then((function(o){var s,u=o[0],d=o[1],h=e._GetTypedArray("".concat(t,"/sparse/indices"),c.indices.componentType,u,c.indices.byteOffset,c.count),f=a*c.count;if(5126!==n.componentType||n.normalized){var p=e._GetTypedArray("".concat(t,"/sparse/values"),n.componentType,d,c.values.byteOffset,f);s=new r(f),A.VertexBuffer.ForEach(p,0,i,a,n.componentType,s.length,n.normalized||!1,(function(e,t){s[t]=e}))}else s=e._GetTypedArray("".concat(t,"/sparse/values"),n.componentType,d,c.values.byteOffset,f);for(var _=0,m=0;m<h.length;m++)for(var y=h[m]*a,v=0;v<a;v++)l[y++]=s[_++];return l}))}))}return n._data},e.prototype._loadFloatAccessorAsync=function(e,t){return this._loadAccessorAsync(e,t,Float32Array)},e.prototype._loadIndicesAccessorAsync=function(t,n){if("SCALAR"!==n.type)throw new Error("".concat(t,"/type: Invalid value ").concat(n.type));if(5121!==n.componentType&&5123!==n.componentType&&5125!==n.componentType)throw new Error("".concat(t,"/componentType: Invalid value ").concat(n.componentType));if(n._data)return n._data;if(n.sparse){var r=e._GetTypedArrayConstructor("".concat(t,"/componentType"),n.componentType);n._data=this._loadAccessorAsync(t,n,r)}else{var o=Oe.Get("".concat(t,"/bufferView"),this._gltf.bufferViews,n.bufferView);n._data=this.loadBufferViewAsync("/bufferViews/".concat(o.index),o).then((function(r){return e._GetTypedArray(t,n.componentType,r,n.byteOffset,n.count)}))}return n._data},e.prototype._loadVertexBufferViewAsync=function(e){if(e._babylonBuffer)return e._babylonBuffer;var t=this._babylonScene.getEngine();return e._babylonBuffer=this.loadBufferViewAsync("/bufferViews/".concat(e.index),e).then((function(e){return new A.Buffer(t,e,!1)})),e._babylonBuffer},e.prototype._loadVertexAccessorAsync=function(t,n,r){var o;if(null===(o=n._babylonVertexBuffer)||void 0===o?void 0:o[r])return n._babylonVertexBuffer[r];n._babylonVertexBuffer||(n._babylonVertexBuffer={});var a=this._babylonScene.getEngine();if(n.sparse||null==n.bufferView)n._babylonVertexBuffer[r]=this._loadFloatAccessorAsync(t,n).then((function(e){return new A.VertexBuffer(a,e,r,!1)}));else{var i=Oe.Get("".concat(t,"/bufferView"),this._gltf.bufferViews,n.bufferView);n._babylonVertexBuffer[r]=this._loadVertexBufferViewAsync(i).then((function(o){var s=e._GetNumComponents(t,n.type);return new A.VertexBuffer(a,o,r,!1,void 0,i.byteStride,void 0,n.byteOffset,s,n.componentType,n.normalized,!0,void 0,!0)}))}return n._babylonVertexBuffer[r]},e.prototype._loadMaterialMetallicRoughnessPropertiesAsync=function(e,t,n){if(!(n instanceof A.PBRMaterial))throw new Error("".concat(e,": Material type not supported"));var r=new Array;return t&&(t.baseColorFactor?(n.albedoColor=A.Color3.FromArray(t.baseColorFactor),n.alpha=t.baseColorFactor[3]):n.albedoColor=A.Color3.White(),n.metallic=null==t.metallicFactor?1:t.metallicFactor,n.roughness=null==t.roughnessFactor?1:t.roughnessFactor,t.baseColorTexture&&r.push(this.loadTextureInfoAsync("".concat(e,"/baseColorTexture"),t.baseColorTexture,(function(e){e.name="".concat(n.name," (Base Color)"),n.albedoTexture=e}))),t.metallicRoughnessTexture&&(t.metallicRoughnessTexture.nonColorData=!0,r.push(this.loadTextureInfoAsync("".concat(e,"/metallicRoughnessTexture"),t.metallicRoughnessTexture,(function(e){e.name="".concat(n.name," (Metallic Roughness)"),n.metallicTexture=e}))),n.useMetallnessFromMetallicTextureBlue=!0,n.useRoughnessFromMetallicTextureGreen=!0,n.useRoughnessFromMetallicTextureAlpha=!1)),Promise.all(r).then((function(){}))},e.prototype._loadMaterialAsync=function(t,n,r,o,a){void 0===a&&(a=function(){});var i=this._extensionsLoadMaterialAsync(t,n,r,o,a);if(i)return i;n._data=n._data||{};var s=n._data[o];if(!s){this.logOpen("".concat(t," ").concat(n.name||""));var l=this.createMaterial(t,n,o);s={babylonMaterial:l,babylonMeshes:[],promise:this.loadMaterialPropertiesAsync(t,n,l)},n._data[o]=s,e.AddPointerMetadata(l,t),this._parent.onMaterialLoadedObservable.notifyObservers(l),this.logClose()}return r&&(s.babylonMeshes.push(r),r.onDisposeObservable.addOnce((function(){var e=s.babylonMeshes.indexOf(r);-1!==e&&s.babylonMeshes.splice(e,1)}))),a(s.babylonMaterial),s.promise.then((function(){return s.babylonMaterial}))},e.prototype._createDefaultMaterial=function(e,t){this._babylonScene._blockEntityCollection=!!this._assetContainer;var n=new A.PBRMaterial(e,this._babylonScene);return n._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,n.fillMode=t,n.enableSpecularAntiAliasing=!0,n.useRadianceOverAlpha=!this._parent.transparencyAsCoverage,n.useSpecularOverAlpha=!this._parent.transparencyAsCoverage,n.transparencyMode=A.PBRMaterial.PBRMATERIAL_OPAQUE,n.metallic=1,n.roughness=1,n},e.prototype.createMaterial=function(e,t,n){var r=this._extensionsCreateMaterial(e,t,n);if(r)return r;var o=t.name||"material".concat(t.index);return this._createDefaultMaterial(o,n)},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this._extensionsLoadMaterialPropertiesAsync(e,t,n);if(r)return r;var o=new Array;return o.push(this.loadMaterialBasePropertiesAsync(e,t,n)),t.pbrMetallicRoughness&&o.push(this._loadMaterialMetallicRoughnessPropertiesAsync("".concat(e,"/pbrMetallicRoughness"),t.pbrMetallicRoughness,n)),this.loadMaterialAlphaProperties(e,t,n),Promise.all(o).then((function(){}))},e.prototype.loadMaterialBasePropertiesAsync=function(e,t,n){if(!(n instanceof A.PBRMaterial))throw new Error("".concat(e,": Material type not supported"));var r=new Array;return n.emissiveColor=t.emissiveFactor?A.Color3.FromArray(t.emissiveFactor):new A.Color3(0,0,0),t.doubleSided&&(n.backFaceCulling=!1,n.twoSidedLighting=!0),t.normalTexture&&(t.normalTexture.nonColorData=!0,r.push(this.loadTextureInfoAsync("".concat(e,"/normalTexture"),t.normalTexture,(function(e){e.name="".concat(n.name," (Normal)"),n.bumpTexture=e}))),n.invertNormalMapX=!this._babylonScene.useRightHandedSystem,n.invertNormalMapY=this._babylonScene.useRightHandedSystem,null!=t.normalTexture.scale&&n.bumpTexture&&(n.bumpTexture.level=t.normalTexture.scale),n.forceIrradianceInFragment=!0),t.occlusionTexture&&(t.occlusionTexture.nonColorData=!0,r.push(this.loadTextureInfoAsync("".concat(e,"/occlusionTexture"),t.occlusionTexture,(function(e){e.name="".concat(n.name," (Occlusion)"),n.ambientTexture=e}))),n.useAmbientInGrayScale=!0,null!=t.occlusionTexture.strength&&(n.ambientTextureStrength=t.occlusionTexture.strength)),t.emissiveTexture&&r.push(this.loadTextureInfoAsync("".concat(e,"/emissiveTexture"),t.emissiveTexture,(function(e){e.name="".concat(n.name," (Emissive)"),n.emissiveTexture=e}))),Promise.all(r).then((function(){}))},e.prototype.loadMaterialAlphaProperties=function(e,t,n){if(!(n instanceof A.PBRMaterial))throw new Error("".concat(e,": Material type not supported"));switch(t.alphaMode||"OPAQUE"){case"OPAQUE":n.transparencyMode=A.PBRMaterial.PBRMATERIAL_OPAQUE,n.alpha=1;break;case"MASK":n.transparencyMode=A.PBRMaterial.PBRMATERIAL_ALPHATEST,n.alphaCutOff=null==t.alphaCutoff?.5:t.alphaCutoff,n.albedoTexture&&(n.albedoTexture.hasAlpha=!0);break;case"BLEND":n.transparencyMode=A.PBRMaterial.PBRMATERIAL_ALPHABLEND,n.albedoTexture&&(n.albedoTexture.hasAlpha=!0,n.useAlphaFromAlbedoTexture=!0);break;default:throw new Error("".concat(e,"/alphaMode: Invalid value (").concat(t.alphaMode,")"))}},e.prototype.loadTextureInfoAsync=function(t,n,r){var o=this;void 0===r&&(r=function(){});var a=this._extensionsLoadTextureInfoAsync(t,n,r);if(a)return a;if(this.logOpen("".concat(t)),n.texCoord>=6)throw new Error("".concat(t,"/texCoord: Invalid value (").concat(n.texCoord,")"));var i=Oe.Get("".concat(t,"/index"),this._gltf.textures,n.index);i._textureInfo=n;var s=this._loadTextureAsync("/textures/".concat(n.index),i,(function(a){a.coordinatesIndex=n.texCoord||0,e.AddPointerMetadata(a,t),o._parent.onTextureLoadedObservable.notifyObservers(a),r(a)}));return this.logClose(),s},e.prototype._loadTextureAsync=function(t,n,r){void 0===r&&(r=function(){});var o=this._extensionsLoadTextureAsync(t,n,r);if(o)return o;this.logOpen("".concat(t," ").concat(n.name||""));var a=null==n.sampler?e.DefaultSampler:Oe.Get("".concat(t,"/sampler"),this._gltf.samplers,n.sampler),i=Oe.Get("".concat(t,"/source"),this._gltf.images,n.source),s=this._createTextureAsync(t,a,i,r,void 0,!n._textureInfo.nonColorData);return this.logClose(),s},e.prototype._createTextureAsync=function(e,t,n,r,o,a){var i=this;void 0===r&&(r=function(){});var s=this._loadSampler("/samplers/".concat(t.index),t),l=new Array,c=new A.Deferred;this._babylonScene._blockEntityCollection=!!this._assetContainer;var u={noMipmap:s.noMipMaps,invertY:!1,samplingMode:s.samplingMode,onLoad:function(){i._disposed||c.resolve()},onError:function(t,n){i._disposed||c.reject(new Error("".concat(e,": ").concat(n&&n.message?n.message:t||"Failed to load texture")))},mimeType:n.mimeType,loaderOptions:o,useSRGBBuffer:!!a&&this._parent.useSRGBBuffers},d=new A.Texture(null,this._babylonScene,u);return d._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,l.push(c.promise),l.push(this.loadImageAsync("/images/".concat(n.index),n).then((function(e){var t=n.uri||"".concat(i._fileName,"#image").concat(n.index),r="data:".concat(i._uniqueRootUrl).concat(t);d.updateURL(r,e);var o=d.getInternalTexture();o&&(o.label=n.name)}))),d.wrapU=s.wrapU,d.wrapV=s.wrapV,r(d),this._parent.useGltfTextureNames&&(d.name=n.name||n.uri||"image".concat(n.index)),Promise.all(l).then((function(){return d}))},e.prototype._loadSampler=function(t,n){return n._data||(n._data={noMipMaps:9728===n.minFilter||9729===n.minFilter,samplingMode:e._GetTextureSamplingMode(t,n),wrapU:e._GetTextureWrapMode("".concat(t,"/wrapS"),n.wrapS),wrapV:e._GetTextureWrapMode("".concat(t,"/wrapT"),n.wrapT)}),n._data},e.prototype.loadImageAsync=function(e,t){if(!t._data){if(this.logOpen("".concat(e," ").concat(t.name||"")),t.uri)t._data=this.loadUriAsync("".concat(e,"/uri"),t,t.uri);else{var n=Oe.Get("".concat(e,"/bufferView"),this._gltf.bufferViews,t.bufferView);t._data=this.loadBufferViewAsync("/bufferViews/".concat(n.index),n)}this.logClose()}return t._data},e.prototype.loadUriAsync=function(t,n,r){var o=this,a=this._extensionsLoadUriAsync(t,n,r);if(a)return a;if(!e._ValidateUri(r))throw new Error("".concat(t,": '").concat(r,"' is invalid"));if((0,A.IsBase64DataUrl)(r)){var i=new Uint8Array((0,A.DecodeBase64UrlToBinary)(r));return this.log("".concat(t,": Decoded ").concat(r.substring(0,64),"... (").concat(i.length," bytes)")),Promise.resolve(i)}return this.log("".concat(t,": Loading ").concat(r)),this._parent.preprocessUrlAsync(this._rootUrl+r).then((function(e){return new Promise((function(n,a){o._parent._loadFile(o._babylonScene,e,(function(e){o._disposed||(o.log("".concat(t,": Loaded ").concat(r," (").concat(e.byteLength," bytes)")),n(new Uint8Array(e)))}),!0,(function(e){a(new A.LoadFileError("".concat(t,": Failed to load '").concat(r,"'").concat(e?": "+e.status+" "+e.statusText:""),e))}))}))}))},e.AddPointerMetadata=function(e,t){e.metadata=e.metadata||{};var n=e._internalMetadata=e._internalMetadata||{},r=n.gltf=n.gltf||{};(r.pointers=r.pointers||[]).push(t)},e._GetTextureWrapMode=function(e,t){switch(t=null==t?10497:t){case 33071:return A.Texture.CLAMP_ADDRESSMODE;case 33648:return A.Texture.MIRROR_ADDRESSMODE;case 10497:return A.Texture.WRAP_ADDRESSMODE;default:return A.Logger.Warn("".concat(e,": Invalid value (").concat(t,")")),A.Texture.WRAP_ADDRESSMODE}},e._GetTextureSamplingMode=function(e,t){var n=null==t.magFilter?9729:t.magFilter,r=null==t.minFilter?9987:t.minFilter;if(9729===n)switch(r){case 9728:return A.Texture.LINEAR_NEAREST;case 9729:return A.Texture.LINEAR_LINEAR;case 9984:return A.Texture.LINEAR_NEAREST_MIPNEAREST;case 9985:return A.Texture.LINEAR_LINEAR_MIPNEAREST;case 9986:return A.Texture.LINEAR_NEAREST_MIPLINEAR;case 9987:return A.Texture.LINEAR_LINEAR_MIPLINEAR;default:return A.Logger.Warn("".concat(e,"/minFilter: Invalid value (").concat(r,")")),A.Texture.LINEAR_LINEAR_MIPLINEAR}else switch(9728!==n&&A.Logger.Warn("".concat(e,"/magFilter: Invalid value (").concat(n,")")),r){case 9728:return A.Texture.NEAREST_NEAREST;case 9729:return A.Texture.NEAREST_LINEAR;case 9984:return A.Texture.NEAREST_NEAREST_MIPNEAREST;case 9985:return A.Texture.NEAREST_LINEAR_MIPNEAREST;case 9986:return A.Texture.NEAREST_NEAREST_MIPLINEAR;case 9987:return A.Texture.NEAREST_LINEAR_MIPLINEAR;default:return A.Logger.Warn("".concat(e,"/minFilter: Invalid value (").concat(r,")")),A.Texture.NEAREST_NEAREST_MIPNEAREST}},e._GetTypedArrayConstructor=function(e,t){switch(t){case 5120:return Int8Array;case 5121:return Uint8Array;case 5122:return Int16Array;case 5123:return Uint16Array;case 5125:return Uint32Array;case 5126:return Float32Array;default:throw new Error("".concat(e,": Invalid component type ").concat(t))}},e._GetTypedArray=function(t,n,r,o,a){var i=r.buffer;o=r.byteOffset+(o||0);var s=e._GetTypedArrayConstructor("".concat(t,"/componentType"),n),l=A.VertexBuffer.GetTypeByteLength(n);return o%l!=0?(A.Logger.Warn("".concat(t,": Copying buffer as byte offset (").concat(o,") is not a multiple of component type byte length (").concat(l,")")),new s(i.slice(o,o+a*l),0)):new s(i,o,a)},e._GetNumComponents=function(e,t){switch(t){case"SCALAR":return 1;case"VEC2":return 2;case"VEC3":return 3;case"VEC4":case"MAT2":return 4;case"MAT3":return 9;case"MAT4":return 16}throw new Error("".concat(e,": Invalid type (").concat(t,")"))},e._ValidateUri=function(e){return A.Tools.IsBase64(e)||-1===e.indexOf("..")},e._GetDrawMode=function(e,t){switch(null==t&&(t=4),t){case 0:return A.Material.PointListDrawMode;case 1:return A.Material.LineListDrawMode;case 2:return A.Material.LineLoopDrawMode;case 3:return A.Material.LineStripDrawMode;case 4:return A.Material.TriangleFillMode;case 5:return A.Material.TriangleStripDrawMode;case 6:return A.Material.TriangleFanDrawMode}throw new Error("".concat(e,": Invalid mesh primitive mode (").concat(t,")"))},e.prototype._compileMaterialsAsync=function(){var e=this;this._parent._startPerformanceCounter("Compile materials");var t=new Array;if(this._gltf.materials)for(var n=0,r=this._gltf.materials;n<r.length;n++){var o=r[n];if(o._data)for(var a in o._data)for(var i=o._data[a],s=0,l=i.babylonMeshes;s<l.length;s++){var c=l[s];c.computeWorldMatrix(!0);var u=i.babylonMaterial;t.push(u.forceCompilationAsync(c)),t.push(u.forceCompilationAsync(c,{useInstances:!0})),this._parent.useClipPlane&&(t.push(u.forceCompilationAsync(c,{clipPlane:!0})),t.push(u.forceCompilationAsync(c,{clipPlane:!0,useInstances:!0})))}}return Promise.all(t).then((function(){e._parent._endPerformanceCounter("Compile materials")}))},e.prototype._compileShadowGeneratorsAsync=function(){var e=this;this._parent._startPerformanceCounter("Compile shadow generators");for(var t=new Array,n=0,r=this._babylonScene.lights;n<r.length;n++){var o=r[n].getShadowGenerator();o&&t.push(o.forceCompilationAsync())}return Promise.all(t).then((function(){e._parent._endPerformanceCounter("Compile shadow generators")}))},e.prototype._forEachExtensions=function(e){for(var t=0,n=this._extensions;t<n.length;t++){var r=n[t];r.enabled&&e(r)}},e.prototype._applyExtensions=function(e,t,n){for(var r=0,o=this._extensions;r<o.length;r++){var a=o[r];if(a.enabled){var i="".concat(a.name,".").concat(t),s=e;s._activeLoaderExtensionFunctions=s._activeLoaderExtensionFunctions||{};var l=s._activeLoaderExtensionFunctions;if(!l[i]){l[i]=!0;try{var c=n(a);if(c)return c}finally{delete l[i]}}}}return null},e.prototype._extensionsOnLoading=function(){this._forEachExtensions((function(e){return e.onLoading&&e.onLoading()}))},e.prototype._extensionsOnReady=function(){this._forEachExtensions((function(e){return e.onReady&&e.onReady()}))},e.prototype._extensionsLoadSceneAsync=function(e,t){return this._applyExtensions(t,"loadScene",(function(n){return n.loadSceneAsync&&n.loadSceneAsync(e,t)}))},e.prototype._extensionsLoadNodeAsync=function(e,t,n){return this._applyExtensions(t,"loadNode",(function(r){return r.loadNodeAsync&&r.loadNodeAsync(e,t,n)}))},e.prototype._extensionsLoadCameraAsync=function(e,t,n){return this._applyExtensions(t,"loadCamera",(function(r){return r.loadCameraAsync&&r.loadCameraAsync(e,t,n)}))},e.prototype._extensionsLoadVertexDataAsync=function(e,t,n){return this._applyExtensions(t,"loadVertexData",(function(r){return r._loadVertexDataAsync&&r._loadVertexDataAsync(e,t,n)}))},e.prototype._extensionsLoadMeshPrimitiveAsync=function(e,t,n,r,o,a){return this._applyExtensions(o,"loadMeshPrimitive",(function(i){return i._loadMeshPrimitiveAsync&&i._loadMeshPrimitiveAsync(e,t,n,r,o,a)}))},e.prototype._extensionsLoadMaterialAsync=function(e,t,n,r,o){return this._applyExtensions(t,"loadMaterial",(function(a){return a._loadMaterialAsync&&a._loadMaterialAsync(e,t,n,r,o)}))},e.prototype._extensionsCreateMaterial=function(e,t,n){return this._applyExtensions(t,"createMaterial",(function(r){return r.createMaterial&&r.createMaterial(e,t,n)}))},e.prototype._extensionsLoadMaterialPropertiesAsync=function(e,t,n){return this._applyExtensions(t,"loadMaterialProperties",(function(r){return r.loadMaterialPropertiesAsync&&r.loadMaterialPropertiesAsync(e,t,n)}))},e.prototype._extensionsLoadTextureInfoAsync=function(e,t,n){return this._applyExtensions(t,"loadTextureInfo",(function(r){return r.loadTextureInfoAsync&&r.loadTextureInfoAsync(e,t,n)}))},e.prototype._extensionsLoadTextureAsync=function(e,t,n){return this._applyExtensions(t,"loadTexture",(function(r){return r._loadTextureAsync&&r._loadTextureAsync(e,t,n)}))},e.prototype._extensionsLoadAnimationAsync=function(e,t){return this._applyExtensions(t,"loadAnimation",(function(n){return n.loadAnimationAsync&&n.loadAnimationAsync(e,t)}))},e.prototype._extensionsLoadAnimationChannelAsync=function(e,t,n,r,o){return this._applyExtensions(n,"loadAnimationChannel",(function(a){return a._loadAnimationChannelAsync&&a._loadAnimationChannelAsync(e,t,n,r,o)}))},e.prototype._extensionsLoadSkinAsync=function(e,t,n){return this._applyExtensions(n,"loadSkin",(function(r){return r._loadSkinAsync&&r._loadSkinAsync(e,t,n)}))},e.prototype._extensionsLoadUriAsync=function(e,t,n){return this._applyExtensions(t,"loadUri",(function(r){return r._loadUriAsync&&r._loadUriAsync(e,t,n)}))},e.prototype._extensionsLoadBufferViewAsync=function(e,t){return this._applyExtensions(t,"loadBufferView",(function(n){return n.loadBufferViewAsync&&n.loadBufferViewAsync(e,t)}))},e.prototype._extensionsLoadBufferAsync=function(e,t,n,r){return this._applyExtensions(t,"loadBuffer",(function(o){return o.loadBufferAsync&&o.loadBufferAsync(e,t,n,r)}))},e.LoadExtensionAsync=function(e,t,n,r){if(!t.extensions)return null;var o=t.extensions[n];return o?r("".concat(e,"/extensions/").concat(n),o):null},e.LoadExtraAsync=function(e,t,n,r){if(!t.extras)return null;var o=t.extras[n];return o?r("".concat(e,"/extras/").concat(n),o):null},e.prototype.isExtensionUsed=function(e){return!!this._gltf.extensionsUsed&&-1!==this._gltf.extensionsUsed.indexOf(e)},e.prototype.logOpen=function(e){this._parent._logOpen(e)},e.prototype.logClose=function(){this._parent._logClose()},e.prototype.log=function(e){this._parent._log(e)},e.prototype.startPerformanceCounter=function(e){this._parent._startPerformanceCounter(e)},e.prototype.endPerformanceCounter=function(e){this._parent._endPerformanceCounter(e)},e.DefaultSampler={index:-1},e}();G._CreateGLTF2Loader=function(e){return new Ee(e)};var Me="EXT_lights_image_based",we=function(){function e(e){this.name=Me,this._loader=e,this.enabled=this._loader.isExtensionUsed(Me)}return e.prototype.dispose=function(){this._loader=null,delete this._lights},e.prototype.onLoading=function(){var e=this._loader.gltf.extensions;if(e&&e[this.name]){var t=e[this.name];this._lights=t.lights}},e.prototype.loadSceneAsync=function(e,t){var n=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(r,o){n._loader._allMaterialsDirtyRequired=!0;var a=new Array;a.push(n._loader.loadSceneAsync(e,t)),n._loader.logOpen("".concat(r));var i=Oe.Get("".concat(r,"/light"),n._lights,o.light);return a.push(n._loadLightAsync("/extensions/".concat(n.name,"/lights/").concat(o.light),i).then((function(e){n._loader.babylonScene.environmentTexture=e}))),n._loader.logClose(),Promise.all(a).then((function(){}))}))},e.prototype._loadLightAsync=function(e,t){var n=this;if(!t._loaded){var r=new Array;this._loader.logOpen("".concat(e));for(var o=new Array(t.specularImages.length),a=function(n){var a=t.specularImages[n];o[n]=new Array(a.length);for(var s=function(t){var s="".concat(e,"/specularImages/").concat(n,"/").concat(t);i._loader.logOpen("".concat(s));var l=a[t],c=Oe.Get(s,i._loader.gltf.images,l);r.push(i._loader.loadImageAsync("/images/".concat(l),c).then((function(e){o[n][t]=e}))),i._loader.logClose()},l=0;l<a.length;l++)s(l)},i=this,s=0;s<t.specularImages.length;s++)a(s);this._loader.logClose(),t._loaded=Promise.all(r).then((function(){var r=new A.RawCubeTexture(n._loader.babylonScene,null,t.specularImageSize);if(r.name=t.name||"environment",t._babylonTexture=r,null!=t.intensity&&(r.level=t.intensity),t.rotation){var a=A.Quaternion.FromArray(t.rotation);n._loader.babylonScene.useRightHandedSystem||(a=A.Quaternion.Inverse(a)),A.Matrix.FromQuaternionToRef(a,r.getReflectionTextureMatrix())}if(!t.irradianceCoefficients)throw new Error("".concat(e,": Irradiance coefficients are missing"));var i=A.SphericalHarmonics.FromArray(t.irradianceCoefficients);i.scaleInPlace(t.intensity),i.convertIrradianceToLambertianRadiance();var s=A.SphericalPolynomial.FromHarmonics(i),l=(o.length-1)/Math.log2(t.specularImageSize);return r.updateRGBDAsync(o,s,l)}))}return t._loaded.then((function(){return t._babylonTexture}))},e}();Te(Me),Ae(Me,!0,(function(e){return new we(e)}));var Ce="EXT_mesh_gpu_instancing",Le=function(){function e(e){this.name=Ce,this._loader=e,this.enabled=this._loader.isExtensionUsed(Ce)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadNodeAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(e,o){r._loader._disableInstancedMesh++;var a=r._loader.loadNodeAsync("/nodes/".concat(t.index),t,n);if(r._loader._disableInstancedMesh--,!t._primitiveBabylonMeshes)return a;var i=new Array,s=0,l=function(t){if(null!=o.attributes[t]){var n=Oe.Get("".concat(e,"/attributes/").concat(t),r._loader.gltf.accessors,o.attributes[t]);if(i.push(r._loader._loadFloatAccessorAsync("/accessors/".concat(n.bufferView),n)),0===s)s=n.count;else if(s!==n.count)throw new Error("".concat(e,"/attributes: Instance buffer accessors do not have the same count."))}else i.push(Promise.resolve(null))};return l("TRANSLATION"),l("ROTATION"),l("SCALE"),a.then((function(e){return Promise.all(i).then((function(n){var r=n[0],o=n[1],a=n[2],i=new Float32Array(16*s);A.TmpVectors.Vector3[0].copyFromFloats(0,0,0),A.TmpVectors.Quaternion[0].copyFromFloats(0,0,0,1),A.TmpVectors.Vector3[1].copyFromFloats(1,1,1);for(var l=0;l<s;++l)r&&A.Vector3.FromArrayToRef(r,3*l,A.TmpVectors.Vector3[0]),o&&A.Quaternion.FromArrayToRef(o,4*l,A.TmpVectors.Quaternion[0]),a&&A.Vector3.FromArrayToRef(a,3*l,A.TmpVectors.Vector3[1]),A.Matrix.ComposeToRef(A.TmpVectors.Vector3[1],A.TmpVectors.Quaternion[0],A.TmpVectors.Vector3[0],A.TmpVectors.Matrix[0]),A.TmpVectors.Matrix[0].copyToArray(i,16*l);for(var c=0,u=t._primitiveBabylonMeshes;c<u.length;c++)u[c].thinInstanceSetBuffer("matrix",i,16,!0);return e}))}))}))},e}();Te(Ce),Ae(Ce,!0,(function(e){return new Le(e)}));var Ne="EXT_meshopt_compression",Se=function(){function e(e){this.name=Ne,this.enabled=e.isExtensionUsed(Ne),this._loader=e}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadBufferViewAsync=function(e,t){var n=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(r,o){var a=t;if(a._meshOptData)return a._meshOptData;var i=Oe.Get("".concat(e,"/buffer"),n._loader.gltf.buffers,o.buffer);return a._meshOptData=n._loader.loadBufferAsync("/buffers/".concat(i.index),i,o.byteOffset||0,o.byteLength).then((function(e){return A.MeshoptCompression.Default.decodeGltfBufferAsync(e,o.count,o.byteStride,o.mode,o.filter)})),a._meshOptData}))},e}();Te(Ne),Ae(Ne,!0,(function(e){return new Se(e)}));var Re="EXT_texture_webp",Pe=function(){function e(e){this.name=Re,this._loader=e,this.enabled=e.isExtensionUsed(Re)}return e.prototype.dispose=function(){this._loader=null},e.prototype._loadTextureAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){var i=null==t.sampler?Ee.DefaultSampler:Oe.Get("".concat(e,"/sampler"),r._loader.gltf.samplers,t.sampler),s=Oe.Get("".concat(o,"/source"),r._loader.gltf.images,a.source);return r._loader._createTextureAsync(e,i,s,(function(e){n(e)}),void 0,!t._textureInfo.nonColorData)}))},e}();Te(Re),Ae(Re,!0,(function(e){return new Pe(e)}));var Fe="EXT_texture_avif",Ie=function(){function e(e){this.name=Fe,this._loader=e,this.enabled=e.isExtensionUsed(Fe)}return e.prototype.dispose=function(){this._loader=null},e.prototype._loadTextureAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){var i=null==t.sampler?Ee.DefaultSampler:Oe.Get("".concat(e,"/sampler"),r._loader.gltf.samplers,t.sampler),s=Oe.Get("".concat(o,"/source"),r._loader.gltf.images,a.source);return r._loader._createTextureAsync(e,i,s,(function(e){n(e)}),void 0,!t._textureInfo.nonColorData)}))},e}();Te(Fe),Ae(Fe,!0,(function(e){return new Ie(e)}));var Be="KHR_draco_mesh_compression",De=function(){function e(e){this.name=Be,this.useNormalizedFlagFromAccessor=!0,this._loader=e,this.enabled=A.DracoCompression.DecoderAvailable&&this._loader.isExtensionUsed(Be)}return e.prototype.dispose=function(){delete this.dracoCompression,this._loader=null},e.prototype._loadVertexDataAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){if(null!=t.mode&&4!==t.mode&&5!==t.mode)throw new Error("".concat(e,": Unsupported mode ").concat(t.mode));var i={},s={},l=function(e,o){var l=a.attributes[e];if(null!=l&&(n._delayInfo=n._delayInfo||[],-1===n._delayInfo.indexOf(o)&&n._delayInfo.push(o),i[o]=l,r.useNormalizedFlagFromAccessor)){var c=Oe.TryGet(r._loader.gltf.accessors,t.attributes[e]);c&&(s[o]=c.normalized||!1)}};l("POSITION",A.VertexBuffer.PositionKind),l("NORMAL",A.VertexBuffer.NormalKind),l("TANGENT",A.VertexBuffer.TangentKind),l("TEXCOORD_0",A.VertexBuffer.UVKind),l("TEXCOORD_1",A.VertexBuffer.UV2Kind),l("TEXCOORD_2",A.VertexBuffer.UV3Kind),l("TEXCOORD_3",A.VertexBuffer.UV4Kind),l("TEXCOORD_4",A.VertexBuffer.UV5Kind),l("TEXCOORD_5",A.VertexBuffer.UV6Kind),l("JOINTS_0",A.VertexBuffer.MatricesIndicesKind),l("WEIGHTS_0",A.VertexBuffer.MatricesWeightsKind),l("COLOR_0",A.VertexBuffer.ColorKind);var c=Oe.Get(o,r._loader.gltf.bufferViews,a.bufferView);return c._dracoBabylonGeometry||(c._dracoBabylonGeometry=r._loader.loadBufferViewAsync("/bufferViews/".concat(c.index),c).then((function(t){return(r.dracoCompression||A.DracoCompression.Default)._decodeMeshToGeometryForGltfAsync(n.name,r._loader.babylonScene,t,i,s).catch((function(t){throw new Error("".concat(e,": ").concat(t.message))}))}))),c._dracoBabylonGeometry}))},e}();Te(Be),Ae(Be,!0,(function(e){return new De(e)}));var Ve="KHR_lights_punctual",ke=function(){function e(e){this.name=Ve,this._loader=e,this.enabled=this._loader.isExtensionUsed(Ve)}return e.prototype.dispose=function(){this._loader=null,delete this._lights},e.prototype.onLoading=function(){var e=this._loader.gltf.extensions;if(e&&e[this.name]){var t=e[this.name];this._lights=t.lights,Oe.Assign(this._lights)}},e.prototype.loadNodeAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){return r._loader._allMaterialsDirtyRequired=!0,r._loader.loadNodeAsync(e,t,(function(e){var t,i=Oe.Get(o,r._lights,a.light),s=i.name||e.name;switch(r._loader.babylonScene._blockEntityCollection=!!r._loader._assetContainer,i.type){case"directional":var l=new A.DirectionalLight(s,A.Vector3.Backward(),r._loader.babylonScene);l.position.setAll(0),t=l;break;case"point":t=new A.PointLight(s,A.Vector3.Zero(),r._loader.babylonScene);break;case"spot":var c=new A.SpotLight(s,A.Vector3.Zero(),A.Vector3.Backward(),0,1,r._loader.babylonScene);c.angle=2*(i.spot&&i.spot.outerConeAngle||Math.PI/4),c.innerAngle=2*(i.spot&&i.spot.innerConeAngle||0),t=c;break;default:throw r._loader.babylonScene._blockEntityCollection=!1,new Error("".concat(o,": Invalid light type (").concat(i.type,")"))}t._parentContainer=r._loader._assetContainer,r._loader.babylonScene._blockEntityCollection=!1,i._babylonLight=t,t.falloffType=A.Light.FALLOFF_GLTF,t.diffuse=i.color?A.Color3.FromArray(i.color):A.Color3.White(),t.intensity=null==i.intensity?1:i.intensity,t.range=null==i.range?Number.MAX_VALUE:i.range,t.parent=e,r._loader._babylonLights.push(t),Ee.AddPointerMetadata(t,o),n(e)}))}))},e}();Te(Ve),Ae(Ve,!0,(function(e){return new ke(e)}));var Ge="KHR_materials_pbrSpecularGlossiness",Ue=function(){function e(e){this.name=Ge,this.order=200,this._loader=e,this.enabled=this._loader.isExtensionUsed(Ge)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){var i=new Array;return i.push(r._loader.loadMaterialBasePropertiesAsync(e,t,n)),i.push(r._loadSpecularGlossinessPropertiesAsync(o,a,n)),r._loader.loadMaterialAlphaProperties(e,t,n),Promise.all(i).then((function(){}))}))},e.prototype._loadSpecularGlossinessPropertiesAsync=function(e,t,n){if(!(n instanceof A.PBRMaterial))throw new Error("".concat(e,": Material type not supported"));var r=new Array;return n.metallic=null,n.roughness=null,t.diffuseFactor?(n.albedoColor=A.Color3.FromArray(t.diffuseFactor),n.alpha=t.diffuseFactor[3]):n.albedoColor=A.Color3.White(),n.reflectivityColor=t.specularFactor?A.Color3.FromArray(t.specularFactor):A.Color3.White(),n.microSurface=null==t.glossinessFactor?1:t.glossinessFactor,t.diffuseTexture&&r.push(this._loader.loadTextureInfoAsync("".concat(e,"/diffuseTexture"),t.diffuseTexture,(function(e){e.name="".concat(n.name," (Diffuse)"),n.albedoTexture=e}))),t.specularGlossinessTexture&&(r.push(this._loader.loadTextureInfoAsync("".concat(e,"/specularGlossinessTexture"),t.specularGlossinessTexture,(function(e){e.name="".concat(n.name," (Specular Glossiness)"),n.reflectivityTexture=e,n.reflectivityTexture.hasAlpha=!0}))),n.useMicroSurfaceFromReflectivityMapAlpha=!0),Promise.all(r).then((function(){}))},e}();Te(Ge),Ae(Ge,!0,(function(e){return new Ue(e)}));var He="KHR_materials_unlit",je=function(){function e(e){this.name=He,this.order=210,this._loader=e,this.enabled=this._loader.isExtensionUsed(He)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(){return r._loadUnlitPropertiesAsync(e,t,n)}))},e.prototype._loadUnlitPropertiesAsync=function(e,t,n){if(!(n instanceof A.PBRMaterial))throw new Error("".concat(e,": Material type not supported"));var r=new Array;n.unlit=!0;var o=t.pbrMetallicRoughness;return o&&(o.baseColorFactor?(n.albedoColor=A.Color3.FromArray(o.baseColorFactor),n.alpha=o.baseColorFactor[3]):n.albedoColor=A.Color3.White(),o.baseColorTexture&&r.push(this._loader.loadTextureInfoAsync("".concat(e,"/baseColorTexture"),o.baseColorTexture,(function(e){e.name="".concat(n.name," (Base Color)"),n.albedoTexture=e})))),t.doubleSided&&(n.backFaceCulling=!1,n.twoSidedLighting=!0),this._loader.loadMaterialAlphaProperties(e,t,n),Promise.all(r).then((function(){}))},e}();Te(He),Ae(He,!0,(function(e){return new je(e)}));var Ke="KHR_materials_clearcoat",We=function(){function e(e){this.name=Ke,this.order=190,this._loader=e,this.enabled=this._loader.isExtensionUsed(Ke)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){var i=new Array;return i.push(r._loader.loadMaterialPropertiesAsync(e,t,n)),i.push(r._loadClearCoatPropertiesAsync(o,a,n)),Promise.all(i).then((function(){}))}))},e.prototype._loadClearCoatPropertiesAsync=function(e,t,n){if(!(n instanceof A.PBRMaterial))throw new Error("".concat(e,": Material type not supported"));var r=new Array;return n.clearCoat.isEnabled=!0,n.clearCoat.useRoughnessFromMainTexture=!1,n.clearCoat.remapF0OnInterfaceChange=!1,null!=t.clearcoatFactor?n.clearCoat.intensity=t.clearcoatFactor:n.clearCoat.intensity=0,t.clearcoatTexture&&r.push(this._loader.loadTextureInfoAsync("".concat(e,"/clearcoatTexture"),t.clearcoatTexture,(function(e){e.name="".concat(n.name," (ClearCoat)"),n.clearCoat.texture=e}))),null!=t.clearcoatRoughnessFactor?n.clearCoat.roughness=t.clearcoatRoughnessFactor:n.clearCoat.roughness=0,t.clearcoatRoughnessTexture&&(t.clearcoatRoughnessTexture.nonColorData=!0,r.push(this._loader.loadTextureInfoAsync("".concat(e,"/clearcoatRoughnessTexture"),t.clearcoatRoughnessTexture,(function(e){e.name="".concat(n.name," (ClearCoat Roughness)"),n.clearCoat.textureRoughness=e})))),t.clearcoatNormalTexture&&(t.clearcoatNormalTexture.nonColorData=!0,r.push(this._loader.loadTextureInfoAsync("".concat(e,"/clearcoatNormalTexture"),t.clearcoatNormalTexture,(function(e){e.name="".concat(n.name," (ClearCoat Normal)"),n.clearCoat.bumpTexture=e}))),n.invertNormalMapX=!n.getScene().useRightHandedSystem,n.invertNormalMapY=n.getScene().useRightHandedSystem,null!=t.clearcoatNormalTexture.scale&&(n.clearCoat.bumpTexture.level=t.clearcoatNormalTexture.scale)),Promise.all(r).then((function(){}))},e}();Te(Ke),Ae(Ke,!0,(function(e){return new We(e)}));var Ye="KHR_materials_iridescence",qe=function(){function e(e){this.name=Ye,this.order=195,this._loader=e,this.enabled=this._loader.isExtensionUsed(Ye)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){var i=new Array;return i.push(r._loader.loadMaterialPropertiesAsync(e,t,n)),i.push(r._loadIridescencePropertiesAsync(o,a,n)),Promise.all(i).then((function(){}))}))},e.prototype._loadIridescencePropertiesAsync=function(e,t,n){var r,o,a,i,s;if(!(n instanceof A.PBRMaterial))throw new Error("".concat(e,": Material type not supported"));var l=new Array;return n.iridescence.isEnabled=!0,n.iridescence.intensity=null!==(r=t.iridescenceFactor)&&void 0!==r?r:0,n.iridescence.indexOfRefraction=null!==(a=null!==(o=t.iridescenceIor)&&void 0!==o?o:t.iridescenceIOR)&&void 0!==a?a:1.3,n.iridescence.minimumThickness=null!==(i=t.iridescenceThicknessMinimum)&&void 0!==i?i:100,n.iridescence.maximumThickness=null!==(s=t.iridescenceThicknessMaximum)&&void 0!==s?s:400,t.iridescenceTexture&&l.push(this._loader.loadTextureInfoAsync("".concat(e,"/iridescenceTexture"),t.iridescenceTexture,(function(e){e.name="".concat(n.name," (Iridescence)"),n.iridescence.texture=e}))),t.iridescenceThicknessTexture&&l.push(this._loader.loadTextureInfoAsync("".concat(e,"/iridescenceThicknessTexture"),t.iridescenceThicknessTexture,(function(e){e.name="".concat(n.name," (Iridescence Thickness)"),n.iridescence.thicknessTexture=e}))),Promise.all(l).then((function(){}))},e}();Te(Ye),Ae(Ye,!0,(function(e){return new qe(e)}));var ze="KHR_materials_anisotropy",Xe=function(){function e(e){this.name=ze,this.order=195,this._loader=e,this.enabled=this._loader.isExtensionUsed(ze)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){var i=new Array;return i.push(r._loader.loadMaterialPropertiesAsync(e,t,n)),i.push(r._loadIridescencePropertiesAsync(o,a,n)),Promise.all(i).then((function(){}))}))},e.prototype._loadIridescencePropertiesAsync=function(e,t,n){var r,o;if(!(n instanceof A.PBRMaterial))throw new Error("".concat(e,": Material type not supported"));var a=new Array;return n.anisotropy.isEnabled=!0,n.anisotropy.intensity=null!==(r=t.anisotropyStrength)&&void 0!==r?r:0,n.anisotropy.angle=null!==(o=t.anisotropyRotation)&&void 0!==o?o:0,t.anisotropyTexture&&(t.anisotropyTexture.nonColorData=!0,a.push(this._loader.loadTextureInfoAsync("".concat(e,"/anisotropyTexture"),t.anisotropyTexture,(function(e){e.name="".concat(n.name," (Anisotropy Intensity)"),n.anisotropy.texture=e})))),Promise.all(a).then((function(){}))},e}();Te(ze),Ae(ze,!0,(function(e){return new Xe(e)}));var Ze="KHR_materials_emissive_strength",Je=function(){function e(e){this.name=Ze,this.order=170,this._loader=e,this.enabled=this._loader.isExtensionUsed(Ze)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){return r._loader.loadMaterialPropertiesAsync(e,t,n).then((function(){r._loadEmissiveProperties(o,a,n)}))}))},e.prototype._loadEmissiveProperties=function(e,t,n){if(!(n instanceof A.PBRMaterial))throw new Error("".concat(e,": Material type not supported"));void 0!==t.emissiveStrength&&(n.emissiveIntensity=t.emissiveStrength)},e}();Te(Ze),Ae(Ze,!0,(function(e){return new Je(e)}));var Qe="KHR_materials_sheen",$e=function(){function e(e){this.name=Qe,this.order=190,this._loader=e,this.enabled=this._loader.isExtensionUsed(Qe)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){var i=new Array;return i.push(r._loader.loadMaterialPropertiesAsync(e,t,n)),i.push(r._loadSheenPropertiesAsync(o,a,n)),Promise.all(i).then((function(){}))}))},e.prototype._loadSheenPropertiesAsync=function(e,t,n){if(!(n instanceof A.PBRMaterial))throw new Error("".concat(e,": Material type not supported"));var r=new Array;return n.sheen.isEnabled=!0,n.sheen.intensity=1,null!=t.sheenColorFactor?n.sheen.color=A.Color3.FromArray(t.sheenColorFactor):n.sheen.color=A.Color3.Black(),t.sheenColorTexture&&r.push(this._loader.loadTextureInfoAsync("".concat(e,"/sheenColorTexture"),t.sheenColorTexture,(function(e){e.name="".concat(n.name," (Sheen Color)"),n.sheen.texture=e}))),void 0!==t.sheenRoughnessFactor?n.sheen.roughness=t.sheenRoughnessFactor:n.sheen.roughness=0,t.sheenRoughnessTexture&&(t.sheenRoughnessTexture.nonColorData=!0,r.push(this._loader.loadTextureInfoAsync("".concat(e,"/sheenRoughnessTexture"),t.sheenRoughnessTexture,(function(e){e.name="".concat(n.name," (Sheen Roughness)"),n.sheen.textureRoughness=e})))),n.sheen.albedoScaling=!0,n.sheen.useRoughnessFromMainTexture=!1,Promise.all(r).then((function(){}))},e}();Te(Qe),Ae(Qe,!0,(function(e){return new $e(e)}));var et="KHR_materials_specular",tt=function(){function e(e){this.name=et,this.order=190,this._loader=e,this.enabled=this._loader.isExtensionUsed(et)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){var i=new Array;return i.push(r._loader.loadMaterialPropertiesAsync(e,t,n)),i.push(r._loadSpecularPropertiesAsync(o,a,n)),Promise.all(i).then((function(){}))}))},e.prototype._loadSpecularPropertiesAsync=function(e,t,n){if(!(n instanceof A.PBRMaterial))throw new Error("".concat(e,": Material type not supported"));var r=new Array;return void 0!==t.specularFactor&&(n.metallicF0Factor=t.specularFactor),void 0!==t.specularColorFactor&&(n.metallicReflectanceColor=A.Color3.FromArray(t.specularColorFactor)),t.specularTexture&&(t.specularTexture.nonColorData=!0,r.push(this._loader.loadTextureInfoAsync("".concat(e,"/specularTexture"),t.specularTexture,(function(e){e.name="".concat(n.name," (Specular)"),n.metallicReflectanceTexture=e,n.useOnlyMetallicFromMetallicReflectanceTexture=!0})))),t.specularColorTexture&&r.push(this._loader.loadTextureInfoAsync("".concat(e,"/specularColorTexture"),t.specularColorTexture,(function(e){e.name="".concat(n.name," (Specular Color)"),n.reflectanceTexture=e}))),Promise.all(r).then((function(){}))},e}();Te(et),Ae(et,!0,(function(e){return new tt(e)}));var nt="KHR_materials_ior",rt=function(){function e(e){this.name=nt,this.order=180,this._loader=e,this.enabled=this._loader.isExtensionUsed(nt)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){var i=new Array;return i.push(r._loader.loadMaterialPropertiesAsync(e,t,n)),i.push(r._loadIorPropertiesAsync(o,a,n)),Promise.all(i).then((function(){}))}))},e.prototype._loadIorPropertiesAsync=function(t,n,r){if(!(r instanceof A.PBRMaterial))throw new Error("".concat(t,": Material type not supported"));return void 0!==n.ior?r.indexOfRefraction=n.ior:r.indexOfRefraction=e._DEFAULT_IOR,Promise.resolve()},e._DEFAULT_IOR=1.5,e}();Te(nt),Ae(nt,!0,(function(e){return new rt(e)}));var ot="KHR_materials_variants",at=function(){function e(e){this.name=ot,this._loader=e,this.enabled=this._loader.isExtensionUsed(ot)}return e.prototype.dispose=function(){this._loader=null},e.GetAvailableVariants=function(e){var t=this._GetExtensionMetadata(e);return t?Object.keys(t.variants):[]},e.prototype.getAvailableVariants=function(t){return e.GetAvailableVariants(t)},e.SelectVariant=function(e,t){var n=this._GetExtensionMetadata(e);if(!n)throw new Error("Cannot select variant on a glTF mesh that does not have the ".concat(ot," extension"));var r=function(e){var t=n.variants[e];if(t)for(var r=0,o=t;r<o.length;r++){var a=o[r];a.mesh.material=a.material}};if(t instanceof Array)for(var o=0,a=t;o<a.length;o++)r(a[o]);else r(t);n.lastSelected=t},e.prototype.selectVariant=function(t,n){e.SelectVariant(t,n)},e.Reset=function(e){var t=this._GetExtensionMetadata(e);if(!t)throw new Error("Cannot reset on a glTF mesh that does not have the ".concat(ot," extension"));for(var n=0,r=t.original;n<r.length;n++){var o=r[n];o.mesh.material=o.material}t.lastSelected=null},e.prototype.reset=function(t){e.Reset(t)},e.GetLastSelectedVariant=function(e){var t=this._GetExtensionMetadata(e);if(!t)throw new Error("Cannot get the last selected variant on a glTF mesh that does not have the ".concat(ot," extension"));return t.lastSelected},e.prototype.getLastSelectedVariant=function(t){return e.GetLastSelectedVariant(t)},e._GetExtensionMetadata=function(e){var t,n;return(null===(n=null===(t=null==e?void 0:e._internalMetadata)||void 0===t?void 0:t.gltf)||void 0===n?void 0:n[ot])||null},e.prototype.onLoading=function(){var e=this._loader.gltf.extensions;if(e&&e[this.name]){var t=e[this.name];this._variants=t.variants}},e.prototype._loadMeshPrimitiveAsync=function(t,n,r,o,a,i){var s=this;return Ee.LoadExtensionAsync(t,a,this.name,(function(l,c){var u=new Array;return u.push(s._loader._loadMeshPrimitiveAsync(t,n,r,o,a,(function(n){if(i(n),n instanceof A.Mesh){var r=Ee._GetDrawMode(t,a.mode),o=s._loader.rootBabylonMesh,d=o?o._internalMetadata=o._internalMetadata||{}:{},h=d.gltf=d.gltf||{},f=h[ot]=h[ot]||{lastSelected:null,original:[],variants:{}};f.original.push({mesh:n,material:n.material});for(var p=function(t){var a=c.mappings[t],i=Oe.Get("".concat(l,"/mappings/").concat(t,"/material"),s._loader.gltf.materials,a.material);u.push(s._loader._loadMaterialAsync("#/materials/".concat(a.material),i,n,r,(function(t){for(var r=function(r){var i=a.variants[r],l=Oe.Get("/extensions/".concat(ot,"/variants/").concat(i),s._variants,i);f.variants[l.name]=f.variants[l.name]||[],f.variants[l.name].push({mesh:n,material:t}),n.onClonedObservable.add((function(t){var r=t,a=null,i=r;do{if(!(i=i.parent))return;a=e._GetExtensionMetadata(i)}while(null===a);if(o&&a===e._GetExtensionMetadata(o)){for(var s in i._internalMetadata={},o._internalMetadata)i._internalMetadata[s]=o._internalMetadata[s];for(var s in i._internalMetadata.gltf=[],o._internalMetadata.gltf)i._internalMetadata.gltf[s]=o._internalMetadata.gltf[s];i._internalMetadata.gltf[ot]={lastSelected:null,original:[],variants:{}};for(var c=0,u=a.original;c<u.length;c++){var d=u[c];i._internalMetadata.gltf[ot].original.push({mesh:d.mesh,material:d.material})}for(var s in a.variants)if(Object.prototype.hasOwnProperty.call(a.variants,s)){i._internalMetadata.gltf[ot].variants[s]=[];for(var h=0,f=a.variants[s];h<f.length;h++){var p=f[h];i._internalMetadata.gltf[ot].variants[s].push({mesh:p.mesh,material:p.material})}}a=i._internalMetadata.gltf[ot]}for(var _=0,m=a.original;_<m.length;_++)(b=m[_]).mesh===n&&(b.mesh=r);for(var y=0,v=a.variants[l.name];y<v.length;y++){var b;(b=v[y]).mesh===n&&(b.mesh=r)}}))},i=0;i<a.variants.length;++i)r(i)})))},_=0;_<c.mappings.length;++_)p(_)}}))),Promise.all(u).then((function(e){return e[0]}))}))},e}();Te(ot),Ae(ot,!0,(function(e){return new at(e)}));var it=function(){function e(t,n){var r=this;this._opaqueRenderTarget=null,this._opaqueMeshesCache=[],this._transparentMeshesCache=[],this._materialObservers={},this._options=v(v({},e._GetDefaultOptions()),t),this._scene=n,this._scene._transmissionHelper=this,this.onErrorObservable=new A.Observable,this._scene.onDisposeObservable.addOnce((function(){r.dispose()})),this._parseScene(),this._setupRenderTargets()}return e._GetDefaultOptions=function(){return{renderSize:1024,samples:4,lodGenerationScale:1,lodGenerationOffset:-4,renderTargetTextureType:A.Constants.TEXTURETYPE_HALF_FLOAT,generateMipmaps:!0}},e.prototype.updateOptions=function(e){var t=this,n=Object.keys(e).filter((function(n){return t._options[n]!==e[n]}));if(n.length){var r=v(v({},this._options),e),o=this._options;this._options=r,r.renderSize===o.renderSize&&r.renderTargetTextureType===o.renderTargetTextureType&&r.generateMipmaps===o.generateMipmaps&&this._opaqueRenderTarget?(this._opaqueRenderTarget.samples=r.samples,this._opaqueRenderTarget.lodGenerationScale=r.lodGenerationScale,this._opaqueRenderTarget.lodGenerationOffset=r.lodGenerationOffset):this._setupRenderTargets()}},e.prototype.getOpaqueTarget=function(){return this._opaqueRenderTarget},e.prototype._shouldRenderAsTransmission=function(e){return!!e&&!!(e instanceof A.PBRMaterial&&e.subSurface.isRefractionEnabled)},e.prototype._addMesh=function(e){var t=this;this._materialObservers[e.uniqueId]=e.onMaterialChangedObservable.add(this._onMeshMaterialChanged.bind(this)),A.Tools.SetImmediate((function(){t._shouldRenderAsTransmission(e.material)?(e.material.refractionTexture=t._opaqueRenderTarget,-1===t._transparentMeshesCache.indexOf(e)&&t._transparentMeshesCache.push(e)):-1===t._opaqueMeshesCache.indexOf(e)&&t._opaqueMeshesCache.push(e)}))},e.prototype._removeMesh=function(e){e.onMaterialChangedObservable.remove(this._materialObservers[e.uniqueId]),delete this._materialObservers[e.uniqueId];var t=this._transparentMeshesCache.indexOf(e);-1!==t&&this._transparentMeshesCache.splice(t,1),-1!==(t=this._opaqueMeshesCache.indexOf(e))&&this._opaqueMeshesCache.splice(t,1)},e.prototype._parseScene=function(){this._scene.meshes.forEach(this._addMesh.bind(this)),this._scene.onNewMeshAddedObservable.add(this._addMesh.bind(this)),this._scene.onMeshRemovedObservable.add(this._removeMesh.bind(this))},e.prototype._onMeshMaterialChanged=function(e){var t=this._transparentMeshesCache.indexOf(e),n=this._opaqueMeshesCache.indexOf(e);this._shouldRenderAsTransmission(e.material)?(e.material instanceof A.PBRMaterial&&(e.material.subSurface.refractionTexture=this._opaqueRenderTarget),-1!==n?(this._opaqueMeshesCache.splice(n,1),this._transparentMeshesCache.push(e)):-1===t&&this._transparentMeshesCache.push(e)):-1!==t?(this._transparentMeshesCache.splice(t,1),this._opaqueMeshesCache.push(e)):-1===n&&this._opaqueMeshesCache.push(e)},e.prototype._isRenderTargetValid=function(){var e;return null!==(null===(e=this._opaqueRenderTarget)||void 0===e?void 0:e.getInternalTexture())},e.prototype._setupRenderTargets=function(){var e,t,n,r,o=this;this._opaqueRenderTarget&&this._opaqueRenderTarget.dispose(),this._opaqueRenderTarget=new A.RenderTargetTexture("opaqueSceneTexture",this._options.renderSize,this._scene,this._options.generateMipmaps,void 0,this._options.renderTargetTextureType),this._opaqueRenderTarget.ignoreCameraViewport=!0,this._opaqueRenderTarget.renderList=this._opaqueMeshesCache,this._opaqueRenderTarget.clearColor=null!==(t=null===(e=this._options.clearColor)||void 0===e?void 0:e.clone())&&void 0!==t?t:this._scene.clearColor.clone(),this._opaqueRenderTarget.gammaSpace=!1,this._opaqueRenderTarget.lodGenerationScale=this._options.lodGenerationScale,this._opaqueRenderTarget.lodGenerationOffset=this._options.lodGenerationOffset,this._opaqueRenderTarget.samples=this._options.samples,this._opaqueRenderTarget.renderSprites=!0,this._opaqueRenderTarget.renderParticles=!0,this._opaqueRenderTarget.onBeforeBindObservable.add((function(e){r=o._scene.environmentIntensity,o._scene.environmentIntensity=1,n=o._scene.imageProcessingConfiguration.applyByPostProcess,o._options.clearColor?e.clearColor.copyFrom(o._options.clearColor):o._scene.clearColor.toLinearSpaceToRef(e.clearColor,o._scene.getEngine().useExactSrgbConversions),o._scene.imageProcessingConfiguration._applyByPostProcess=!0})),this._opaqueRenderTarget.onAfterUnbindObservable.add((function(){o._scene.environmentIntensity=r,o._scene.imageProcessingConfiguration._applyByPostProcess=n})),this._transparentMeshesCache.forEach((function(e){o._shouldRenderAsTransmission(e.material)&&(e.material.refractionTexture=o._opaqueRenderTarget)}))},e.prototype.dispose=function(){this._scene._transmissionHelper=void 0,this._opaqueRenderTarget&&(this._opaqueRenderTarget.dispose(),this._opaqueRenderTarget=null),this._transparentMeshesCache=[],this._opaqueMeshesCache=[]},e}(),st="KHR_materials_transmission",lt=function(){function e(e){this.name=st,this.order=175,this._loader=e,this.enabled=this._loader.isExtensionUsed(st),this.enabled&&(e.parent.transparencyAsCoverage=!0)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){var i=new Array;return i.push(r._loader.loadMaterialPropertiesAsync(e,t,n)),i.push(r._loadTransparentPropertiesAsync(o,t,n,a)),Promise.all(i).then((function(){}))}))},e.prototype._loadTransparentPropertiesAsync=function(e,t,n,r){var o,a;if(!(n instanceof A.PBRMaterial))throw new Error("".concat(e,": Material type not supported"));var i=n;if(i.subSurface.isRefractionEnabled=!0,i.subSurface.volumeIndexOfRefraction=1,i.subSurface.useAlbedoToTintRefraction=!0,void 0===r.transmissionFactor)return i.subSurface.refractionIntensity=0,i.subSurface.isRefractionEnabled=!1,Promise.resolve();i.subSurface.refractionIntensity=r.transmissionFactor;var s=i.getScene();return i.subSurface.refractionIntensity&&!s._transmissionHelper?new it({},i.getScene()):i.subSurface.refractionIntensity&&!(null===(o=s._transmissionHelper)||void 0===o?void 0:o._isRenderTargetValid())&&(null===(a=s._transmissionHelper)||void 0===a||a._setupRenderTargets()),i.subSurface.minimumThickness=0,i.subSurface.maximumThickness=0,r.transmissionTexture?(r.transmissionTexture.nonColorData=!0,this._loader.loadTextureInfoAsync("".concat(e,"/transmissionTexture"),r.transmissionTexture,void 0).then((function(e){e.name="".concat(n.name," (Transmission)"),i.subSurface.refractionIntensityTexture=e,i.subSurface.useGltfStyleTextures=!0}))):Promise.resolve()},e}();Te(st),Ae(st,!0,(function(e){return new lt(e)}));var ct="KHR_materials_diffuse_transmission",ut=function(){function e(e){this.name=ct,this.order=174,this._loader=e,this.enabled=this._loader.isExtensionUsed(ct),this.enabled&&(e.parent.transparencyAsCoverage=!0)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){var i=new Array;return i.push(r._loader.loadMaterialPropertiesAsync(e,t,n)),i.push(r._loadTranslucentPropertiesAsync(o,t,n,a)),Promise.all(i).then((function(){}))}))},e.prototype._loadTranslucentPropertiesAsync=function(e,t,n,r){if(!(n instanceof A.PBRMaterial))throw new Error("".concat(e,": Material type not supported"));var o=n;if(o.subSurface.isTranslucencyEnabled=!0,o.subSurface.volumeIndexOfRefraction=1,o.subSurface.minimumThickness=0,o.subSurface.maximumThickness=0,o.subSurface.useAlbedoToTintTranslucency=!1,void 0===r.diffuseTransmissionFactor)return o.subSurface.translucencyIntensity=0,o.subSurface.isTranslucencyEnabled=!1,Promise.resolve();o.subSurface.translucencyIntensity=r.diffuseTransmissionFactor;var a=new Array;return o.subSurface.useGltfStyleTextures=!0,r.diffuseTransmissionTexture&&(r.diffuseTransmissionTexture.nonColorData=!0,a.push(this._loader.loadTextureInfoAsync("".concat(e,"/diffuseTransmissionTexture"),r.diffuseTransmissionTexture).then((function(e){e.name="".concat(n.name," (Diffuse Transmission)"),o.subSurface.translucencyIntensityTexture=e})))),void 0!==r.diffuseTransmissionColorFactor?o.subSurface.translucencyColor=A.Color3.FromArray(r.diffuseTransmissionColorFactor):o.subSurface.translucencyColor=A.Color3.White(),r.diffuseTransmissionColorTexture&&a.push(this._loader.loadTextureInfoAsync("".concat(e,"/diffuseTransmissionColorTexture"),r.diffuseTransmissionColorTexture).then((function(e){e.name="".concat(n.name," (Diffuse Transmission Color)"),o.subSurface.translucencyColorTexture=e}))),Promise.all(a).then((function(){}))},e}();Te(ct),Ae(ct,!0,(function(e){return new ut(e)}));var dt="KHR_materials_volume",ht=function(){function e(e){this.name=dt,this.order=173,this._loader=e,this.enabled=this._loader.isExtensionUsed(dt),this.enabled&&this._loader._disableInstancedMesh++}return e.prototype.dispose=function(){this.enabled&&this._loader._disableInstancedMesh--,this._loader=null},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){var i=new Array;return i.push(r._loader.loadMaterialPropertiesAsync(e,t,n)),i.push(r._loadVolumePropertiesAsync(o,t,n,a)),Promise.all(i).then((function(){}))}))},e.prototype._loadVolumePropertiesAsync=function(e,t,n,r){if(!(n instanceof A.PBRMaterial))throw new Error("".concat(e,": Material type not supported"));if(!n.subSurface.isRefractionEnabled&&!n.subSurface.isTranslucencyEnabled||!r.thicknessFactor)return Promise.resolve();n.subSurface.volumeIndexOfRefraction=n.indexOfRefraction;var o=void 0!==r.attenuationDistance?r.attenuationDistance:Number.MAX_VALUE;return n.subSurface.tintColorAtDistance=o,void 0!==r.attenuationColor&&3==r.attenuationColor.length&&n.subSurface.tintColor.copyFromFloats(r.attenuationColor[0],r.attenuationColor[1],r.attenuationColor[2]),n.subSurface.minimumThickness=0,n.subSurface.maximumThickness=r.thicknessFactor,n.subSurface.useThicknessAsDepth=!0,r.thicknessTexture?(r.thicknessTexture.nonColorData=!0,this._loader.loadTextureInfoAsync("".concat(e,"/thicknessTexture"),r.thicknessTexture).then((function(e){e.name="".concat(n.name," (Thickness)"),n.subSurface.thicknessTexture=e,n.subSurface.useGltfStyleTextures=!0}))):Promise.resolve()},e}();Te(dt),Ae(dt,!0,(function(e){return new ht(e)}));var ft="KHR_materials_dispersion",pt=function(){function e(e){this.name=ft,this.order=174,this._loader=e,this.enabled=this._loader.isExtensionUsed(ft)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){var i=new Array;return i.push(r._loader.loadMaterialPropertiesAsync(e,t,n)),i.push(r._loadDispersionPropertiesAsync(o,t,n,a)),Promise.all(i).then((function(){}))}))},e.prototype._loadDispersionPropertiesAsync=function(e,t,n,r){if(!(n instanceof A.PBRMaterial))throw new Error("".concat(e,": Material type not supported"));return n.subSurface.isRefractionEnabled&&r.dispersion?(n.subSurface.isDispersionEnabled=!0,n.subSurface.dispersion=r.dispersion,Promise.resolve()):Promise.resolve()},e}();Te(ft),Ae(ft,!0,(function(e){return new pt(e)}));var _t="KHR_mesh_quantization",mt=function(){function e(e){this.name=_t,this.enabled=e.isExtensionUsed(_t)}return e.prototype.dispose=function(){},e}();Te(_t),Ae(_t,!0,(function(e){return new mt(e)}));var yt="KHR_texture_basisu",vt=function(){function e(e){this.name=yt,this._loader=e,this.enabled=e.isExtensionUsed(yt)}return e.prototype.dispose=function(){this._loader=null},e.prototype._loadTextureAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){var i=null==t.sampler?Ee.DefaultSampler:Oe.Get("".concat(e,"/sampler"),r._loader.gltf.samplers,t.sampler),s=Oe.Get("".concat(o,"/source"),r._loader.gltf.images,a.source);return r._loader._createTextureAsync(e,i,s,(function(e){n(e)}),t._textureInfo.nonColorData?{useRGBAIfASTCBC7NotAvailableWhenUASTC:!0}:void 0,!t._textureInfo.nonColorData)}))},e}();Te(yt),Ae(yt,!0,(function(e){return new vt(e)}));var bt="KHR_texture_transform",gt=function(){function e(e){this.name=bt,this._loader=e,this.enabled=this._loader.isExtensionUsed(bt)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadTextureInfoAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(o,a){return r._loader.loadTextureInfoAsync(e,t,(function(e){if(!(e instanceof A.Texture))throw new Error("".concat(o,": Texture type not supported"));a.offset&&(e.uOffset=a.offset[0],e.vOffset=a.offset[1]),e.uRotationCenter=0,e.vRotationCenter=0,a.rotation&&(e.wAng=-a.rotation),a.scale&&(e.uScale=a.scale[0],e.vScale=a.scale[1]),null!=a.texCoord&&(e.coordinatesIndex=a.texCoord),n(e)}))}))},e}();Te(bt),Ae(bt,!0,(function(e){return new gt(e)}));var At="KHR_xmp_json_ld",Tt=function(){function e(e){this.name=At,this.order=100,this._loader=e,this.enabled=this._loader.isExtensionUsed(At)}return e.prototype.dispose=function(){this._loader=null},e.prototype.onLoading=function(){var e,t,n;if(null!==this._loader.rootBabylonMesh){var r=null===(e=this._loader.gltf.extensions)||void 0===e?void 0:e.KHR_xmp_json_ld,o=null===(n=null===(t=this._loader.gltf.asset)||void 0===t?void 0:t.extensions)||void 0===n?void 0:n.KHR_xmp_json_ld;if(r&&o){var a=+o.packet;r.packets&&a<r.packets.length&&(this._loader.rootBabylonMesh.metadata=this._loader.rootBabylonMesh.metadata||{},this._loader.rootBabylonMesh.metadata.xmp=r.packets[a])}}},e}();function xt(e,t,n,r){return A.Color3.FromArray(t,n).scale(r)}function Ot(e,t,n,r){return t[n]*r}function Et(e,t,n,r){return-t[n]*r}function Mt(e,t,n,r){return t[n+1]*r}function wt(e,t,n,r){return t[n]*r*2}function Ct(e){return{scale:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"".concat(e,".uScale"),Ot,(function(){return 2})),new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"".concat(e,".vScale"),Mt,(function(){return 2}))],offset:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"".concat(e,".uOffset"),Ot,(function(){return 2})),new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"".concat(e,".vOffset"),Mt,(function(){return 2}))],rotation:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"".concat(e,".wAng"),Et,(function(){return 1}))]}}Te(At),Ae(At,!0,(function(e){return new Tt(e)}));var Lt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return y(t,e),t.prototype.buildAnimations=function(e,t,n,r,o){o(e._babylonCamera,this._buildAnimation(t,n,r))},t}(_e),Nt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return y(t,e),t.prototype.buildAnimations=function(e,t,n,r,o){for(var a in e._data)o(e._data[a].babylonMaterial,this._buildAnimation(t,n,r))},t}(_e),St=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return y(t,e),t.prototype.buildAnimations=function(e,t,n,r,o){o(e._babylonLight,this._buildAnimation(t,n,r))},t}(_e),Rt={__array__:v({__target__:!0},ve)},Pt={__array__:{__target__:!0,orthographic:{xmag:[new Lt(A.Animation.ANIMATIONTYPE_FLOAT,"orthoLeft",Et,(function(){return 1})),new Lt(A.Animation.ANIMATIONTYPE_FLOAT,"orthoRight",Mt,(function(){return 1}))],ymag:[new Lt(A.Animation.ANIMATIONTYPE_FLOAT,"orthoBottom",Et,(function(){return 1})),new Lt(A.Animation.ANIMATIONTYPE_FLOAT,"orthoTop",Mt,(function(){return 1}))],zfar:[new Lt(A.Animation.ANIMATIONTYPE_FLOAT,"maxZ",Ot,(function(){return 1}))],znear:[new Lt(A.Animation.ANIMATIONTYPE_FLOAT,"minZ",Ot,(function(){return 1}))]},perspective:{yfov:[new Lt(A.Animation.ANIMATIONTYPE_FLOAT,"fov",Ot,(function(){return 1}))],zfar:[new Lt(A.Animation.ANIMATIONTYPE_FLOAT,"maxZ",Ot,(function(){return 1}))],znear:[new Lt(A.Animation.ANIMATIONTYPE_FLOAT,"minZ",Ot,(function(){return 1}))]}}},Ft={nodes:Rt,materials:{__array__:{__target__:!0,pbrMetallicRoughness:{baseColorFactor:[new Nt(A.Animation.ANIMATIONTYPE_COLOR3,"albedoColor",xt,(function(){return 4})),new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"alpha",(function(e,t,n,r){return t[n+3]*r}),(function(){return 4}))],metallicFactor:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"metallic",Ot,(function(){return 1}))],roughnessFactor:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"roughness",Ot,(function(){return 1}))],baseColorTexture:{extensions:{KHR_texture_transform:Ct("albedoTexture")}},metallicRoughnessTexture:{extensions:{KHR_texture_transform:Ct("metallicTexture")}}},emissiveFactor:[new Nt(A.Animation.ANIMATIONTYPE_COLOR3,"emissiveColor",xt,(function(){return 3}))],normalTexture:{scale:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"bumpTexture.level",Ot,(function(){return 1}))],extensions:{KHR_texture_transform:Ct("bumpTexture")}},occlusionTexture:{strength:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"ambientTextureStrength",Ot,(function(){return 1}))],extensions:{KHR_texture_transform:Ct("ambientTexture")}},emissiveTexture:{extensions:{KHR_texture_transform:Ct("emissiveTexture")}},extensions:{KHR_materials_anisotropy:{anisotropyStrength:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"anisotropy.intensity",Ot,(function(){return 1}))],anisotropyRotation:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"anisotropy.angle",Ot,(function(){return 1}))],anisotropyTexture:{extensions:{KHR_texture_transform:Ct("anisotropy.texture")}}},KHR_materials_clearcoat:{clearcoatFactor:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"clearCoat.intensity",Ot,(function(){return 1}))],clearcoatRoughnessFactor:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"clearCoat.roughness",Ot,(function(){return 1}))],clearcoatTexture:{extensions:{KHR_texture_transform:Ct("clearCoat.texture")}},clearcoatNormalTexture:{scale:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"clearCoat.bumpTexture.level",Ot,(function(){return 1}))],extensions:{KHR_texture_transform:Ct("clearCoat.bumpTexture")}},clearcoatRoughnessTexture:{extensions:{KHR_texture_transform:Ct("clearCoat.textureRoughness")}}},KHR_materials_dispersion:{dispersion:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"subSurface.dispersion",Ot,(function(){return 1}))]},KHR_materials_emissive_strength:{emissiveStrength:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"emissiveIntensity",Ot,(function(){return 1}))]},KHR_materials_ior:{ior:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"indexOfRefraction",Ot,(function(){return 1}))]},KHR_materials_iridescence:{iridescenceFactor:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"iridescence.intensity",Ot,(function(){return 1}))],iridescenceIor:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"iridescence.indexOfRefraction",Ot,(function(){return 1}))],iridescenceThicknessMinimum:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"iridescence.minimumThickness",Ot,(function(){return 1}))],iridescenceThicknessMaximum:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"iridescence.maximumThickness",Ot,(function(){return 1}))],iridescenceTexture:{extensions:{KHR_texture_transform:Ct("iridescence.texture")}},iridescenceThicknessTexture:{extensions:{KHR_texture_transform:Ct("iridescence.thicknessTexture")}}},KHR_materials_sheen:{sheenColorFactor:[new Nt(A.Animation.ANIMATIONTYPE_COLOR3,"sheen.color",xt,(function(){return 3}))],sheenRoughnessFactor:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"sheen.roughness",Ot,(function(){return 1}))],sheenColorTexture:{extensions:{KHR_texture_transform:Ct("sheen.texture")}},sheenRoughnessTexture:{extensions:{KHR_texture_transform:Ct("sheen.textureRoughness")}}},KHR_materials_specular:{specularFactor:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"metallicF0Factor",Ot,(function(){return 1}))],specularColorFactor:[new Nt(A.Animation.ANIMATIONTYPE_COLOR3,"metallicReflectanceColor",xt,(function(){return 3}))],specularTexture:{extensions:{KHR_texture_transform:Ct("metallicReflectanceTexture")}},specularColorTexture:{extensions:{KHR_texture_transform:Ct("reflectanceTexture")}}},KHR_materials_transmission:{transmissionFactor:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"subSurface.refractionIntensity",Ot,(function(){return 1}))],transmissionTexture:{extensions:{KHR_texture_transform:Ct("subSurface.refractionIntensityTexture")}}},KHR_materials_volume:{attenuationColor:[new Nt(A.Animation.ANIMATIONTYPE_COLOR3,"subSurface.tintColor",xt,(function(){return 3}))],attenuationDistance:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"subSurface.tintColorAtDistance",Ot,(function(){return 1}))],thicknessFactor:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"subSurface.maximumThickness",Ot,(function(){return 1}))],thicknessTexture:{extensions:{KHR_texture_transform:Ct("subSurface.thicknessTexture")}}},KHR_materials_diffuse_transmission:{diffuseTransmissionFactor:[new Nt(A.Animation.ANIMATIONTYPE_FLOAT,"subSurface.translucencyIntensity",Ot,(function(){return 1}))],diffuseTransmissionTexture:{extensions:{KHR_texture_transform:Ct("subSurface.translucencyIntensityTexture")}},diffuseTransmissionColorFactor:[new Nt(A.Animation.ANIMATIONTYPE_COLOR3,"subSurface.translucencyColor",xt,(function(){return 3}))],diffuseTransmissionColorTexture:{extensions:{KHR_texture_transform:Ct("subSurface.translucencyColorTexture")}}}}}},cameras:Pt,extensions:{KHR_lights_punctual:{lights:{__array__:{__target__:!0,color:[new St(A.Animation.ANIMATIONTYPE_COLOR3,"diffuse",xt,(function(){return 3}))],intensity:[new St(A.Animation.ANIMATIONTYPE_FLOAT,"intensity",Ot,(function(){return 1}))],range:[new St(A.Animation.ANIMATIONTYPE_FLOAT,"range",Ot,(function(){return 1}))],spot:{innerConeAngle:[new St(A.Animation.ANIMATIONTYPE_FLOAT,"innerAngle",wt,(function(){return 1}))],outerConeAngle:[new St(A.Animation.ANIMATIONTYPE_FLOAT,"angle",wt,(function(){return 1}))]}}}}}},It=function(){function e(e,t){this._gltf=e,this._infoTree=t}return e.prototype.convert=function(e){var t=this._gltf,n=this._infoTree,r=void 0;if(!e.startsWith("/"))throw new Error("Path must start with a /");var o=e.split("/");o.shift();for(var a=0,i=o;a<i.length;a++){var s=i[a];if(n.__array__)n=n.__array__;else if(!(n=n[s]))throw new Error("Path ".concat(e," is invalid"));if(void 0===t)throw new Error("Path ".concat(e," is invalid"));t=t[s],n.__target__&&(r=t)}return{object:r,info:n}},e}(),Bt="KHR_animation_pointer",Dt=function(e){function t(t){return e.call(this,t,Ft)||this}return y(t,e),t}(It),Vt=function(){function e(e){this.name=Bt,this._loader=e,this._pathToObjectConverter=new Dt(this._loader.gltf)}return Object.defineProperty(e.prototype,"enabled",{get:function(){return this._loader.isExtensionUsed(Bt)},enumerable:!1,configurable:!0}),e.prototype.dispose=function(){this._loader=null,delete this._pathToObjectConverter},e.prototype._loadAnimationChannelAsync=function(e,t,n,r,o){var a,i=null===(a=r.target.extensions)||void 0===a?void 0:a.KHR_animation_pointer;if(!i||!this._pathToObjectConverter)return null;"pointer"!==r.target.path&&A.Logger.Warn("".concat(e,"/target/path: Value (").concat(r.target.path,") must be (").concat("pointer",") when using the ").concat(this.name," extension")),null!=r.target.node&&A.Logger.Warn("".concat(e,"/target/node: Value (").concat(r.target.node,") must not be present when using the ").concat(this.name," extension"));var s="".concat(e,"/extensions/").concat(this.name),l=i.pointer;if(!l)throw new Error("".concat(s,": Pointer is missing"));try{var c=this._pathToObjectConverter.convert(l);return this._loader._loadAnimationChannelFromTargetInfoAsync(e,t,n,r,c,o)}catch(e){return A.Logger.Warn("".concat(s,"/pointer: Invalid pointer (").concat(l,") skipped")),null}},e}();Te(Bt),Ae(Bt,!0,(function(e){return new Vt(e)}));var kt="MSFT_audio_emitter",Gt=function(){function e(e){this.name=kt,this._loader=e,this.enabled=this._loader.isExtensionUsed(kt)}return e.prototype.dispose=function(){this._loader=null,this._clips=null,this._emitters=null},e.prototype.onLoading=function(){var e=this._loader.gltf.extensions;if(e&&e[this.name]){var t=e[this.name];this._clips=t.clips,this._emitters=t.emitters,Oe.Assign(this._clips),Oe.Assign(this._emitters)}},e.prototype.loadSceneAsync=function(e,t){var n=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(r,o){var a=new Array;a.push(n._loader.loadSceneAsync(e,t));for(var i=0,s=o.emitters;i<s.length;i++){var l=s[i],c=Oe.Get("".concat(r,"/emitters"),n._emitters,l);if(null!=c.refDistance||null!=c.maxDistance||null!=c.rolloffFactor||null!=c.distanceModel||null!=c.innerAngle||null!=c.outerAngle)throw new Error("".concat(r,": Direction or Distance properties are not allowed on emitters attached to a scene"));a.push(n._loadEmitterAsync("".concat(r,"/emitters/").concat(c.index),c))}return Promise.all(a).then((function(){}))}))},e.prototype.loadNodeAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(e,o){var a=new Array;return r._loader.loadNodeAsync(e,t,(function(t){for(var i=function(n){var o=Oe.Get("".concat(e,"/emitters"),r._emitters,n);a.push(r._loadEmitterAsync("".concat(e,"/emitters/").concat(o.index),o).then((function(){for(var e=0,n=o._babylonSounds;e<n.length;e++){var r=n[e];r.attachToMesh(t),null==o.innerAngle&&null==o.outerAngle||(r.setLocalDirectionToMesh(A.Vector3.Forward()),r.setDirectionalCone(2*A.Tools.ToDegrees(null==o.innerAngle?Math.PI:o.innerAngle),2*A.Tools.ToDegrees(null==o.outerAngle?Math.PI:o.outerAngle),0))}})))},s=0,l=o.emitters;s<l.length;s++)i(l[s]);n(t)})).then((function(e){return Promise.all(a).then((function(){return e}))}))}))},e.prototype.loadAnimationAsync=function(e,t){var n=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(r,o){return n._loader.loadAnimationAsync(e,t).then((function(a){var i=new Array;Oe.Assign(o.events);for(var s=0,l=o.events;s<l.length;s++){var c=l[s];i.push(n._loadAnimationEventAsync("".concat(r,"/events/").concat(c.index),e,t,c,a))}return Promise.all(i).then((function(){return a}))}))}))},e.prototype._loadClipAsync=function(e,t){if(t._objectURL)return t._objectURL;var n;if(t.uri)n=this._loader.loadUriAsync(e,t,t.uri);else{var r=Oe.Get("".concat(e,"/bufferView"),this._loader.gltf.bufferViews,t.bufferView);n=this._loader.loadBufferViewAsync("/bufferViews/".concat(r.index),r)}return t._objectURL=n.then((function(e){return URL.createObjectURL(new Blob([e],{type:t.mimeType}))})),t._objectURL},e.prototype._loadEmitterAsync=function(e,t){var n=this;if(t._babylonSounds=t._babylonSounds||[],!t._babylonData){for(var r=new Array,o=t.name||"emitter".concat(t.index),a={loop:!1,autoplay:!1,volume:null==t.volume?1:t.volume},i=function(e){var i="/extensions/".concat(s.name,"/clips"),l=Oe.Get(i,s._clips,t.clips[e].clip);r.push(s._loadClipAsync("".concat(i,"/").concat(t.clips[e].clip),l).then((function(r){var i=t._babylonSounds[e]=new A.Sound(o,r,n._loader.babylonScene,null,a);i.refDistance=t.refDistance||1,i.maxDistance=t.maxDistance||256,i.rolloffFactor=t.rolloffFactor||1,i.distanceModel=t.distanceModel||"exponential"})))},s=this,l=0;l<t.clips.length;l++)i(l);var c=Promise.all(r).then((function(){var e=t.clips.map((function(e){return e.weight||1})),n=new A.WeightedSound(t.loop||!1,t._babylonSounds,e);t.innerAngle&&(n.directionalConeInnerAngle=2*A.Tools.ToDegrees(t.innerAngle)),t.outerAngle&&(n.directionalConeOuterAngle=2*A.Tools.ToDegrees(t.outerAngle)),t.volume&&(n.volume=t.volume),t._babylonData.sound=n}));t._babylonData={loaded:c}}return t._babylonData.loaded},e.prototype._getEventAction=function(e,t,n,r,o){switch(n){case"play":return function(e){var n=(o||0)+(e-r);t.play(n)};case"stop":return function(){t.stop()};case"pause":return function(){t.pause()};default:throw new Error("".concat(e,": Unsupported action ").concat(n))}},e.prototype._loadAnimationEventAsync=function(e,t,n,r,o){var a=this;if(0==o.targetedAnimations.length)return Promise.resolve();var i=o.targetedAnimations[0],s=r.emitter,l=Oe.Get("/extensions/".concat(this.name,"/emitters"),this._emitters,s);return this._loadEmitterAsync(e,l).then((function(){var t=l._babylonData.sound;if(t){var n=new A.AnimationEvent(r.time,a._getEventAction(e,t,r.action,r.time,r.startOffset));i.animation.addEvent(n),o.onAnimationGroupEndObservable.add((function(){t.stop()})),o.onAnimationGroupPauseObservable.add((function(){t.pause()}))}}))},e}();Te(kt),Ae(kt,!0,(function(e){return new Gt(e)}));var Ut="MSFT_lod",Ht=function(){function e(e){var t,n;this.name=Ut,this.order=100,this.maxLODsToLoad=10,this.onNodeLODsLoadedObservable=new A.Observable,this.onMaterialLODsLoadedObservable=new A.Observable,this._bufferLODs=new Array,this._nodeIndexLOD=null,this._nodeSignalLODs=new Array,this._nodePromiseLODs=new Array,this._nodeBufferLODs=new Array,this._materialIndexLOD=null,this._materialSignalLODs=new Array,this._materialPromiseLODs=new Array,this._materialBufferLODs=new Array,this._loader=e,this.maxLODsToLoad=null!==(n=null===(t=this._loader.parent.extensionOptions[Ut])||void 0===t?void 0:t.maxLODsToLoad)&&void 0!==n?n:this.maxLODsToLoad,this.enabled=this._loader.isExtensionUsed(Ut)}return e.prototype.dispose=function(){this._loader=null,this._nodeIndexLOD=null,this._nodeSignalLODs.length=0,this._nodePromiseLODs.length=0,this._nodeBufferLODs.length=0,this._materialIndexLOD=null,this._materialSignalLODs.length=0,this._materialPromiseLODs.length=0,this._materialBufferLODs.length=0,this.onMaterialLODsLoadedObservable.clear(),this.onNodeLODsLoadedObservable.clear()},e.prototype.onReady=function(){for(var e=this,t=function(t){var r=Promise.all(n._nodePromiseLODs[t]).then((function(){0!==t&&(e._loader.endPerformanceCounter("Node LOD ".concat(t)),e._loader.log("Loaded node LOD ".concat(t))),e.onNodeLODsLoadedObservable.notifyObservers(t),t!==e._nodePromiseLODs.length-1&&(e._loader.startPerformanceCounter("Node LOD ".concat(t+1)),e._loadBufferLOD(e._nodeBufferLODs,t+1),e._nodeSignalLODs[t]&&e._nodeSignalLODs[t].resolve())}));n._loader._completePromises.push(r)},n=this,r=0;r<this._nodePromiseLODs.length;r++)t(r);var o=function(t){var n=Promise.all(a._materialPromiseLODs[t]).then((function(){0!==t&&(e._loader.endPerformanceCounter("Material LOD ".concat(t)),e._loader.log("Loaded material LOD ".concat(t))),e.onMaterialLODsLoadedObservable.notifyObservers(t),t!==e._materialPromiseLODs.length-1&&(e._loader.startPerformanceCounter("Material LOD ".concat(t+1)),e._loadBufferLOD(e._materialBufferLODs,t+1),e._materialSignalLODs[t]&&e._materialSignalLODs[t].resolve())}));a._loader._completePromises.push(n)},a=this;for(r=0;r<this._materialPromiseLODs.length;r++)o(r)},e.prototype.loadSceneAsync=function(e,t){var n=this._loader.loadSceneAsync(e,t);return this._loadBufferLOD(this._bufferLODs,0),n},e.prototype.loadNodeAsync=function(e,t,n){var r=this;return Ee.LoadExtensionAsync(e,t,this.name,(function(e,o){var a,i=r._getLODs(e,t,r._loader.gltf.nodes,o.ids);r._loader.logOpen("".concat(e));for(var s=function(e){var t=i[e];0!==e&&(r._nodeIndexLOD=e,r._nodeSignalLODs[e]=r._nodeSignalLODs[e]||new A.Deferred);var o=r._loader.loadNodeAsync("/nodes/".concat(t.index),t,(function(e){n(e),e.setEnabled(!1)})).then((function(t){if(0!==e){var n=i[e-1];n._babylonTransformNode&&(r._disposeTransformNode(n._babylonTransformNode),delete n._babylonTransformNode)}return t.setEnabled(!0),t}));r._nodePromiseLODs[e]=r._nodePromiseLODs[e]||[],0===e?a=o:(r._nodeIndexLOD=null,r._nodePromiseLODs[e].push(o))},l=0;l<i.length;l++)s(l);return r._loader.logClose(),a}))},e.prototype._loadMaterialAsync=function(e,t,n,r,o){var a=this;return this._nodeIndexLOD?null:Ee.LoadExtensionAsync(e,t,this.name,(function(e,i){var s,l=a._getLODs(e,t,a._loader.gltf.materials,i.ids);a._loader.logOpen("".concat(e));for(var c=function(e){var t=l[e];0!==e&&(a._materialIndexLOD=e);var i=a._loader._loadMaterialAsync("/materials/".concat(t.index),t,n,r,(function(t){0===e&&o(t)})).then((function(t){if(0!==e){o(t);var n=l[e-1]._data;n[r]&&(a._disposeMaterials([n[r].babylonMaterial]),delete n[r])}return t}));a._materialPromiseLODs[e]=a._materialPromiseLODs[e]||[],0===e?s=i:(a._materialIndexLOD=null,a._materialPromiseLODs[e].push(i))},u=0;u<l.length;u++)c(u);return a._loader.logClose(),s}))},e.prototype._loadUriAsync=function(e,t,n){var r=this;if(null!==this._nodeIndexLOD){this._loader.log("deferred");var o=this._nodeIndexLOD-1;return this._nodeSignalLODs[o]=this._nodeSignalLODs[o]||new A.Deferred,this._nodeSignalLODs[this._nodeIndexLOD-1].promise.then((function(){return r._loader.loadUriAsync(e,t,n)}))}return null!==this._materialIndexLOD?(this._loader.log("deferred"),o=this._materialIndexLOD-1,this._materialSignalLODs[o]=this._materialSignalLODs[o]||new A.Deferred,this._materialSignalLODs[o].promise.then((function(){return r._loader.loadUriAsync(e,t,n)}))):null},e.prototype.loadBufferAsync=function(e,t,n,r){if(this._loader.parent.useRangeRequests&&!t.uri){if(!this._loader.bin)throw new Error("".concat(e,": Uri is missing or the binary glTF is missing its binary chunk"));var o=function(e,t){var o=n,a=o+r-1,i=e[t];return i?(i.start=Math.min(i.start,o),i.end=Math.max(i.end,a)):(i={start:o,end:a,loaded:new A.Deferred},e[t]=i),i.loaded.promise.then((function(e){return new Uint8Array(e.buffer,e.byteOffset+n-i.start,r)}))};return this._loader.log("deferred"),null!==this._nodeIndexLOD?o(this._nodeBufferLODs,this._nodeIndexLOD):null!==this._materialIndexLOD?o(this._materialBufferLODs,this._materialIndexLOD):o(this._bufferLODs,0)}return null},e.prototype._loadBufferLOD=function(e,t){var n=e[t];n&&(this._loader.log("Loading buffer range [".concat(n.start,"-").concat(n.end,"]")),this._loader.bin.readAsync(n.start,n.end-n.start+1).then((function(e){n.loaded.resolve(e)}),(function(e){n.loaded.reject(e)})))},e.prototype._getLODs=function(e,t,n,r){if(this.maxLODsToLoad<=0)throw new Error("maxLODsToLoad must be greater than zero");for(var o=[],a=r.length-1;a>=0;a--)if(o.push(Oe.Get("".concat(e,"/ids/").concat(r[a]),n,r[a])),o.length===this.maxLODsToLoad)return o;return o.push(t),o},e.prototype._disposeTransformNode=function(e){var t=this,n=[],r=e.material;r&&n.push(r);for(var o=0,a=e.getChildMeshes();o<a.length;o++){var i=a[o];i.material&&n.push(i.material)}e.dispose();var s=n.filter((function(e){return t._loader.babylonScene.meshes.every((function(t){return t.material!=e}))}));this._disposeMaterials(s)},e.prototype._disposeMaterials=function(e){for(var t={},n=0,r=e;n<r.length;n++){for(var o=0,a=(u=r[n]).getActiveTextures();o<a.length;o++){var i=a[o];t[i.uniqueId]=i}u.dispose()}for(var s in t)for(var l=0,c=this._loader.babylonScene.materials;l<c.length;l++){var u;(u=c[l]).hasTexture(t[s])&&delete t[s]}for(var s in t)t[s].dispose()},e}();Te(Ut),Ae(Ut,!0,(function(e){return new Ht(e)}));var jt="MSFT_minecraftMesh",Kt=function(){function e(e){this.name=jt,this._loader=e,this.enabled=this._loader.isExtensionUsed(jt)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this;return Ee.LoadExtraAsync(e,t,this.name,(function(o,a){if(a){if(!(n instanceof A.PBRMaterial))throw new Error("".concat(o,": Material type not supported"));var i=r._loader.loadMaterialPropertiesAsync(e,t,n);return n.needAlphaBlending()&&(n.forceDepthWrite=!0,n.separateCullingPass=!0),n.backFaceCulling=n.forceDepthWrite,n.twoSidedLighting=!0,i}return null}))},e}();Te(jt),Ae(jt,!0,(function(e){return new Kt(e)}));var Wt="MSFT_sRGBFactors",Yt=function(){function e(e){this.name=Wt,this._loader=e,this.enabled=this._loader.isExtensionUsed(Wt)}return e.prototype.dispose=function(){this._loader=null},e.prototype.loadMaterialPropertiesAsync=function(e,t,n){var r=this;return Ee.LoadExtraAsync(e,t,this.name,(function(o,a){if(a){if(!(n instanceof A.PBRMaterial))throw new Error("".concat(o,": Material type not supported"));var i=r._loader.loadMaterialPropertiesAsync(e,t,n),s=n.getScene().getEngine().useExactSrgbConversions;return n.albedoTexture||n.albedoColor.toLinearSpaceToRef(n.albedoColor,s),n.reflectivityTexture||n.reflectivityColor.toLinearSpaceToRef(n.reflectivityColor,s),i}return null}))},e}();Te(Wt),Ae(Wt,!0,(function(e){return new Yt(e)}));var qt={"lifecycle/onStart":A.FlowGraphSceneReadyEventBlock.ClassName,"lifecycle/onTick":A.FlowGraphSceneTickEventBlock.ClassName,log:A.FlowGraphConsoleLogBlock.ClassName,"flow/delay":A.FlowGraphTimerBlock.ClassName,"customEvent/send":A.FlowGraphSendCustomEventBlock.ClassName,"customEvent/receive":A.FlowGraphReceiveCustomEventBlock.ClassName,"flow/sequence":A.FlowGraphSequenceBlock.ClassName,"world/get":A.FlowGraphGetPropertyBlock.ClassName,"world/set":A.FlowGraphSetPropertyBlock.ClassName,"flow/doN":A.FlowGraphDoNBlock.ClassName,"variable/get":A.FlowGraphGetVariableBlock.ClassName,"variable/set":A.FlowGraphSetVariableBlock.ClassName,"flow/whileLoop":A.FlowGraphWhileLoopBlock.ClassName,"math/random":A.FlowGraphRandomBlock.ClassName,"math/e":A.FlowGraphEBlock.ClassName,"math/pi":A.FlowGraphPiBlock.ClassName,"math/inf":A.FlowGraphInfBlock.ClassName,"math/nan":A.FlowGraphNaNBlock.ClassName,"math/abs":A.FlowGraphAbsBlock.ClassName,"math/sign":A.FlowGraphSignBlock.ClassName,"math/trunc":A.FlowGraphTruncBlock.ClassName,"math/floor":A.FlowGraphFloorBlock.ClassName,"math/ceil":A.FlowGraphCeilBlock.ClassName,"math/fract":A.FlowGraphFractBlock.ClassName,"math/neg":A.FlowGraphNegBlock.ClassName,"math/add":A.FlowGraphAddBlock.ClassName,"math/sub":A.FlowGraphSubtractBlock.ClassName,"math/mul":A.FlowGraphMultiplyBlock.ClassName,"math/div":A.FlowGraphDivideBlock.ClassName,"math/rem":A.FlowGraphRemainderBlock.ClassName,"math/min":A.FlowGraphMinBlock.ClassName,"math/max":A.FlowGraphMaxBlock.ClassName,"math/clamp":A.FlowGraphClampBlock.ClassName,"math/saturate":A.FlowGraphSaturateBlock.ClassName,"math/mix":A.FlowGraphInterpolateBlock.ClassName,"math/eq":A.FlowGraphEqBlock.ClassName,"math/lt":A.FlowGraphLessThanBlock.ClassName,"math/le":A.FlowGraphLessThanOrEqualBlock.ClassName,"math/gt":A.FlowGraphGreaterThanBlock.ClassName,"math/ge":A.FlowGraphGreaterThanOrEqualBlock.ClassName,"math/isnan":A.FlowGraphIsNanBlock.ClassName,"math/isinf":A.FlowGraphIsInfBlock.ClassName,"math/rad":A.FlowGraphDegToRadBlock.ClassName,"math/deg":A.FlowGraphRadToDegBlock.ClassName,"math/sin":A.FlowGraphSinBlock.ClassName,"math/cos":A.FlowGraphCosBlock.ClassName,"math/tan":A.FlowGraphTanBlock.ClassName,"math/asin":A.FlowGraphAsinBlock.ClassName,"math/acos":A.FlowGraphAcosBlock.ClassName,"math/atan":A.FlowGraphAtanBlock.ClassName,"math/atan2":A.FlowGraphAtan2Block.ClassName,"math/sinh":A.FlowGraphSinhBlock.ClassName,"math/cosh":A.FlowGraphCoshBlock.ClassName,"math/tanh":A.FlowGraphTanhBlock.ClassName,"math/asinh":A.FlowGraphAsinhBlock.ClassName,"math/acosh":A.FlowGraphAcoshBlock.ClassName,"math/atanh":A.FlowGraphAtanhBlock.ClassName,"math/exp":A.FlowGraphExpBlock.ClassName,"math/log":A.FlowGraphLogBlock.ClassName,"math/log2":A.FlowGraphLog2Block.ClassName,"math/log10":A.FlowGraphLog10Block.ClassName,"math/sqrt":A.FlowGraphSqrtBlock.ClassName,"math/cbrt":A.FlowGraphCubeRootBlock.ClassName,"math/pow":A.FlowGraphPowBlock.ClassName,"math/length":A.FlowGraphLengthBlock.ClassName,"math/normalize":A.FlowGraphNormalizeBlock.ClassName,"math/dot":A.FlowGraphDotBlock.ClassName,"math/cross":A.FlowGraphCrossBlock.ClassName,"math/rotate2d":A.FlowGraphRotate2DBlock.ClassName,"math/rotate3d":A.FlowGraphRotate3DBlock.ClassName,"math/transpose":A.FlowGraphTransposeBlock.ClassName,"math/determinant":A.FlowGraphDeterminantBlock.ClassName,"math/inverse":A.FlowGraphInvertMatrixBlock.ClassName,"math/matmul":A.FlowGraphMatMulBlock.ClassName,"math/not":A.FlowGraphBitwiseNotBlock.ClassName,"math/and":A.FlowGraphBitwiseAndBlock.ClassName,"math/or":A.FlowGraphBitwiseOrBlock.ClassName,"math/xor":A.FlowGraphBitwiseXorBlock.ClassName,"math/asr":A.FlowGraphBitwiseRightShiftBlock.ClassName,"math/lsl":A.FlowGraphBitwiseLeftShiftBlock.ClassName,"math/clz":A.FlowGraphCountLeadingZerosBlock.ClassName,"math/ctz":A.FlowGraphCountTrailingZerosBlock.ClassName,"math/popcnt":A.FlowGraphCountOneBitsBlock.ClassName},zt={float2:"Vector2",float3:"Vector3",float4:"Vector4",float4x4:"Matrix",int:"FlowGraphInteger"};function Xt(e,t,n){if(void 0!==e.type){var r=t.types&&t.types[e.type];if(!r)throw new Error("".concat(n,": Unknown type: ").concat(e.type));var o=r.signature;if(!o)throw new Error("".concat(n,": Type ").concat(e.type," has no signature"));var a=zt[o];return{value:e.value,className:a}}return e.value}function Zt(e,t,n){var r=qt[t.type];if(!r)throw new Error("/extensions/KHR_interactivity/nodes/".concat(e,": Unknown block type: ").concat(t.type));var o=e.toString(),a=function(e,t,n){for(var r,o={},a=0,i=null!==(r=e.configuration)&&void 0!==r?r:[];a<i.length;a++){var s=i[a];if("customEvent"===s.id){var l=t.customEvents&&t.customEvents[s.value];if(!l)throw new Error("/extensions/KHR_interactivity/nodes/".concat(n,": Unknown custom event: ").concat(s.value));o.eventId=l.id,o.eventData=l.values.map((function(e){return e.id}))}else if("variable"===s.id){var c=t.variables&&t.variables[s.value];if(!c)throw new Error("/extensions/KHR_interactivity/nodes/".concat(n,": Unknown variable: ").concat(s.value));o.variableName=c.id}else if("path"===s.id){var u=s.value;o.path=u}else o[s.id]=Xt(s,t,"/extensions/KHR_interactivity/nodes/".concat(n))}return o}(t,n,o);return{className:r,config:a,uniqueId:o,metadata:t.metadata,dataInputs:[],dataOutputs:[],signalInputs:[],signalOutputs:[]}}var Jt=function(e){function t(t){return e.call(this,t,Qt)||this}return y(t,e),t}(It),Qt={nodes:{__array__:{__target__:!0,translation:{type:"Vector3",get:function(e){return e._babylonTransformNode.position},set:function(e,t){t._babylonTransformNode.position=e},getObject:function(e){return e._babylonTransformNode}}}}},$t="KHR_interactivity",en=function(){function e(e){this._loader=e,this.name=$t,this.enabled=this._loader.isExtensionUsed($t),this._pathConverter=new Jt(this._loader.gltf)}return e.prototype.dispose=function(){this._loader=null,delete this._pathConverter},e.prototype.onReady=function(){var e;if(this._loader.babylonScene&&this._pathConverter){var t=this._loader.babylonScene,n=function(e){for(var t,n,r,o={uniqueId:(0,A.RandomGUID)(),_userVariables:{},_connectionValues:{}},a=[o],i=[],s=0;s<e.nodes.length;s++){var l=e.nodes[s],c=Zt(s,l,e);i.push(c)}for(s=0;s<e.nodes.length;s++){l=e.nodes[s];for(var u=i[s],d=function(e){var t=e.id,n={uniqueId:(0,A.RandomGUID)(),name:t,_connectionType:1,connectedPointIds:[]};u.signalOutputs.push(n);var r=e.node,o=e.socket,a=i[r];if(!a)throw new Error("/extensions/KHR_interactivity/nodes/".concat(s,": Could not find node with id ").concat(r," that connects its input with with node ").concat(s,"'s output ").concat(t));var l=a.signalInputs.find((function(e){return e.name===o}));l||(l={uniqueId:(0,A.RandomGUID)(),name:o,_connectionType:0,connectedPointIds:[]},a.signalInputs.push(l)),l.connectedPointIds.push(n.uniqueId),n.connectedPointIds.push(l.uniqueId)},h=0,f=null!==(t=l.flows)&&void 0!==t?t:[];h<f.length;h++)d(f[h]);for(var p=function(t){var n=t.id,r={uniqueId:(0,A.RandomGUID)(),name:n,_connectionType:0,connectedPointIds:[]};if(u.dataInputs.push(r),void 0!==t.value){var a=Xt(t,e,"/extensions/KHR_interactivity/nodes/".concat(s));o._connectionValues[r.uniqueId]=a}else{if(void 0===t.node||void 0===t.socket)throw new Error("/extensions/KHR_interactivity/nodes/".concat(s,": Invalid socket ").concat(n," in node ").concat(s));var l=t.node,c=t.socket,d=i[l];if(!d)throw new Error("/extensions/KHR_interactivity/nodes/".concat(s,": Could not find node with id ").concat(l," that connects its output with node").concat(s,"'s input ").concat(n));var h=d.dataOutputs.find((function(e){return e.name===c}));h||(h={uniqueId:(0,A.RandomGUID)(),name:c,_connectionType:1,connectedPointIds:[]},d.dataOutputs.push(h)),r.connectedPointIds.push(h.uniqueId),h.connectedPointIds.push(r.uniqueId)}},_=0,m=null!==(n=l.values)&&void 0!==n?n:[];_<m.length;_++)p(m[_])}var y=null!==(r=e.variables)&&void 0!==r?r:[];for(s=0;s<y.length;s++){var v=y[s],b=v.id;o._userVariables[b]=Xt(v,e,"/extensions/KHR_interactivity/variables/".concat(s))}return{allBlocks:i,executionContexts:a}}(null===(e=this._loader.gltf.extensions)||void 0===e?void 0:e.KHR_interactivity),r=new A.FlowGraphCoordinator({scene:t});A.FlowGraph.Parse(n,{coordinator:r,pathConverter:this._pathConverter}),r.start()}},e}();Te($t),Ae($t,!0,(function(e){return new en(e)}));var tn="KHR_node_visibility",nn=function(){function e(e){this.name=tn,this._loader=e,this.enabled=e.isExtensionUsed(tn)}return e.prototype.onReady=function(){return b(this,void 0,void 0,(function(){var e;return g(this,(function(t){return null===(e=this._loader.gltf.nodes)||void 0===e||e.forEach((function(e){var t,n,r,o;null===(t=e._primitiveBabylonMeshes)||void 0===t||t.forEach((function(e){e.inheritVisibility=!0})),(null===(n=e.extensions)||void 0===n?void 0:n.KHR_node_visibility)&&!1===(null===(r=e.extensions)||void 0===r?void 0:r.KHR_node_visibility.visible)&&(e._babylonTransformNode&&(e._babylonTransformNode.isVisible=!1),null===(o=e._primitiveBabylonMeshes)||void 0===o||o.forEach((function(e){e.isVisible=!1})))})),[2]}))}))},e.prototype.dispose=function(){this._loader=null},e}();Te(tn),Ae(tn,!0,(function(e){return new nn(e)}));var rn="ExtrasAsMetadata",on=function(){function e(e){this.name=rn,this.enabled=!0,this._loader=e}return e.prototype._assignExtras=function(e,t){if(t.extras&&Object.keys(t.extras).length>0){var n=e.metadata=e.metadata||{};(n.gltf=n.gltf||{}).extras=t.extras}},e.prototype.dispose=function(){this._loader=null},e.prototype.loadNodeAsync=function(e,t,n){var r=this;return this._loader.loadNodeAsync(e,t,(function(e){r._assignExtras(e,t),n(e)}))},e.prototype.loadCameraAsync=function(e,t,n){var r=this;return this._loader.loadCameraAsync(e,t,(function(e){r._assignExtras(e,t),n(e)}))},e.prototype.createMaterial=function(e,t,n){var r=this._loader.createMaterial(e,t,n);return this._assignExtras(r,t),r},e}();Te(rn),Ae(rn,!1,(function(e){return new on(e)}));var an=function(){function e(){this.materials=[]}return e.prototype.parseMTL=function(t,n,r,o){if(!(n instanceof ArrayBuffer)){for(var a,i=n.split("\n"),s=/\s+/,l=null,c=0;c<i.length;c++){var u=i[c].trim();if(0!==u.length&&"#"!==u.charAt(0)){var d=u.indexOf(" "),h=d>=0?u.substring(0,d):u;h=h.toLowerCase();var f=d>=0?u.substring(d+1).trim():"";if("newmtl"===h)l&&this.materials.push(l),t._blockEntityCollection=!!o,(l=new A.StandardMaterial(f,t))._parentContainer=o,t._blockEntityCollection=!1;else if("kd"===h&&l)a=f.split(s,3).map(parseFloat),l.diffuseColor=A.Color3.FromArray(a);else if("ka"===h&&l)a=f.split(s,3).map(parseFloat),l.ambientColor=A.Color3.FromArray(a);else if("ks"===h&&l)a=f.split(s,3).map(parseFloat),l.specularColor=A.Color3.FromArray(a);else if("ke"===h&&l)a=f.split(s,3).map(parseFloat),l.emissiveColor=A.Color3.FromArray(a);else if("ns"===h&&l)l.specularPower=parseFloat(f);else if("d"===h&&l)l.alpha=parseFloat(f);else if("map_ka"===h&&l)l.ambientTexture=e._GetTexture(r,f,t);else if("map_kd"===h&&l)l.diffuseTexture=e._GetTexture(r,f,t);else if("map_ks"===h&&l)l.specularTexture=e._GetTexture(r,f,t);else if("map_ns"===h);else if("map_bump"===h&&l){var p=f.split(s),_=p.indexOf("-bm"),m=null;_>=0&&(m=p[_+1],p.splice(_,2)),l.bumpTexture=e._GetTexture(r,p.join(" "),t),l.bumpTexture&&null!==m&&(l.bumpTexture.level=parseFloat(m))}else"map_d"===h&&l&&(l.opacityTexture=e._GetTexture(r,f,t))}}l&&this.materials.push(l)}},e._GetTexture=function(t,n,r){if(!n)return null;var o=t;if("file:"===t){var a=n.lastIndexOf("\\");-1===a&&(a=n.lastIndexOf("/")),o+=a>-1?n.substring(a+1):n}else o+=n;return new A.Texture(o,r,!1,e.INVERT_TEXTURE_Y)},e.INVERT_TEXTURE_Y=!0,e}(),sn=function(){function e(e,t,n){this._positions=[],this._normals=[],this._uvs=[],this._colors=[],this._extColors=[],this._meshesFromObj=[],this._indicesForBabylon=[],this._wrappedPositionForBabylon=[],this._wrappedUvsForBabylon=[],this._wrappedColorsForBabylon=[],this._wrappedNormalsForBabylon=[],this._tuplePosNorm=[],this._curPositionInIndices=0,this._hasMeshes=!1,this._unwrappedPositionsForBabylon=[],this._unwrappedColorsForBabylon=[],this._unwrappedNormalsForBabylon=[],this._unwrappedUVForBabylon=[],this._triangles=[],this._materialNameFromObj="",this._objMeshName="",this._increment=1,this._isFirstMaterial=!0,this._grayColor=new A.Color4(.5,.5,.5,1),this._hasLineData=!1,this._materialToUse=e,this._babylonMeshesArray=t,this._loadingOptions=n}return e.prototype._isInArray=function(e,t){e[t[0]]||(e[t[0]]={normals:[],idx:[]});var n=e[t[0]].normals.indexOf(t[1]);return-1===n?-1:e[t[0]].idx[n]},e.prototype._isInArrayUV=function(e,t){e[t[0]]||(e[t[0]]={normals:[],idx:[],uv:[]});var n=e[t[0]].normals.indexOf(t[1]);return 1!=n&&t[2]===e[t[0]].uv[n]?e[t[0]].idx[n]:-1},e.prototype._setData=function(e,t,n,r,o,a,i){var s;-1===(s=this._loadingOptions.optimizeWithUV?this._isInArrayUV(this._tuplePosNorm,[e,n,t]):this._isInArray(this._tuplePosNorm,[e,n]))?(this._indicesForBabylon.push(this._wrappedPositionForBabylon.length),this._wrappedPositionForBabylon.push(r),o=null!=o?o:new A.Vector2(0,0),this._wrappedUvsForBabylon.push(o),this._wrappedNormalsForBabylon.push(a),void 0!==i&&this._wrappedColorsForBabylon.push(i),this._tuplePosNorm[e].normals.push(n),this._tuplePosNorm[e].idx.push(this._curPositionInIndices++),this._loadingOptions.optimizeWithUV&&this._tuplePosNorm[e].uv.push(t)):this._indicesForBabylon.push(s)},e.prototype._unwrapData=function(){try{for(var e=0;e<this._wrappedPositionForBabylon.length;e++)this._unwrappedPositionsForBabylon.push(this._wrappedPositionForBabylon[e].x*this._handednessSign,this._wrappedPositionForBabylon[e].y,this._wrappedPositionForBabylon[e].z),this._unwrappedNormalsForBabylon.push(this._wrappedNormalsForBabylon[e].x*this._handednessSign,this._wrappedNormalsForBabylon[e].y,this._wrappedNormalsForBabylon[e].z),this._unwrappedUVForBabylon.push(this._wrappedUvsForBabylon[e].x,this._wrappedUvsForBabylon[e].y),this._loadingOptions.importVertexColors&&this._unwrappedColorsForBabylon.push(this._wrappedColorsForBabylon[e].r,this._wrappedColorsForBabylon[e].g,this._wrappedColorsForBabylon[e].b,this._wrappedColorsForBabylon[e].a);this._wrappedPositionForBabylon.length=0,this._wrappedNormalsForBabylon.length=0,this._wrappedUvsForBabylon.length=0,this._wrappedColorsForBabylon.length=0,this._tuplePosNorm.length=0,this._curPositionInIndices=0}catch(e){throw new Error("Unable to unwrap data while parsing OBJ data.")}},e.prototype._getTriangles=function(e,t){for(var n=t;n<e.length-1;n++)this._pushTriangle(e,n)},e.prototype._getColor=function(e){var t;return this._loadingOptions.importVertexColors?null!==(t=this._extColors[e])&&void 0!==t?t:this._colors[e]:void 0},e.prototype._setDataForCurrentFaceWithPattern1=function(e,t){this._getTriangles(e,t);for(var n=0;n<this._triangles.length;n++){var r=parseInt(this._triangles[n])-1;this._setData(r,0,0,this._positions[r],A.Vector2.Zero(),A.Vector3.Up(),this._getColor(r))}this._triangles.length=0},e.prototype._setDataForCurrentFaceWithPattern2=function(e,t){var n;this._getTriangles(e,t);for(var r=0;r<this._triangles.length;r++){var o=this._triangles[r].split("/"),a=parseInt(o[0])-1,i=parseInt(o[1])-1;this._setData(a,i,0,this._positions[a],null!==(n=this._uvs[i])&&void 0!==n?n:A.Vector2.Zero(),A.Vector3.Up(),this._getColor(a))}this._triangles.length=0},e.prototype._setDataForCurrentFaceWithPattern3=function(e,t){var n,r;this._getTriangles(e,t);for(var o=0;o<this._triangles.length;o++){var a=this._triangles[o].split("/"),i=parseInt(a[0])-1,s=parseInt(a[1])-1,l=parseInt(a[2])-1;this._setData(i,s,l,this._positions[i],null!==(n=this._uvs[s])&&void 0!==n?n:A.Vector2.Zero(),null!==(r=this._normals[l])&&void 0!==r?r:A.Vector3.Up())}this._triangles.length=0},e.prototype._setDataForCurrentFaceWithPattern4=function(e,t){this._getTriangles(e,t);for(var n=0;n<this._triangles.length;n++){var r=this._triangles[n].split("//"),o=parseInt(r[0])-1,a=parseInt(r[1])-1;this._setData(o,1,a,this._positions[o],A.Vector2.Zero(),this._normals[a],this._getColor(o))}this._triangles.length=0},e.prototype._setDataForCurrentFaceWithPattern5=function(e,t){this._getTriangles(e,t);for(var n=0;n<this._triangles.length;n++){var r=this._triangles[n].split("/"),o=this._positions.length+parseInt(r[0]),a=this._uvs.length+parseInt(r[1]),i=this._normals.length+parseInt(r[2]);this._setData(o,a,i,this._positions[o],this._uvs[a],this._normals[i],this._getColor(o))}this._triangles.length=0},e.prototype._addPreviousObjMesh=function(){this._meshesFromObj.length>0&&(this._handledMesh=this._meshesFromObj[this._meshesFromObj.length-1],this._unwrapData(),this._loadingOptions.useLegacyBehavior&&this._indicesForBabylon.reverse(),this._handledMesh.indices=this._indicesForBabylon.slice(),this._handledMesh.positions=this._unwrappedPositionsForBabylon.slice(),this._handledMesh.normals=this._unwrappedNormalsForBabylon.slice(),this._handledMesh.uvs=this._unwrappedUVForBabylon.slice(),this._handledMesh.hasLines=this._hasLineData,this._loadingOptions.importVertexColors&&(this._handledMesh.colors=this._unwrappedColorsForBabylon.slice()),this._indicesForBabylon.length=0,this._unwrappedPositionsForBabylon.length=0,this._unwrappedColorsForBabylon.length=0,this._unwrappedNormalsForBabylon.length=0,this._unwrappedUVForBabylon.length=0,this._hasLineData=!1)},e.prototype._optimizeNormals=function(e){var t=e.getVerticesData(A.VertexBuffer.PositionKind),n=e.getVerticesData(A.VertexBuffer.NormalKind),r={};if(t&&n){for(var o=0;o<t.length/3;o++)(s=r[i=t[3*o+0]+"_"+t[3*o+1]+"_"+t[3*o+2]])||(s=[],r[i]=s),s.push(o);var a=new A.Vector3;for(var i in r){var s;if(!((s=r[i]).length<2)){var l=s[0];for(o=1;o<s.length;++o){var c=s[o];n[3*l+0]+=n[3*c+0],n[3*l+1]+=n[3*c+1],n[3*l+2]+=n[3*c+2]}for(a.copyFromFloats(n[3*l+0],n[3*l+1],n[3*l+2]),a.normalize(),o=0;o<s.length;++o)n[3*(c=s[o])+0]=a.x,n[3*c+1]=a.y,n[3*c+2]=a.z}}e.setVerticesData(A.VertexBuffer.NormalKind,n)}},e._IsLineElement=function(e){return e.startsWith("l")},e._IsObjectElement=function(e){return e.startsWith("o")},e._IsGroupElement=function(e){return e.startsWith("g")},e._GetZbrushMRGB=function(e,t){if(!e.startsWith("mrgb"))return null;if(e=e.replace("mrgb","").trim(),t)return[];var n=e.match(/[a-z0-9]/g);if(!n||n.length%8!=0)return[];for(var r=[],o=0;o<n.length/8;o++){var a=n[8*o+2]+n[8*o+3],i=n[8*o+4]+n[8*o+5],s=n[8*o+6]+n[8*o+7];r.push(new A.Color4(parseInt(a,16)/255,parseInt(i,16)/255,parseInt(s,16)/255,1))}return r},e.prototype.parse=function(t,n,r,o,a){var i,s,l=this;n=(n=n.replace(/#MRGB/g,"mrgb")).replace(/#.*$/gm,"").trim(),this._loadingOptions.useLegacyBehavior?(this._pushTriangle=function(e,t){return l._triangles.push(e[0],e[t],e[t+1])},this._handednessSign=1):r.useRightHandedSystem?(this._pushTriangle=function(e,t){return l._triangles.push(e[0],e[t+1],e[t])},this._handednessSign=1):(this._pushTriangle=function(e,t){return l._triangles.push(e[0],e[t],e[t+1])},this._handednessSign=-1);var c=n.split("\n"),u=[],d=[];u.push(d);for(var h=0;h<c.length;h++)if(0!==(m=c[h].trim().replace(/\s\s/g," ")).length&&"#"!==m.charAt(0))if((e._IsGroupElement(m)||e._IsObjectElement(m))&&(d=[],u.push(d)),e._IsLineElement(m))for(var f=m.split(" "),p=1;p<f.length-1;p++)d.push("l ".concat(f[p]," ").concat(f[p+1]));else d.push(m);var _=u.flat();for(h=0;h<_.length;h++){var m,y=void 0;if(0!==(m=_[h].trim().replace(/\s\s/g," ")).length&&"#"!==m.charAt(0))if(e.VertexPattern.test(m)){if(y=m.match(/[^ ]+/g),this._positions.push(new A.Vector3(parseFloat(y[1]),parseFloat(y[2]),parseFloat(y[3]))),this._loadingOptions.importVertexColors)if(y.length>=7){var v=parseFloat(y[4]),b=parseFloat(y[5]),g=parseFloat(y[6]);this._colors.push(new A.Color4(v>1?v/255:v,b>1?b/255:b,g>1?g/255:g,7===y.length||void 0===y[7]?1:parseFloat(y[7])))}else this._colors.push(this._grayColor)}else if(null!==(y=e.NormalPattern.exec(m)))this._normals.push(new A.Vector3(parseFloat(y[1]),parseFloat(y[2]),parseFloat(y[3])));else if(null!==(y=e.UVPattern.exec(m)))this._uvs.push(new A.Vector2(parseFloat(y[1])*this._loadingOptions.UVScaling.x,parseFloat(y[2])*this._loadingOptions.UVScaling.y));else if(null!==(y=e.FacePattern3.exec(m)))this._setDataForCurrentFaceWithPattern3(y[1].trim().split(" "),1);else if(null!==(y=e.FacePattern4.exec(m)))this._setDataForCurrentFaceWithPattern4(y[1].trim().split(" "),1);else if(null!==(y=e.FacePattern5.exec(m)))this._setDataForCurrentFaceWithPattern5(y[1].trim().split(" "),1);else if(null!==(y=e.FacePattern2.exec(m)))this._setDataForCurrentFaceWithPattern2(y[1].trim().split(" "),1);else if(null!==(y=e.FacePattern1.exec(m)))this._setDataForCurrentFaceWithPattern1(y[1].trim().split(" "),1);else if(null!==(y=e.LinePattern1.exec(m)))this._setDataForCurrentFaceWithPattern1(y[1].trim().split(" "),0),this._hasLineData=!0;else if(null!==(y=e.LinePattern2.exec(m)))this._setDataForCurrentFaceWithPattern2(y[1].trim().split(" "),0),this._hasLineData=!0;else if(y=e._GetZbrushMRGB(m,!this._loadingOptions.importVertexColors))y.forEach((function(e){l._extColors.push(e)}));else if(null!==(y=e.LinePattern3.exec(m)))this._setDataForCurrentFaceWithPattern3(y[1].trim().split(" "),0),this._hasLineData=!0;else if(e.GroupDescriptor.test(m)||e.ObjectDescriptor.test(m)){var T={name:m.substring(2).trim(),indices:null,positions:null,normals:null,uvs:null,colors:null,materialName:this._materialNameFromObj,isObject:e.ObjectDescriptor.test(m)};this._addPreviousObjMesh(),this._meshesFromObj.push(T),this._hasMeshes=!0,this._isFirstMaterial=!0,this._increment=1}else e.UseMtlDescriptor.test(m)?(this._materialNameFromObj=m.substring(7).trim(),this._isFirstMaterial&&this._hasMeshes||(this._addPreviousObjMesh(),T={name:(this._objMeshName||"mesh")+"_mm"+this._increment.toString(),indices:null,positions:null,normals:null,uvs:null,colors:null,materialName:this._materialNameFromObj,isObject:!1},this._increment++,this._meshesFromObj.push(T),this._hasMeshes=!0),this._hasMeshes&&this._isFirstMaterial&&(this._meshesFromObj[this._meshesFromObj.length-1].materialName=this._materialNameFromObj,this._isFirstMaterial=!1)):e.MtlLibGroupDescriptor.test(m)?a(m.substring(7).trim()):e.SmoothDescriptor.test(m)||A.Logger.Log("Unhandled expression at line : "+m)}if(this._hasMeshes&&(this._handledMesh=this._meshesFromObj[this._meshesFromObj.length-1],this._loadingOptions.useLegacyBehavior&&this._indicesForBabylon.reverse(),this._unwrapData(),this._handledMesh.indices=this._indicesForBabylon,this._handledMesh.positions=this._unwrappedPositionsForBabylon,this._handledMesh.normals=this._unwrappedNormalsForBabylon,this._handledMesh.uvs=this._unwrappedUVForBabylon,this._handledMesh.hasLines=this._hasLineData,this._loadingOptions.importVertexColors&&(this._handledMesh.colors=this._unwrappedColorsForBabylon)),!this._hasMeshes){var x=null;if(this._indicesForBabylon.length)this._loadingOptions.useLegacyBehavior&&this._indicesForBabylon.reverse(),this._unwrapData();else{for(var O=0,E=this._positions;O<E.length;O++){var M=E[O];this._unwrappedPositionsForBabylon.push(M.x,M.y,M.z)}if(this._normals.length)for(var w=0,C=this._normals;w<C.length;w++){var L=C[w];this._unwrappedNormalsForBabylon.push(L.x,L.y,L.z)}if(this._uvs.length)for(var N=0,S=this._uvs;N<S.length;N++){var R=S[N];this._unwrappedUVForBabylon.push(R.x,R.y)}if(this._extColors.length)for(var P=0,F=this._extColors;P<F.length;P++){var I=F[P];this._unwrappedColorsForBabylon.push(I.r,I.g,I.b,I.a)}else if(this._colors.length)for(var B=0,D=this._colors;B<D.length;B++)I=D[B],this._unwrappedColorsForBabylon.push(I.r,I.g,I.b,I.a);this._materialNameFromObj||((x=new A.StandardMaterial(A.Geometry.RandomId(),r)).pointsCloud=!0,this._materialNameFromObj=x.name,this._normals.length||(x.disableLighting=!0,x.emissiveColor=A.Color3.White()))}this._meshesFromObj.push({name:A.Geometry.RandomId(),indices:this._indicesForBabylon,positions:this._unwrappedPositionsForBabylon,colors:this._unwrappedColorsForBabylon,normals:this._unwrappedNormalsForBabylon,uvs:this._unwrappedUVForBabylon,materialName:this._materialNameFromObj,directMaterial:x,isObject:!0,hasLines:this._hasLineData})}for(var V=0;V<this._meshesFromObj.length;V++){if(t&&this._meshesFromObj[V].name)if(t instanceof Array){if(-1===t.indexOf(this._meshesFromObj[V].name))continue}else if(this._meshesFromObj[V].name!==t)continue;this._handledMesh=this._meshesFromObj[V],r._blockEntityCollection=!!o;var k=new A.Mesh(this._meshesFromObj[V].name,r);if(k._parentContainer=o,r._blockEntityCollection=!1,this._handledMesh._babylonMesh=k,!this._handledMesh.isObject)for(var G=V-1;G>=0;--G)if(this._meshesFromObj[G].isObject&&this._meshesFromObj[G]._babylonMesh){k.parent=this._meshesFromObj[G]._babylonMesh;break}if(this._materialToUse.push(this._meshesFromObj[V].materialName),this._handledMesh.hasLines&&(null!==(i=k._internalMetadata)&&void 0!==i||(k._internalMetadata={}),k._internalMetadata._isLine=!0),0!==(null===(s=this._handledMesh.positions)||void 0===s?void 0:s.length)){var U=new A.VertexData;if(U.uvs=this._handledMesh.uvs,U.indices=this._handledMesh.indices,U.positions=this._handledMesh.positions,this._loadingOptions.computeNormals){var H=new Array;A.VertexData.ComputeNormals(this._handledMesh.positions,this._handledMesh.indices,H),U.normals=H}else U.normals=this._handledMesh.normals;this._loadingOptions.importVertexColors&&(U.colors=this._handledMesh.colors),U.applyToMesh(k),this._loadingOptions.invertY&&(k.scaling.y*=-1),this._loadingOptions.optimizeNormals&&this._optimizeNormals(k),this._babylonMeshesArray.push(k),this._handledMesh.directMaterial&&(k.material=this._handledMesh.directMaterial)}else this._babylonMeshesArray.push(k)}},e.ObjectDescriptor=/^o/,e.GroupDescriptor=/^g/,e.MtlLibGroupDescriptor=/^mtllib /,e.UseMtlDescriptor=/^usemtl /,e.SmoothDescriptor=/^s /,e.VertexPattern=/^v(\s+[\d|.|+|\-|e|E]+){3,7}/,e.NormalPattern=/^vn(\s+[\d|.|+|\-|e|E]+)( +[\d|.|+|\-|e|E]+)( +[\d|.|+|\-|e|E]+)/,e.UVPattern=/^vt(\s+[\d|.|+|\-|e|E]+)( +[\d|.|+|\-|e|E]+)/,e.FacePattern1=/^f\s+(([\d]{1,}[\s]?){3,})+/,e.FacePattern2=/^f\s+((([\d]{1,}\/[\d]{1,}[\s]?){3,})+)/,e.FacePattern3=/^f\s+((([\d]{1,}\/[\d]{1,}\/[\d]{1,}[\s]?){3,})+)/,e.FacePattern4=/^f\s+((([\d]{1,}\/\/[\d]{1,}[\s]?){3,})+)/,e.FacePattern5=/^f\s+(((-[\d]{1,}\/-[\d]{1,}\/-[\d]{1,}[\s]?){3,})+)/,e.LinePattern1=/^l\s+(([\d]{1,}[\s]?){2,})+/,e.LinePattern2=/^l\s+((([\d]{1,}\/[\d]{1,}[\s]?){2,})+)/,e.LinePattern3=/^l\s+((([\d]{1,}\/[\d]{1,}\/[\d]{1,}[\s]?){2,})+)/,e}(),ln=function(){function e(t){this.name="obj",this.extensions=".obj",this._assetContainer=null,this._loadingOptions=t||e._DefaultLoadingOptions}return Object.defineProperty(e,"INVERT_TEXTURE_Y",{get:function(){return an.INVERT_TEXTURE_Y},set:function(e){an.INVERT_TEXTURE_Y=e},enumerable:!1,configurable:!0}),Object.defineProperty(e,"_DefaultLoadingOptions",{get:function(){return{computeNormals:e.COMPUTE_NORMALS,optimizeNormals:e.OPTIMIZE_NORMALS,importVertexColors:e.IMPORT_VERTEX_COLORS,invertY:e.INVERT_Y,invertTextureY:e.INVERT_TEXTURE_Y,UVScaling:e.UV_SCALING,materialLoadingFailsSilently:e.MATERIAL_LOADING_FAILS_SILENTLY,optimizeWithUV:e.OPTIMIZE_WITH_UV,skipMaterials:e.SKIP_MATERIALS,useLegacyBehavior:e.USE_LEGACY_BEHAVIOR}},enumerable:!1,configurable:!0}),e.prototype._loadMTL=function(e,t,n,r){var o=t+e;A.Tools.LoadFile(o,n,void 0,void 0,!1,(function(e,t){r(o,t)}))},e.prototype.createPlugin=function(){return new e(e._DefaultLoadingOptions)},e.prototype.canDirectLoad=function(){return!1},e.prototype.importMeshAsync=function(e,t,n,r){return this._parseSolid(e,t,n,r).then((function(e){return{meshes:e,particleSystems:[],skeletons:[],animationGroups:[],transformNodes:[],geometries:[],lights:[],spriteManagers:[]}}))},e.prototype.loadAsync=function(e,t,n){return this.importMeshAsync(null,e,t,n).then((function(){}))},e.prototype.loadAssetContainerAsync=function(e,t,n){var r=this,o=new A.AssetContainer(e);return this._assetContainer=o,this.importMeshAsync(null,e,t,n).then((function(e){return e.meshes.forEach((function(e){return o.meshes.push(e)})),e.meshes.forEach((function(e){var t=e.material;t&&-1==o.materials.indexOf(t)&&(o.materials.push(t),t.getActiveTextures().forEach((function(e){-1==o.textures.indexOf(e)&&o.textures.push(e)})))})),r._assetContainer=null,o})).catch((function(e){throw r._assetContainer=null,e}))},e.prototype._parseSolid=function(e,t,n,r){var o=this,a="",i=new an,s=[],l=[];n=n.replace(/#.*$/gm,"").trim(),new sn(s,l,this._loadingOptions).parse(e,n,t,this._assetContainer,(function(e){a=e}));var c=[];return""===a||this._loadingOptions.skipMaterials||c.push(new Promise((function(e,n){o._loadMTL(a,r,(function(c){try{i.parseMTL(t,c,r,o._assetContainer);for(var u=0;u<i.materials.length;u++){for(var d=0,h=[],f=void 0;(f=s.indexOf(i.materials[u].name,d))>-1;)h.push(f),d=f+1;if(-1===f&&0===h.length)i.materials[u].dispose();else for(var p=0;p<h.length;p++){var _=l[h[p]],m=i.materials[u];_.material=m,_.getTotalIndices()||(m.pointsCloud=!0)}}e()}catch(t){A.Tools.Warn("Error processing MTL file: '".concat(a,"'")),o._loadingOptions.materialLoadingFailsSilently?e():n(t)}}),(function(t,r){A.Tools.Warn("Error downloading MTL file: '".concat(a,"'")),o._loadingOptions.materialLoadingFailsSilently?e():n(r)}))}))),Promise.all(c).then((function(){var e=function(e){var t,n;return Boolean(null!==(n=null===(t=e._internalMetadata)||void 0===t?void 0:t._isLine)&&void 0!==n&&n)};return l.forEach((function(n){var r,o;if(e(n)){var a=null!==(r=n.material)&&void 0!==r?r:new A.StandardMaterial(n.name+"_line",t);a.getBindedMeshes().filter((function(t){return!e(t)})).length>0&&(a=null!==(o=a.clone(a.name+"_line"))&&void 0!==o?o:a),a.wireframe=!0,n.material=a,n._internalMetadata&&(n._internalMetadata._isLine=void 0)}})),l}))},e.OPTIMIZE_WITH_UV=!0,e.INVERT_Y=!1,e.IMPORT_VERTEX_COLORS=!1,e.COMPUTE_NORMALS=!1,e.OPTIMIZE_NORMALS=!1,e.UV_SCALING=new A.Vector2(1,1),e.SKIP_MATERIALS=!1,e.MATERIAL_LOADING_FAILS_SILENTLY=!0,e.USE_LEGACY_BEHAVIOR=!1,e}();(0,A.registerSceneLoaderPlugin)(new ln);var cn={".stl":{isBinary:!0}},un=function(){function e(){this.solidPattern=/solid (\S*)([\S\s]*?)endsolid[ ]*(\S*)/g,this.facetsPattern=/facet([\s\S]*?)endfacet/g,this.normalPattern=/normal[\s]+([-+]?[0-9]+\.?[0-9]*([eE][-+]?[0-9]+)?)+[\s]+([-+]?[0-9]*\.?[0-9]+([eE][-+]?[0-9]+)?)+[\s]+([-+]?[0-9]*\.?[0-9]+([eE][-+]?[0-9]+)?)+/g,this.vertexPattern=/vertex[\s]+([-+]?[0-9]+\.?[0-9]*([eE][-+]?[0-9]+)?)+[\s]+([-+]?[0-9]*\.?[0-9]+([eE][-+]?[0-9]+)?)+[\s]+([-+]?[0-9]*\.?[0-9]+([eE][-+]?[0-9]+)?)+/g,this.name="stl",this.extensions=cn}return e.prototype.importMesh=function(e,t,n,r,o){var a;if("string"!=typeof n){if(this._isBinary(n)){var i=new A.Mesh("stlmesh",t);return this._parseBinary(i,n),o&&o.push(i),!0}n=(new TextDecoder).decode(new Uint8Array(n))}for(;a=this.solidPattern.exec(n);){var s=a[1],l=a[3];if(l&&s!=l)return A.Tools.Error("Error in STL, solid name != endsolid name"),!1;if(e&&s)if(e instanceof Array){if(!e.indexOf(s))continue}else if(s!==e)continue;s=s||"stlmesh",i=new A.Mesh(s,t),this._parseASCII(i,a[2]),o&&o.push(i)}return!0},e.prototype.load=function(e,t,n){return this.importMesh(null,e,t,n,null)},e.prototype.loadAssetContainer=function(e,t,n){var r=new A.AssetContainer(e);return e._blockEntityCollection=!0,this.importMesh(null,e,t,n,r.meshes),e._blockEntityCollection=!1,r},e.prototype._isBinary=function(e){var t=new DataView(e);if(t.byteLength<=80)return!1;if(84+50*t.getUint32(80,!0)===t.byteLength)return!0;for(var n=[115,111,108,105,100],r=0;r<5;r++)if(t.getUint8(r)!==n[r])return!0;return!1},e.prototype._parseBinary=function(t,n){for(var r=new DataView(n),o=r.getUint32(80,!0),a=0,i=new Float32Array(3*o*3),s=new Float32Array(3*o*3),l=new Uint32Array(3*o),c=0,u=0;u<o;u++){for(var d=84+50*u,h=r.getFloat32(d,!0),f=r.getFloat32(d+4,!0),p=r.getFloat32(d+8,!0),_=1;_<=3;_++){var m=d+12*_;i[a]=r.getFloat32(m,!0),s[a]=h,e.DO_NOT_ALTER_FILE_COORDINATES?(i[a+1]=r.getFloat32(m+4,!0),i[a+2]=r.getFloat32(m+8,!0),s[a+1]=f,s[a+2]=p):(i[a+2]=r.getFloat32(m+4,!0),i[a+1]=r.getFloat32(m+8,!0),s[a+2]=f,s[a+1]=p),a+=3}e.DO_NOT_ALTER_FILE_COORDINATES?(l[c]=c,l[c+1]=c+2,l[c+2]=c+1,c+=3):(l[c]=c++,l[c]=c++,l[c]=c++)}t.setVerticesData(A.VertexBuffer.PositionKind,i),t.setVerticesData(A.VertexBuffer.NormalKind,s),t.setIndices(l),t.computeWorldMatrix(!0)},e.prototype._parseASCII=function(t,n){for(var r,o=[],a=[],i=[],s=0;r=this.facetsPattern.exec(n);){var l=r[1],c=this.normalPattern.exec(l);if(this.normalPattern.lastIndex=0,c){for(var u=[Number(c[1]),Number(c[5]),Number(c[3])],d=void 0;d=this.vertexPattern.exec(l);)e.DO_NOT_ALTER_FILE_COORDINATES?(o.push(Number(d[1]),Number(d[3]),Number(d[5])),a.push(u[0],u[2],u[1])):(o.push(Number(d[1]),Number(d[5]),Number(d[3])),a.push(u[0],u[1],u[2]));e.DO_NOT_ALTER_FILE_COORDINATES?(i.push(s,s+2,s+1),s+=3):i.push(s++,s++,s++),this.vertexPattern.lastIndex=0}}this.facetsPattern.lastIndex=0,t.setVerticesData(A.VertexBuffer.PositionKind,o),t.setVerticesData(A.VertexBuffer.NormalKind,a),t.setIndices(i),t.computeWorldMatrix(!0)},e.DO_NOT_ALTER_FILE_COORDINATES=!1,e}();(0,A.registerSceneLoaderPlugin)(new un);var dn,hn="splat",fn={".splat":{isBinary:!0},".ply":{isBinary:!0}};!function(e){e[e.Splat=0]="Splat",e[e.PointCloud=1]="PointCloud",e[e.Mesh=2]="Mesh"}(dn||(dn={}));var pn=function(){function e(t){void 0===t&&(t=e._DefaultLoadingOptions),this.name=hn,this._assetContainer=null,this.extensions=fn,this._loadingOptions=t}return e.prototype.createPlugin=function(t){return new e(t[hn])},e.prototype.importMeshAsync=function(e,t,n,r,o,a){return b(this,void 0,void 0,(function(){return g(this,(function(o){return[2,this._parse(e,t,n,r).then((function(e){return{meshes:e,particleSystems:[],skeletons:[],animationGroups:[],transformNodes:[],geometries:[],lights:[],spriteManagers:[]}}))]}))}))},e._BuildPointCloud=function(e,t){if(!t.byteLength)return!1;var n=new Uint8Array(t),r=new Float32Array(t),o=n.length/32;return e.addPoints(o,(function(e,t){var o=r[8*t+0],a=r[8*t+1],i=r[8*t+2];e.position=new A.Vector3(o,a,i);var s=n[32*t+24+0]/255,l=n[32*t+24+1]/255,c=n[32*t+24+2]/255;e.color=new A.Color4(s,l,c,1)})),!0},e._BuildMesh=function(e,t){for(var n=new A.Mesh("PLYMesh",e),r=new Uint8Array(t.data),o=new Float32Array(t.data),a=r.length/32,i=[],s=new A.VertexData,l=0;l<a;l++){var c=o[8*l+0],u=o[8*l+1],d=o[8*l+2];i.push(c,u,d)}if(t.hasVertexColors){var h=new Float32Array(4*a);for(l=0;l<a;l++){var f=r[32*l+24+0]/255,p=r[32*l+24+1]/255,_=r[32*l+24+2]/255;h[4*l+0]=f,h[4*l+1]=p,h[4*l+2]=_,h[4*l+3]=1}s.colors=h}return s.positions=i,s.indices=t.faces,s.applyToMesh(n),n},e.prototype._parse=function(t,n,r,o){var a=this;return e._ConvertPLYToSplat(r).then((function(t){return b(a,void 0,void 0,(function(){var r,o,a;return g(this,(function(i){switch(i.label){case 0:switch(r=[],t.mode){case 0:return[3,1];case 1:return[3,3];case 2:return[3,4]}return[3,5];case 1:return(o=new A.GaussianSplattingMesh("GaussianSplatting",null,n,this._loadingOptions.keepInRam))._parentContainer=this._assetContainer,r.push(o),[4,o.updateDataAsync(t.data)];case 2:return i.sent(),[3,6];case 3:return a=new A.PointsCloudSystem("PointCloud",1,n),e._BuildPointCloud(a,t.data)?[2,Promise.all([a.buildMeshAsync()]).then((function(e){return r.push(e[0]),r}))]:(a.dispose(),[3,6]);case 4:if(!t.faces)throw new Error("PLY mesh doesn't contain face informations.");return r.push(e._BuildMesh(n,t)),[3,6];case 5:throw new Error("Unsupported Splat mode");case 6:return[2,new Promise((function(e){e(r)}))]}}))}))}))},e.prototype.loadAssetContainerAsync=function(e,t,n){var r=this,o=new A.AssetContainer(e);return this._assetContainer=o,this.importMeshAsync(null,e,t,n).then((function(e){return e.meshes.forEach((function(e){return o.meshes.push(e)})),r._assetContainer=null,o})).catch((function(e){throw r._assetContainer=null,e}))},e.prototype.loadAsync=function(e,t,n){return this.importMeshAsync(null,e,t,n).then((function(){}))},e._ConvertPLYToSplat=function(e){var t=new Uint8Array(e),n=(new TextDecoder).decode(t.slice(0,10240)),r=n.indexOf("end_header\n");if(r<0||!n)return new Promise((function(t){t({mode:0,data:e})}));var o=parseInt(/element vertex (\d+)\n/.exec(n)[1]),a=/element face (\d+)\n/.exec(n),i=0;a&&(i=parseInt(a[1]));var s=/element chunk (\d+)\n/.exec(n),l=0;s&&(l=parseInt(s[1]));var c,u=0,d=0,h={double:8,int:4,uint:4,float:4,short:2,ushort:2,uchar:1,list:0};!function(e){e[e.Vertex=0]="Vertex",e[e.Chunk=1]="Chunk"}(c||(c={}));for(var f=1,p=[],_=[],m=0,y=n.slice(0,r).split("\n");m<y.length;m++){var v=y[m];if(v.startsWith("property ")){var b=v.split(" "),g=b[1],T=b[2];1==f?(_.push({name:T,type:g,offset:d}),d+=h[g]):0==f&&(p.push({name:T,type:g,offset:u}),u+=h[g]),h[g]||A.Logger.Warn("Unsupported property type: ".concat(g,"."))}else v.startsWith("element ")&&("chunk"==(g=v.split(" ")[1])?f=1:"vertex"==g&&(f=0))}var x=u,O=d;return A.GaussianSplattingMesh.ConvertPLYToSplatAsync(e).then((function(t){var n=new DataView(e,r+11),a=O*l+x*o,s=[];if(i)for(var c=0;c<i;c++){var u=n.getUint8(a);if(3==u){a+=1;for(var d=0;d<u;d++){var h=n.getUint32(a+4*(2-d),!0);s.push(h)}a+=12}}if(l)return new Promise((function(e){e({mode:0,data:t,faces:s,hasVertexColors:!1})}));for(var f=0,_=0,m=["x","y","z","scale_0","scale_1","scale_2","opacity","rot_0","rot_1","rot_2","rot_3"],y=["red","green","blue","f_dc_0","f_dc_1","f_dc_2"],v=0;v<p.length;v++){var b=p[v];m.includes(b.name)&&f++,y.includes(b.name)&&_++}var g=f==m.length&&3==_,A=i?2:g?0:1;return new Promise((function(e){e({mode:A,data:t,faces:s,hasVertexColors:!!_})}))}))},e._DefaultLoadingOptions={keepInRam:!1},e}();(0,A.registerSceneLoaderPlugin)(new pn);var _n=void 0!==a.g?a.g:"undefined"!=typeof window?window:void 0;if(void 0!==_n){for(var mn in _n.BABYLON=_n.BABYLON||{},l)_n.BABYLON[mn]=l[mn];for(var mn in s)_n.BABYLON[mn]=s[mn]}var yn=void 0!==a.g?a.g:"undefined"!=typeof window?window:void 0;if(void 0!==yn)for(var vn in yn.BABYLON=yn.BABYLON||{},yn.BABYLON.GLTF1=yn.BABYLON.GLTF1||{},c)yn.BABYLON.GLTF1[vn]=c[vn];var bn=void 0!==a.g?a.g:"undefined"!=typeof window?window:void 0;if(void 0!==bn){bn.BABYLON=bn.BABYLON||{};var gn=bn.BABYLON;gn.GLTF2=gn.GLTF2||{},gn.GLTF2.Loader=gn.GLTF2.Loader||{},gn.GLTF2.Loader.Extensions=gn.GLTF2.Loader.Extensions||{};var An=[];for(var Tn in d)gn.GLTF2.Loader.Extensions[Tn]=d[Tn],An.push(Tn);for(var Tn in u)gn.GLTF2.Loader[Tn]=u[Tn],An.push(Tn);for(var Tn in h)An.indexOf(Tn)>-1||(gn.GLTF2[Tn]=h[Tn])}var xn=void 0!==a.g?a.g:"undefined"!=typeof window?window:void 0;if(void 0!==xn)for(var On in f)xn.BABYLON[On]||(xn.BABYLON[On]=f[On]);var En=void 0!==a.g?a.g:"undefined"!=typeof window?window:void 0;if(void 0!==En)for(var Mn in p)En.BABYLON[Mn]||(En.BABYLON[Mn]=p[Mn]);const wn=_;return i.default})()));
//# sourceMappingURL=babylonjs.loaders.min.js.map