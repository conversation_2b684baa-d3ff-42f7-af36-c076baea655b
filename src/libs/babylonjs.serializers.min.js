!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("babylonjs")):"function"==typeof define&&define.amd?define("babylonjs-serializers",["babylonjs"],t):"object"==typeof exports?exports["babylonjs-serializers"]=t(require("babylonjs")):e.SERIALIZERS=t(e.BABYLON)}("undefined"!=typeof self?self:"undefined"!=typeof global?global:this,(e=>(()=>{"use strict";var t={597:t=>{t.exports=e}},r={};function n(e){var a=r[e];if(void 0!==a)return a.exports;var o=r[e]={exports:{}};return t[e](o,o.exports,n),o.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var a={};n.d(a,{default:()=>Pe});var o={};n.r(o),n.d(o,{OBJExport:()=>g});var i={};n.r(i),n.d(i,{__IGLTFExporterExtension:()=>_});var s={};n.r(s),n.d(s,{GLTFData:()=>T});var u={};n.r(u),n.d(u,{GLTF2Export:()=>B});var l={};n.r(l),n.d(l,{EXT_mesh_gpu_instancing:()=>oe,KHR_lights_punctual:()=>L,KHR_materials_anisotropy:()=>q,KHR_materials_clearcoat:()=>k,KHR_materials_diffuse_transmission:()=>le,KHR_materials_dispersion:()=>te,KHR_materials_emissive_strength:()=>se,KHR_materials_ior:()=>Y,KHR_materials_iridescence:()=>D,KHR_materials_sheen:()=>z,KHR_materials_specular:()=>Z,KHR_materials_transmission:()=>ne,KHR_materials_unlit:()=>j,KHR_materials_volume:()=>$,KHR_texture_transform:()=>O});var c={};n.r(c),n.d(c,{EXT_mesh_gpu_instancing:()=>oe,GLTF2Export:()=>B,GLTFData:()=>T,KHR_lights_punctual:()=>L,KHR_materials_anisotropy:()=>q,KHR_materials_clearcoat:()=>k,KHR_materials_diffuse_transmission:()=>le,KHR_materials_dispersion:()=>te,KHR_materials_emissive_strength:()=>se,KHR_materials_ior:()=>Y,KHR_materials_iridescence:()=>D,KHR_materials_sheen:()=>z,KHR_materials_specular:()=>Z,KHR_materials_transmission:()=>ne,KHR_materials_unlit:()=>j,KHR_materials_volume:()=>$,KHR_texture_transform:()=>O,_BinaryWriter:()=>V,_Exporter:()=>S,_GLTFAnimation:()=>y,_GLTFMaterialExporter:()=>E,_GLTFUtilities:()=>x,__IGLTFExporterExtensionV2:()=>I});var f={};n.r(f),n.d(f,{STLExport:()=>ce});var p={};n.r(p),n.d(p,{USDZExportAsync:()=>Ae});var h={};n.r(h),n.d(h,{EXT_mesh_gpu_instancing:()=>oe,GLTF2Export:()=>B,GLTFData:()=>T,KHR_lights_punctual:()=>L,KHR_materials_anisotropy:()=>q,KHR_materials_clearcoat:()=>k,KHR_materials_diffuse_transmission:()=>le,KHR_materials_dispersion:()=>te,KHR_materials_emissive_strength:()=>se,KHR_materials_ior:()=>Y,KHR_materials_iridescence:()=>D,KHR_materials_sheen:()=>z,KHR_materials_specular:()=>Z,KHR_materials_transmission:()=>ne,KHR_materials_unlit:()=>j,KHR_materials_volume:()=>$,KHR_texture_transform:()=>O,OBJExport:()=>g,STLExport:()=>ce,USDZExportAsync:()=>Ae,_BinaryWriter:()=>V,_Exporter:()=>S,_GLTFAnimation:()=>y,_GLTFMaterialExporter:()=>E,_GLTFUtilities:()=>x,__IGLTFExporterExtension:()=>_,__IGLTFExporterExtensionV2:()=>I});var d,m=n(597),g=function(){function e(){}return e.OBJ=function(e,t,r,n){var a=[],o=1,i=1;t&&(r||(r="mat"),a.push("mtllib "+r+".mtl"));for(var s=0;s<e.length;s++){var u=e[s],l=u.name||"mesh".concat(s,"}");a.push("o ".concat(l));var c=null;if(n){var f=u.computeWorldMatrix(!0);c=new m.Matrix,f.invertToRef(c),u.bakeTransformIntoVertices(f)}if(t){var p=u.material;p&&a.push("usemtl "+p.id)}var h=u.geometry;if(h){var d=h.getVerticesData("position"),g=h.getVerticesData("normal"),_=h.getVerticesData("uv"),x=h.getIndices(),y=0,T=0;if(d&&x){for(var v=e[0].getScene().useRightHandedSystem?1:-1,b=0;b<d.length;b+=3)a.push("v "+d[b]*v+" "+d[b+1]+" "+d[b+2]),y++;if(null!=g)for(b=0;b<g.length;b+=3)a.push("vn "+g[b]*v+" "+g[b+1]+" "+g[b+2]);if(null!=_)for(b=0;b<_.length;b+=2)a.push("vt "+_[b]+" "+_[b+1]),T++;var A=["","",""],M=(u.material||u.getScene().defaultMaterial)._getEffectiveOrientation(u)===m.Material.ClockWiseSideOrientation?[2,1]:[1,2],E=M[0],w=M[1];for(b=0;b<x.length;b+=3){var F=[String(x[b]+o),String(x[b+E]+o),String(x[b+w]+o)],C=[String(x[b]+i),String(x[b+E]+i),String(x[b+w]+i)],R=F,S=null!=_?C:A,V=null!=g?F:A;a.push("f "+R[0]+"/"+S[0]+"/"+V[0]+" "+R[1]+"/"+S[1]+"/"+V[1]+" "+R[2]+"/"+S[2]+"/"+V[2])}n&&c&&u.bakeTransformIntoVertices(c),o+=y,i+=T}else m.Tools.Warn("There are no position vertices or indices on the mesh!")}else m.Tools.Warn("No geometry is present on the mesh")}return a.join("\n")},e.MTL=function(e){var t=[],r=e.material;return t.push("newmtl mat1"),t.push("  Ns "+r.specularPower.toFixed(4)),t.push("  Ni 1.5000"),t.push("  d "+r.alpha.toFixed(4)),t.push("  Tr 0.0000"),t.push("  Tf 1.0000 1.0000 1.0000"),t.push("  illum 2"),t.push("  Ka "+r.ambientColor.r.toFixed(4)+" "+r.ambientColor.g.toFixed(4)+" "+r.ambientColor.b.toFixed(4)),t.push("  Kd "+r.diffuseColor.r.toFixed(4)+" "+r.diffuseColor.g.toFixed(4)+" "+r.diffuseColor.b.toFixed(4)),t.push("  Ks "+r.specularColor.r.toFixed(4)+" "+r.specularColor.g.toFixed(4)+" "+r.specularColor.b.toFixed(4)),t.push("  Ke "+r.emissiveColor.r.toFixed(4)+" "+r.emissiveColor.g.toFixed(4)+" "+r.emissiveColor.b.toFixed(4)),r.ambientTexture&&t.push("  map_Ka "+r.ambientTexture.name),r.diffuseTexture&&t.push("  map_Kd "+r.diffuseTexture.name),r.specularTexture&&t.push("  map_Ks "+r.specularTexture.name),r.bumpTexture&&t.push("  map_bump -imfchan z "+r.bumpTexture.name),r.opacityTexture&&t.push("  map_d "+r.opacityTexture.name),t.join("\n")},e}(),_=0,x=function(){function e(){}return e._CreateBufferView=function(e,t,r,n,a){var o={buffer:e,byteLength:r};return t&&(o.byteOffset=t),a&&(o.name=a),n&&(o.byteStride=n),o},e._CreateAccessor=function(e,t,r,n,a,o,i,s){var u={name:t,bufferView:e,componentType:n,count:a,type:r};return null!=i&&(u.min=i),null!=s&&(u.max=s),null!=o&&(u.byteOffset=o),u},e._CalculateMinMaxPositions=function(e,t,r){var n,a,o=[1/0,1/0,1/0],i=[-1/0,-1/0,-1/0];if(r)for(var s=t,u=t+r;s<u;++s){n=3*s,a=m.Vector3.FromArray(e,n).asArray();for(var l=0;l<3;++l){var c=a[l];c<o[l]&&(o[l]=c),c>i[l]&&(i[l]=c),++n}}return{min:o,max:i}},e._NormalizeTangentFromRef=function(e){var t=Math.sqrt(e.x*e.x+e.y*e.y+e.z*e.z);t>0&&(e.x/=t,e.y/=t,e.z/=t)},e._GetDataAccessorElementCount=function(e){switch(e){case"MAT2":case"VEC4":return 4;case"MAT3":return 9;case"MAT4":return 16;case"SCALAR":return 1;case"VEC2":return 2;case"VEC3":return 3}},e}();!function(e){e[e.INTANGENT=0]="INTANGENT",e[e.OUTTANGENT=1]="OUTTANGENT"}(d||(d={}));var y=function(){function e(){}return e._IsTransformable=function(e){return e&&(e instanceof m.TransformNode||e instanceof m.Camera||e instanceof m.Light)},e._CreateNodeAnimation=function(t,r,n,a,o){if(this._IsTransformable(t)){var i=[],s=[],u=r.getKeys(),l=e._CalculateMinMaxKeyFrames(u),c=e._DeduceInterpolation(u,n,a),f=c.interpolationType,p=c.shouldBakeAnimation;if(p?e._CreateBakedAnimation(t,r,n,l.min,l.max,r.framePerSecond,o,i,s,l,a):"LINEAR"===f||"STEP"===f?e._CreateLinearOrStepAnimation(t,r,n,i,s,a):"CUBICSPLINE"===f?e._CreateCubicSplineAnimation(t,r,n,i,s,a):e._CreateBakedAnimation(t,r,n,l.min,l.max,r.framePerSecond,o,i,s,l,a),i.length&&s.length)return{inputs:i,outputs:s,samplerInterpolation:f,inputsMin:p?l.min:m.Tools.FloatRound(l.min/r.framePerSecond),inputsMax:p?l.max:m.Tools.FloatRound(l.max/r.framePerSecond)}}return null},e._DeduceAnimationInfo=function(e){var t=null,r="VEC3",n=!1,a=e.targetProperty.split(".");switch(a[0]){case"scaling":t="scale";break;case"position":t="translation";break;case"rotation":r="VEC4",t="rotation";break;case"rotationQuaternion":r="VEC4",n=!0,t="rotation";break;case"influence":r="SCALAR",t="weights";break;default:m.Tools.Error("Unsupported animatable property ".concat(a[0]))}return t?{animationChannelTargetPath:t,dataAccessorType:r,useQuaternion:n}:(m.Tools.Error("animation channel target path and data accessor type could be deduced"),null)},e._CreateNodeAnimationFromNodeAnimations=function(t,r,n,a,o,i,s,u,l,c){var f;if(e._IsTransformable(t)&&t.animations)for(var p=0,h=t.animations;p<h.length;p++){var d=h[p];if(!c||c(d)){var m=e._DeduceAnimationInfo(d);m&&(f={name:d.name,samplers:[],channels:[]},e._AddAnimation("".concat(d.name),d.hasRunningRuntimeAnimations?r:f,t,d,m.dataAccessorType,m.animationChannelTargetPath,a,i,s,u,m.useQuaternion,l),f.samplers.length&&f.channels.length&&n.push(f))}}},e._CreateMorphTargetAnimationFromMorphTargetAnimations=function(t,r,n,a,o,i,s,u,l,c){var f;if(t instanceof m.Mesh){var p=t.morphTargetManager;if(p)for(var h=0;h<p.numTargets;++h)for(var d=0,g=p.getTarget(h).animations;d<g.length;d++){var _=g[d];if(!c||c(_)){for(var x=new m.Animation("".concat(_.name),"influence",_.framePerSecond,_.dataType,_.loopMode,_.enableBlending),y=[],T=_.getKeys(),v=0;v<T.length;++v)for(var b=T[v],A=0;A<p.numTargets;++A)A==h?y.push(b):y.push({frame:b.frame,value:0});x.setKeys(y);var M=e._DeduceAnimationInfo(x);M&&(f={name:x.name,samplers:[],channels:[]},e._AddAnimation(_.name,_.hasRunningRuntimeAnimations?r:f,t,x,M.dataAccessorType,M.animationChannelTargetPath,a,i,s,u,M.useQuaternion,l,p.numTargets),f.samplers.length&&f.channels.length&&n.push(f))}}}},e._CreateNodeAndMorphAnimationFromAnimationGroups=function(t,r,n,a,o,i,s,u){var l,c;if(t.animationGroups)for(var f=t.animationGroups,p=function(f){var p=new Map,d=new Map,g=new Set,_=f.to-f.from;c={name:f.name,channels:[],samplers:[]};for(var x=function(r){var _=f.targetedAnimations[r],x=_.target,y=_.animation;if(u&&!u(y))return"continue";if(h._IsTransformable(x)||1===x.length&&h._IsTransformable(x[0])){if(v=e._DeduceAnimationInfo(_.animation)){var T=h._IsTransformable(x)?x:h._IsTransformable(x[0])?x[0]:null;T&&e._AddAnimation("".concat(y.name),c,T,y,v.dataAccessorType,v.animationChannelTargetPath,n,a,o,i,v.useQuaternion,s)}}else if(x instanceof m.MorphTarget||1===x.length&&x[0]instanceof m.MorphTarget){var v;if(v=e._DeduceAnimationInfo(_.animation)){var b=x instanceof m.MorphTarget?x:x[0];if(b){var A=t.morphTargetManagers.find((function(e){for(var t=0;t<e.numTargets;++t)if(e.getTarget(t)===b)return!0;return!1}));if(A){var M=t.meshes.find((function(e){return e.morphTargetManager===A}));M&&(p.has(M)||p.set(M,new Map),null===(l=p.get(M))||void 0===l||l.set(b,y),g.add(M),d.set(M,y))}}}}},y=0;y<f.targetedAnimations.length;++y)x(y);g.forEach((function(t){for(var r=t.morphTargetManager,u=null,l=[],h=d.get(t).getKeys(),g=h.length,x=0;x<g;++x)for(var y=0;y<r.numTargets;++y){var T=r.getTarget(y),v=p.get(t);if(v){var b=v.get(T);b?(u||(u=new m.Animation("".concat(f.name,"_").concat(t.name,"_MorphWeightAnimation"),"influence",b.framePerSecond,m.Animation.ANIMATIONTYPE_FLOAT,b.loopMode,b.enableBlending)),l.push(b.getKeys()[x])):l.push({frame:f.from+_/g*x,value:T.influence,inTangent:h[0].inTangent?0:void 0,outTangent:h[0].outTangent?0:void 0})}}u.setKeys(l);var A=e._DeduceAnimationInfo(u);A&&e._AddAnimation("".concat(f.name,"_").concat(t.name,"_MorphWeightAnimation"),c,t,u,A.dataAccessorType,A.animationChannelTargetPath,n,a,o,i,A.useQuaternion,s,null==r?void 0:r.numTargets)})),c.channels.length&&c.samplers.length&&r.push(c)},h=this,d=0,g=f;d<g.length;d++)p(g[d])},e._AddAnimation=function(t,r,n,a,o,i,s,u,l,c,f,p,h){var d,m,g,_,y,T,v,b=e._CreateNodeAnimation(n,a,i,f,p);if(b){if(h){for(var A=0,M=0,E=[];b.inputs.length>0;)M=b.inputs.shift(),A%h==0&&E.push(M),A++;b.inputs=E}var w=s[n.uniqueId],F=4*b.inputs.length;d=x._CreateBufferView(0,u.getByteOffset(),F,void 0,"".concat(t,"  keyframe data view")),l.push(d),b.inputs.forEach((function(e){u.setFloat32(e)})),m=x._CreateAccessor(l.length-1,"".concat(t,"  keyframes"),"SCALAR",5126,b.inputs.length,null,[b.inputsMin],[b.inputsMax]),c.push(m),g=c.length-1,y=b.outputs.length,F=4*x._GetDataAccessorElementCount(o)*b.outputs.length,d=x._CreateBufferView(0,u.getByteOffset(),F,void 0,"".concat(t,"  data view")),l.push(d),b.outputs.forEach((function(e){e.forEach((function(e){u.setFloat32(e)}))})),m=x._CreateAccessor(l.length-1,"".concat(t,"  data"),o,5126,y,null,null,null),c.push(m),_=c.length-1,T={interpolation:b.samplerInterpolation,input:g,output:_},r.samplers.push(T),v={sampler:r.samplers.length-1,target:{node:w,path:i}},r.channels.push(v)}},e._CreateBakedAnimation=function(t,r,n,a,o,i,s,u,l,c,f){var p,h,d=m.Quaternion.Identity(),g=null,_=null,x=null,y=null,T=null,v=null;c.min=m.Tools.FloatRound(a/i);for(var b=r.getKeys(),A=0,M=b.length;A<M;++A){if(v=null,x=b[A],A+1<M)if(y=b[A+1],x.value.equals&&x.value.equals(y.value)||x.value===y.value){if(0!==A)continue;v=x.frame}else v=y.frame;else{if(T=b[A-1],x.value.equals&&x.value.equals(T.value)||x.value===T.value)continue;v=o}if(v)for(var E=x.frame;E<=v;E+=s)if((h=m.Tools.FloatRound(E/i))!==g){g=h,_=h;var w={key:0,repeatCount:0,loopMode:r.loopMode};p=r._interpolate(E,w),e._SetInterpolatedValue(t,p,h,r,n,d,u,l,f)}}_&&(c.max=_)},e._ConvertFactorToVector3OrQuaternion=function(t,r,n,a,o){var i=e._GetBasePositionRotationOrScale(r,a,o),s=n.targetProperty.split("."),u=s?s[1]:"",l=o?m.Quaternion.FromArray(i).normalize():m.Vector3.FromArray(i);switch(u){case"x":case"y":case"z":l[u]=t;break;case"w":l.w=t;break;default:m.Tools.Error('glTFAnimation: Unsupported component name "'.concat(u,'"!'))}return l},e._SetInterpolatedValue=function(e,t,r,n,a,o,i,s,u){var l;i.push(r),"weights"!==a?(n.dataType===m.Animation.ANIMATIONTYPE_FLOAT&&(t=this._ConvertFactorToVector3OrQuaternion(t,e,n,a,u)),"rotation"===a?(u?o=t:(l=t,m.Quaternion.RotationYawPitchRollToRef(l.y,l.x,l.z,o)),s.push(o.asArray())):(l=t,s.push(l.asArray()))):s.push([t])},e._CreateLinearOrStepAnimation=function(t,r,n,a,o,i){for(var s=0,u=r.getKeys();s<u.length;s++){var l=u[s];a.push(l.frame/r.framePerSecond),e._AddKeyframeValue(l,r,o,n,t,i)}},e._CreateCubicSplineAnimation=function(t,r,n,a,o,i){r.getKeys().forEach((function(s){a.push(s.frame/r.framePerSecond),e._AddSplineTangent(d.INTANGENT,o,n,"CUBICSPLINE",s,i),e._AddKeyframeValue(s,r,o,n,t,i),e._AddSplineTangent(d.OUTTANGENT,o,n,"CUBICSPLINE",s,i)}))},e._GetBasePositionRotationOrScale=function(e,t,r){var n;if("rotation"===t)if(r){var a=e.rotationQuaternion;n=(null!=a?a:m.Quaternion.Identity()).asArray()}else{var o=e.rotation;n=(null!=o?o:m.Vector3.Zero()).asArray()}else if("translation"===t){var i=e.position;n=(null!=i?i:m.Vector3.Zero()).asArray()}else{var s=e.scaling;n=(null!=s?s:m.Vector3.One()).asArray()}return n},e._AddKeyframeValue=function(e,t,r,n,a,o){var i,s=t.dataType;if(s===m.Animation.ANIMATIONTYPE_VECTOR3){var u=e.value.asArray();if("rotation"===n){var l=m.Vector3.FromArray(u);u=m.Quaternion.RotationYawPitchRoll(l.y,l.x,l.z).asArray()}r.push(u)}else if(s===m.Animation.ANIMATIONTYPE_FLOAT){if("weights"===n)r.push([e.value]);else if(i=this._ConvertFactorToVector3OrQuaternion(e.value,a,t,n,o)){if("rotation"===n){var c=o?i:m.Quaternion.RotationYawPitchRoll(i.y,i.x,i.z).normalize();r.push(c.asArray())}r.push(i.asArray())}}else s===m.Animation.ANIMATIONTYPE_QUATERNION?r.push(e.value.normalize().asArray()):m.Tools.Error("glTFAnimation: Unsupported key frame values for animation!")},e._DeduceInterpolation=function(e,t,r){var n,a,o=!1;if("rotation"===t&&!r)return{interpolationType:"LINEAR",shouldBakeAnimation:!0};for(var i=0,s=e.length;i<s;++i)if((a=e[i]).inTangent||a.outTangent)if(n){if("CUBICSPLINE"!==n){n="LINEAR",o=!0;break}}else n="CUBICSPLINE";else if(n){if("CUBICSPLINE"===n||a.interpolation&&1===a.interpolation&&"STEP"!==n){n="LINEAR",o=!0;break}}else n=a.interpolation&&1===a.interpolation?"STEP":"LINEAR";return n||(n="LINEAR"),{interpolationType:n,shouldBakeAnimation:o}},e._AddSplineTangent=function(e,t,r,n,a,o){var i,s=e===d.INTANGENT?a.inTangent:a.outTangent;if("CUBICSPLINE"===n){if("rotation"===r)if(s)if(o)i=s.asArray();else{var u=s;i=m.Quaternion.RotationYawPitchRoll(u.y,u.x,u.z).asArray()}else i=[0,0,0,0];else i="weights"===r?s?[s]:[0]:s?s.asArray():[0,0,0];t.push(i)}},e._CalculateMinMaxKeyFrames=function(e){var t=1/0,r=-1/0;return e.forEach((function(e){t=Math.min(t,e.frame),r=Math.max(r,e.frame)})),{min:t,max:r}},e}(),T=function(){function e(){this.glTFFiles={}}return e.prototype.downloadFiles=function(){function e(e,t){return-1!==e.indexOf(t,e.length-t.length)}for(var t in this.glTFFiles){var r=document.createElement("a");document.body.appendChild(r),r.setAttribute("type","hidden"),r.download=t;var n=this.glTFFiles[t],a=void 0;e(t,".glb")?a={type:"model/gltf-binary"}:e(t,".bin")?a={type:"application/octet-stream"}:e(t,".gltf")?a={type:"model/gltf+json"}:e(t,".jpeg")||e(t,".jpg")?a={type:"image/jpeg"}:e(t,".png")&&(a={type:"image/png"}),r.href=window.URL.createObjectURL(new Blob([n],a)),r.click()}},e}(),v=function(){return v=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var a in t=arguments[r])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},v.apply(this,arguments)};function b(e,t,r,n){return new(r||(r=Promise))((function(a,o){function i(e){try{u(n.next(e))}catch(e){o(e)}}function s(e){try{u(n.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}u((n=n.apply(e,t||[])).next())}))}function A(e,t){var r,n,a,o={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(o=0)),o;)try{if(r=1,n&&(a=2&s[0]?n.return:s[0]?n.throw||((a=n.return)&&a.call(n),0):n.next)&&!(a=a.call(n,s[1])).done)return a;switch(n=0,a&&(s=[2&s[0],a.value]),s[0]){case 0:case 1:a=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,n=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!((a=(a=o.trys).length>0&&a[a.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!a||s[1]>a[0]&&s[1]<a[3])){o.label=s[1];break}if(6===s[0]&&o.label<a[1]){o.label=a[1],a=s;break}if(a&&o.label<a[2]){o.label=a[2],o.ops.push(s);break}a[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],n=0}finally{r=a=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}}function M(e,t,r){if(r||2===arguments.length)for(var n,a=0,o=t.length;a<o;a++)!n&&a in t||(n||(n=Array.prototype.slice.call(t,0,a)),n[a]=t[a]);return e.concat(n||Array.prototype.slice.call(t))}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var E=function(){function e(e){this._textureMap={},this._internalTextureToImage={},this._textureMap={},this._exporter=e}return e._FuzzyEquals=function(e,t,r){return m.Scalar.WithinEpsilon(e.r,t.r,r)&&m.Scalar.WithinEpsilon(e.g,t.g,r)&&m.Scalar.WithinEpsilon(e.b,t.b,r)},e.prototype._convertMaterialsToGLTFAsync=function(e,t,r){var n=this,a=[];return e.forEach((function(e){"StandardMaterial"===e.getClassName()?a.push(n._convertStandardMaterialAsync(e,t,r)):-1!==e.getClassName().indexOf("PBR")?a.push(n._convertPBRMaterialAsync(e,t,r)):m.Tools.Warn("Unsupported material type: ".concat(e.name))})),Promise.all(a).then((function(){}))},e.prototype._stripTexturesFromMaterial=function(e){var t={};if(e){t.name=e.name,t.doubleSided=e.doubleSided,t.alphaMode=e.alphaMode,t.alphaCutoff=e.alphaCutoff,t.emissiveFactor=e.emissiveFactor;var r=e.pbrMetallicRoughness;r&&(t.pbrMetallicRoughness={},t.pbrMetallicRoughness.baseColorFactor=r.baseColorFactor,t.pbrMetallicRoughness.metallicFactor=r.metallicFactor,t.pbrMetallicRoughness.roughnessFactor=r.roughnessFactor)}return t},e.prototype._hasTexturesPresent=function(e){var t;if(e.emissiveTexture||e.normalTexture||e.occlusionTexture)return!0;var r=e.pbrMetallicRoughness;if(r&&(r.baseColorTexture||r.metallicRoughnessTexture))return!0;if(e.extensions)for(var n in e.extensions){var a=e.extensions[n];if(a)return null===(t=a.hasTextures)||void 0===t?void 0:t.call(a)}return!1},e.prototype._getTextureInfo=function(e){if(e){var t=e.uid;if(t in this._textureMap)return this._textureMap[t]}return null},e.prototype._convertToGLTFPBRMetallicRoughness=function(t){var r,n,a,o,i,s,u=new m.Vector2(0,1),l=new m.Vector2(0,.1),c=new m.Vector2(0,.1),f=new m.Vector2(1300,.1),p=t.diffuseColor.toLinearSpace(t.getScene().getEngine().useExactSrgbConversions).scale(.5),h=t.alpha,d=(r=m.Scalar.Clamp(t.specularPower,0,e._MaxSpecularPower),n=Math.pow(r/f.x,.333333),a=u.y,o=l.y,i=c.y,s=f.y,(1-n)*(1-n)*(1-n)*a+3*(1-n)*(1-n)*n*o+3*(1-n)*n*n*i+n*n*n*s);return{baseColorFactor:[p.r,p.g,p.b,h],metallicFactor:0,roughnessFactor:d}},e._SolveMetallic=function(e,t,r){if(t<this._DielectricSpecular.r)return this._DielectricSpecular,0;var n=this._DielectricSpecular.r,a=e*r/(1-this._DielectricSpecular.r)+t-2*this._DielectricSpecular.r,o=a*a-4*n*(this._DielectricSpecular.r-t);return m.Scalar.Clamp((-a+Math.sqrt(o))/(2*n),0,1)},e._SetAlphaMode=function(e,t){t.needAlphaBlending()?e.alphaMode="BLEND":t.needAlphaTesting()&&(e.alphaMode="MASK",e.alphaCutoff=t.alphaCutOff)},e.prototype._convertStandardMaterialAsync=function(t,r,n){var a=this._exporter._materialMap,o=this._exporter._materials,i=[],s=this._convertToGLTFPBRMetallicRoughness(t),u={name:t.name};if(null==t.backFaceCulling||t.backFaceCulling||(t.twoSidedLighting||m.Tools.Warn(t.name+": Back-face culling disabled and two-sided lighting disabled is not supported in glTF."),u.doubleSided=!0),n){t.diffuseTexture&&i.push(this._exportTextureAsync(t.diffuseTexture,r).then((function(e){e&&(s.baseColorTexture=e)})));var l=t.bumpTexture;l&&i.push(this._exportTextureAsync(l,r).then((function(e){e&&(u.normalTexture=e,1!==l.level&&(u.normalTexture.scale=l.level))}))),t.emissiveTexture&&(u.emissiveFactor=[1,1,1],i.push(this._exportTextureAsync(t.emissiveTexture,r).then((function(e){e&&(u.emissiveTexture=e)})))),t.ambientTexture&&i.push(this._exportTextureAsync(t.ambientTexture,r).then((function(e){if(e){var t={index:e.index};u.occlusionTexture=t}})))}return(t.alpha<1||t.opacityTexture)&&(t.alphaMode===m.Constants.ALPHA_COMBINE?u.alphaMode="BLEND":m.Tools.Warn(t.name+": glTF 2.0 does not support alpha mode: "+t.alphaMode.toString())),t.emissiveColor&&!e._FuzzyEquals(t.emissiveColor,m.Color3.Black(),e._Epsilon)&&(u.emissiveFactor=t.emissiveColor.asArray()),u.pbrMetallicRoughness=s,e._SetAlphaMode(u,t),o.push(u),a[t.uniqueId]=o.length-1,this._finishMaterial(i,u,t,r)},e.prototype._finishMaterial=function(e,t,r,n){var a=this;return Promise.all(e).then((function(){for(var e=null,o=0,i=a._exporter._extensionsPostExportMaterialAdditionalTextures("exportMaterial",t,r);o<i.length;o++){var s=i[o];e||(e=[]),e.push(a._exportTextureAsync(s,n))}return e||(e=[Promise.resolve(null)]),Promise.all(e).then((function(){var e=a._exporter._extensionsPostExportMaterialAsync("exportMaterial",t,r);return e?e.then((function(){return t})):t}))}))},e.prototype._getImageDataAsync=function(e,t,r,n){return b(this,void 0,void 0,(function(){var a,o,i,s,u;return A(this,(function(l){switch(l.label){case 0:return a=m.Constants.TEXTURETYPE_UNSIGNED_INT,o=this._exporter._babylonScene,i=o.getEngine(),s=i.createRawTexture(e,t,r,m.Constants.TEXTUREFORMAT_RGBA,!1,!0,m.Texture.NEAREST_SAMPLINGMODE,null,a),[4,m.TextureTools.ApplyPostProcess("pass",s,o,a,m.Constants.TEXTURE_NEAREST_SAMPLINGMODE,m.Constants.TEXTUREFORMAT_RGBA)];case 1:return l.sent(),[4,i._readTexturePixels(s,t,r)];case 2:return u=l.sent(),[4,m.DumpTools.DumpDataAsync(t,r,u,n,void 0,!0,!0)];case 3:return[2,l.sent()]}}))}))},e.prototype._createWhiteTexture=function(e,t,r){for(var n=new Uint8Array(e*t*4),a=0;a<n.length;a+=4)n[a]=n[a+1]=n[a+2]=n[a+3]=255;return m.RawTexture.CreateRGBATexture(n,e,t,r)},e.prototype._resizeTexturesToSameDimensions=function(e,t,r){var n,a,o=e?e.getSize():{width:0,height:0},i=t?t.getSize():{width:0,height:0};return o.width<i.width?(n=e&&e instanceof m.Texture?m.TextureTools.CreateResizedCopy(e,i.width,i.height,!0):this._createWhiteTexture(i.width,i.height,r),a=t):o.width>i.width?(a=t&&t instanceof m.Texture?m.TextureTools.CreateResizedCopy(t,o.width,o.height,!0):this._createWhiteTexture(o.width,o.height,r),n=e):(n=e,a=t),{texture1:n,texture2:a}},e.prototype._convertPixelArrayToFloat32=function(e){if(e instanceof Uint8Array){for(var t=e.length,r=new Float32Array(e.length),n=0;n<t;++n)r[n]=e[n]/255;return r}if(e instanceof Float32Array)return e;throw new Error("Unsupported pixel format!")},e.prototype._convertSpecularGlossinessTexturesToMetallicRoughnessAsync=function(t,r,n,a){return b(this,void 0,void 0,(function(){var o,i,s,u,l,c,f,p,h,d,g,_,x,y,T,v,b,M,E,w,F,C,R,S,V,I,B,P,O,N,L,U;return A(this,(function(A){switch(A.label){case 0:return o=new Array,t||r?(i=t?t.getScene():r?r.getScene():null)?(s=this._resizeTexturesToSameDimensions(t,r,i),u=null===(U=s.texture1)||void 0===U?void 0:U.getSize(),l=void 0,c=void 0,f=u.width,p=u.height,[4,s.texture1.readPixels()]):[3,3]:[2,Promise.reject("_ConvertSpecularGlosinessTexturesToMetallicRoughness: diffuse and specular glossiness textures are not defined!")];case 1:return h=A.sent(),[4,s.texture2.readPixels()];case 2:if(d=A.sent(),!h)return[2,Promise.reject("Failed to retrieve pixels from diffuse texture!")];if(l=this._convertPixelArrayToFloat32(h),!d)return[2,Promise.reject("Failed to retrieve pixels from specular glossiness texture!")];for(c=this._convertPixelArrayToFloat32(d),g=c.byteLength,_=new Uint8Array(g),x=new Uint8Array(g),y=m.Color3.Black(),T=0,v=0,I=0;I<p;++I)for(B=0;B<f;++B)b=4*(f*I+B),M=new m.Color3(l[b],l[b+1],l[b+2]).toLinearSpace(i.getEngine().useExactSrgbConversions).multiply(n.diffuseColor),E=new m.Color3(c[b],c[b+1],c[b+2]).toLinearSpace(i.getEngine().useExactSrgbConversions).multiply(n.specularColor),w=c[b+3]*n.glossiness,F={diffuseColor:M,specularColor:E,glossiness:w},C=this._convertSpecularGlossinessToMetallicRoughness(F),y.r=Math.max(y.r,C.baseColor.r),y.g=Math.max(y.g,C.baseColor.g),y.b=Math.max(y.b,C.baseColor.b),T=Math.max(T,C.metallic),v=Math.max(v,C.roughness),x[b]=255*C.baseColor.r,x[b+1]=255*C.baseColor.g,x[b+2]=255*C.baseColor.b,x[b+3]=s.texture1.hasAlpha?255*l[b+3]:255,_[b]=0,_[b+1]=255*C.roughness,_[b+2]=255*C.metallic,_[b+3]=255;for(R={baseColor:y,metallic:T,roughness:v},S=!1,V=!1,I=0;I<p;++I)for(B=0;B<f;++B)x[P=4*(f*I+B)]/=R.baseColor.r>e._Epsilon?R.baseColor.r:1,x[P+1]/=R.baseColor.g>e._Epsilon?R.baseColor.g:1,x[P+2]/=R.baseColor.b>e._Epsilon?R.baseColor.b:1,O=m.Color3.FromInts(x[P],x[P+1],x[P+2]),N=O.toGammaSpace(i.getEngine().useExactSrgbConversions),x[P]=255*N.r,x[P+1]=255*N.g,x[P+2]=255*N.b,e._FuzzyEquals(N,m.Color3.White(),e._Epsilon)||(V=!0),_[P+1]/=R.roughness>e._Epsilon?R.roughness:1,_[P+2]/=R.metallic>e._Epsilon?R.metallic:1,L=m.Color3.FromInts(255,_[P+1],_[P+2]),e._FuzzyEquals(L,m.Color3.White(),e._Epsilon)||(S=!0);return S&&o.push(this._getImageDataAsync(_,f,p,a).then((function(e){R.metallicRoughnessTextureData=e}))),V&&o.push(this._getImageDataAsync(x,f,p,a).then((function(e){R.baseColorTextureData=e}))),[2,Promise.all(o).then((function(){return R}))];case 3:return[2,Promise.reject("_ConvertSpecularGlossinessTexturesToMetallicRoughness: Scene from textures is missing!")]}}))}))},e.prototype._convertSpecularGlossinessToMetallicRoughness=function(t){var r=this._getPerceivedBrightness(t.diffuseColor),n=this._getPerceivedBrightness(t.specularColor),a=1-this._getMaxComponent(t.specularColor),o=e._SolveMetallic(r,n,a),i=t.diffuseColor.scale(a/(1-e._DielectricSpecular.r)/Math.max(1-o,e._Epsilon)),s=t.specularColor.subtract(e._DielectricSpecular.scale(1-o)).scale(1/Math.max(o,e._Epsilon)),u=m.Color3.Lerp(i,s,o*o);return{baseColor:u=u.clampToRef(0,1,u),metallic:o,roughness:1-t.glossiness}},e.prototype._getPerceivedBrightness=function(e){return e?Math.sqrt(.299*e.r*e.r+.587*e.g*e.g+.114*e.b*e.b):0},e.prototype._getMaxComponent=function(e){return e?Math.max(e.r,Math.max(e.g,e.b)):0},e.prototype._convertMetalRoughFactorsToMetallicRoughnessAsync=function(e,t,r,n){var a=[],o={baseColor:e._albedoColor,metallic:e._metallic,roughness:e._roughness};if(n){e._albedoTexture&&a.push(this._exportTextureAsync(e._albedoTexture,t).then((function(e){e&&(r.baseColorTexture=e)})));var i=e._metallicTexture;i&&a.push(this._exportTextureAsync(i,t).then((function(e){e&&(r.metallicRoughnessTexture=e)})))}return Promise.all(a).then((function(){return o}))},e.prototype._getTextureSampler=function(e){var t={};if(!(e&&e instanceof m.Texture))return t;var r=this._getGLTFTextureWrapMode(e.wrapU);10497!==r&&(t.wrapS=r);var n=this._getGLTFTextureWrapMode(e.wrapV);switch(10497!==n&&(t.wrapT=n),e.samplingMode){case m.Texture.LINEAR_LINEAR:t.magFilter=9729,t.minFilter=9729;break;case m.Texture.LINEAR_NEAREST:t.magFilter=9729,t.minFilter=9728;break;case m.Texture.NEAREST_LINEAR:t.magFilter=9728,t.minFilter=9729;break;case m.Texture.NEAREST_LINEAR_MIPLINEAR:t.magFilter=9728,t.minFilter=9987;break;case m.Texture.NEAREST_NEAREST:t.magFilter=9728,t.minFilter=9728;break;case m.Texture.NEAREST_LINEAR_MIPNEAREST:t.magFilter=9728,t.minFilter=9985;break;case m.Texture.LINEAR_NEAREST_MIPNEAREST:t.magFilter=9729,t.minFilter=9984;break;case m.Texture.LINEAR_NEAREST_MIPLINEAR:t.magFilter=9729,t.minFilter=9986;break;case m.Texture.NEAREST_NEAREST_MIPLINEAR:t.magFilter=9728,t.minFilter=9986;break;case m.Texture.LINEAR_LINEAR_MIPLINEAR:t.magFilter=9729,t.minFilter=9987;break;case m.Texture.LINEAR_LINEAR_MIPNEAREST:t.magFilter=9729,t.minFilter=9985;break;case m.Texture.NEAREST_NEAREST_MIPNEAREST:t.magFilter=9728,t.minFilter=9984}return t},e.prototype._getGLTFTextureWrapMode=function(e){switch(e){case m.Texture.WRAP_ADDRESSMODE:return 10497;case m.Texture.CLAMP_ADDRESSMODE:return 33071;case m.Texture.MIRROR_ADDRESSMODE:return 33648;default:return m.Tools.Error("Unsupported Texture Wrap Mode ".concat(e,"!")),10497}},e.prototype._convertSpecGlossFactorsToMetallicRoughnessAsync=function(e,t,r,n){var a=this;return Promise.resolve().then((function(){var o={diffuseColor:e._albedoColor,specularColor:e._reflectivityColor,glossiness:e._microSurface},i=e._albedoTexture,s=e._reflectivityTexture,u=e._useMicroSurfaceFromReflectivityMapAlpha;if(s&&!u)return Promise.reject("_ConvertPBRMaterial: Glossiness values not included in the reflectivity texture are currently not supported");if((i||s)&&n){var l=a._exportTextureSampler(i||s);return a._convertSpecularGlossinessTexturesToMetallicRoughnessAsync(i,s,o,t).then((function(e){var n=a._exporter._textures;if(e.baseColorTextureData){var o=a._exportImage("baseColor".concat(n.length),t,e.baseColorTextureData);r.baseColorTexture=a._exportTextureInfo(o,l,null==i?void 0:i.coordinatesIndex)}return e.metallicRoughnessTextureData&&(o=a._exportImage("metallicRoughness".concat(n.length),t,e.metallicRoughnessTextureData),r.metallicRoughnessTexture=a._exportTextureInfo(o,l,null==s?void 0:s.coordinatesIndex)),e}))}return a._convertSpecularGlossinessToMetallicRoughness(o)}))},e.prototype._convertPBRMaterialAsync=function(e,t,r){var n=this,a={},o={name:e.name};if(e.isMetallicWorkflow()){var i=e._albedoColor,s=e.alpha;return i&&(a.baseColorFactor=[i.r,i.g,i.b,s]),this._convertMetalRoughFactorsToMetallicRoughnessAsync(e,t,a,r).then((function(i){return n._setMetallicRoughnessPbrMaterial(i,e,o,a,t,r)}))}return this._convertSpecGlossFactorsToMetallicRoughnessAsync(e,t,a,r).then((function(i){return n._setMetallicRoughnessPbrMaterial(i,e,o,a,t,r)}))},e.prototype._setMetallicRoughnessPbrMaterial=function(t,r,n,a,o,i){var s=this._exporter._materialMap,u=this._exporter._materials,l=[];if(t){if(e._SetAlphaMode(n,r),e._FuzzyEquals(t.baseColor,m.Color3.White(),e._Epsilon)&&r.alpha>=e._Epsilon||(a.baseColorFactor=[t.baseColor.r,t.baseColor.g,t.baseColor.b,r.alpha]),null!=t.metallic&&1!==t.metallic&&(a.metallicFactor=t.metallic),null!=t.roughness&&1!==t.roughness&&(a.roughnessFactor=t.roughness),null==r.backFaceCulling||r.backFaceCulling||(r._twoSidedLighting||m.Tools.Warn(r.name+": Back-face culling disabled and two-sided lighting disabled is not supported in glTF."),n.doubleSided=!0),i){var c=r._bumpTexture;if(c){var f=this._exportTextureAsync(c,o).then((function(e){e&&(n.normalTexture=e,1!==c.level&&(n.normalTexture.scale=c.level))}));l.push(f)}var p=r._ambientTexture;p&&(f=this._exportTextureAsync(p,o).then((function(e){if(e){var t={index:e.index,texCoord:e.texCoord,extensions:e.extensions};n.occlusionTexture=t;var a=r._ambientTextureStrength;a&&(t.strength=a)}})),l.push(f));var h=r._emissiveTexture;h&&(f=this._exportTextureAsync(h,o).then((function(e){e&&(n.emissiveTexture=e)})),l.push(f))}var d=r._emissiveColor;e._FuzzyEquals(d,m.Color3.Black(),e._Epsilon)||(n.emissiveFactor=d.asArray()),n.pbrMetallicRoughness=a,u.push(n),s[r.uniqueId]=u.length-1}return this._finishMaterial(l,n,r,o)},e.prototype._getPixelsFromTexture=function(e){return e.textureType,m.Constants.TEXTURETYPE_UNSIGNED_INT,e.readPixels()},e.prototype._exportTextureAsync=function(e,t){var r=this,n=this._exporter._extensionsPreExportTextureAsync("exporter",e,t);return n?n.then((function(n){return n?r._exportTextureInfoAsync(n,t):r._exportTextureInfoAsync(e,t)})):this._exportTextureInfoAsync(e,t)},e.prototype._exportTextureInfoAsync=function(e,t){return b(this,void 0,void 0,(function(){var r,n,a,o,i,s,u,l,c,f,p=this;return A(this,(function(h){switch(h.label){case 0:return(r=e.uid)in this._textureMap?[3,3]:[4,this._getPixelsFromTexture(e)];case 1:if(!(n=h.sent()))return[2,null];if(a=this._exportTextureSampler(e),o=e.mimeType)switch(o){case"image/jpeg":case"image/png":case"image/webp":t=o;break;default:m.Tools.Warn("Unsupported media type: ".concat(o))}return i=this._internalTextureToImage,s=e.getInternalTexture().uniqueId,i[s]||(i[s]={}),void 0===(u=i[s][t])&&(l=e.getSize(),u=b(p,void 0,void 0,(function(){var r;return A(this,(function(a){switch(a.label){case 0:return[4,this._getImageDataAsync(n,l.width,l.height,t)];case 1:return r=a.sent(),[2,this._exportImage(e.name,t,r)]}}))})),i[s][t]=u),f=this._exportTextureInfo,[4,u];case 2:c=f.apply(this,[h.sent(),a,e.coordinatesIndex]),this._textureMap[r]=c,this._exporter._extensionsPostExportTextures("exporter",this._textureMap[r],e),h.label=3;case 3:return[2,this._textureMap[r]]}}))}))},e.prototype._exportImage=function(e,t,r){var n=this._exporter._imageData,a=e.replace(/\.\/|\/|\.\\|\\/g,"_"),o=function(e){switch(e){case"image/jpeg":return".jpg";case"image/png":return".png";case"image/webp":return".webp";case"image/avif":return".avif"}}(t),i=a+o;i in n&&(i="".concat(a,"_").concat(m.Tools.RandomId()).concat(o)),n[i]={data:r,mimeType:t};var s=this._exporter._images;return s.push({name:e,uri:i}),s.length-1},e.prototype._exportTextureInfo=function(e,t,r){var n=this._exporter._textures,a=n.findIndex((function(r){return r.sampler==t&&r.source===e}));-1===a&&(a=n.length,n.push({source:e,sampler:t}));var o={index:a};return r&&(o.texCoord=r),o},e.prototype._exportTextureSampler=function(e){var t=this._getTextureSampler(e),r=this._exporter._samplers,n=r.findIndex((function(e){return e.minFilter===t.minFilter&&e.magFilter===t.magFilter&&e.wrapS===t.wrapS&&e.wrapT===t.wrapT}));return-1!==n?n:(r.push(t),r.length-1)},e._DielectricSpecular=new m.Color3(.04,.04,.04),e._MaxSpecularPower=1024,e._Epsilon=1e-6,e}(),w=m.Matrix.Compose(new m.Vector3(-1,1,1),m.Quaternion.Identity(),m.Vector3.Zero()),F=new m.Quaternion(0,1,0,0);function C(e,t){if(!(e instanceof m.TransformNode))return!1;if(t){if(!e.getWorldMatrix().isIdentity())return!1}else if(!e.getWorldMatrix().multiplyToRef(w,m.TmpVectors.Matrix[0]).isIdentity())return!1;return!(e instanceof m.Mesh&&e.geometry||e instanceof m.InstancedMesh&&e.sourceMesh.geometry)}function R(e,t){switch(t){case 5121:return e.setUInt8.bind(e);case 5123:return e.setUInt16.bind(e);case 5125:return e.setUInt32.bind(e);case 5126:return e.setFloat32.bind(e);default:return m.Tools.Warn("Unsupported Attribute Component kind: "+t),null}}var S=function(){function e(e,t){this._extensions={},this._glTF={asset:{generator:"Babylon.js v".concat(m.Engine.Version),version:"2.0"}},(e=e||m.EngineStore.LastCreatedScene)&&(this._babylonScene=e,this._bufferViews=[],this._accessors=[],this._meshes=[],this._scenes=[],this._cameras=[],this._nodes=[],this._images=[],this._materials=[],this._materialMap=[],this._textures=[],this._samplers=[],this._skins=[],this._animations=[],this._imageData={},this._orderedImageData=[],this._options=t||{},this._animationSampleRate=this._options.animationSampleRate||1/60,this._glTFMaterialExporter=new E(this),this._loadExtensions())}return e.prototype._applyExtension=function(e,t,r,n){var a=this;if(r>=t.length)return Promise.resolve(e);var o=n(t[r],e);return o?o.then((function(e){return a._applyExtension(e,t,r+1,n)})):this._applyExtension(e,t,r+1,n)},e.prototype._applyExtensions=function(t,r){for(var n=[],a=0,o=e._ExtensionNames;a<o.length;a++){var i=o[a];n.push(this._extensions[i])}return this._applyExtension(t,n,0,r)},e.prototype._extensionsPreExportTextureAsync=function(e,t,r){return this._applyExtensions(t,(function(t,n){return t.preExportTextureAsync&&t.preExportTextureAsync(e,n,r)}))},e.prototype._extensionsPostExportMeshPrimitiveAsync=function(e,t,r,n){return this._applyExtensions(t,(function(t,a){return t.postExportMeshPrimitiveAsync&&t.postExportMeshPrimitiveAsync(e,a,r,n)}))},e.prototype._extensionsPostExportNodeAsync=function(e,t,r,n,a){return this._applyExtensions(t,(function(t,o){return t.postExportNodeAsync&&t.postExportNodeAsync(e,o,r,n,a)}))},e.prototype._extensionsPostExportMaterialAsync=function(e,t,r){return this._applyExtensions(t,(function(t,n){return t.postExportMaterialAsync&&t.postExportMaterialAsync(e,n,r)}))},e.prototype._extensionsPostExportMaterialAdditionalTextures=function(t,r,n){for(var a=[],o=0,i=e._ExtensionNames;o<i.length;o++){var s=i[o],u=this._extensions[s];u.postExportMaterialAdditionalTextures&&a.push.apply(a,u.postExportMaterialAdditionalTextures(t,r,n))}return a},e.prototype._extensionsPostExportTextures=function(t,r,n){for(var a=0,o=e._ExtensionNames;a<o.length;a++){var i=o[a],s=this._extensions[i];s.postExportTexture&&s.postExportTexture(t,r,n)}},e.prototype._forEachExtensions=function(t){for(var r=0,n=e._ExtensionNames;r<n.length;r++){var a=n[r],o=this._extensions[a];o.enabled&&t(o)}},e.prototype._extensionsOnExporting=function(){var e=this;this._forEachExtensions((function(t){t.wasUsed&&(null==e._glTF.extensionsUsed&&(e._glTF.extensionsUsed=[]),-1===e._glTF.extensionsUsed.indexOf(t.name)&&e._glTF.extensionsUsed.push(t.name),t.required&&(null==e._glTF.extensionsRequired&&(e._glTF.extensionsRequired=[]),-1===e._glTF.extensionsRequired.indexOf(t.name)&&e._glTF.extensionsRequired.push(t.name)),null==e._glTF.extensions&&(e._glTF.extensions={}),t.onExporting&&t.onExporting())}))},e.prototype._loadExtensions=function(){for(var t=0,r=e._ExtensionNames;t<r.length;t++){var n=r[t],a=e._ExtensionFactories[n](this);this._extensions[n]=a}},e.prototype.dispose=function(){for(var e in this._extensions)this._extensions[e].dispose()},Object.defineProperty(e.prototype,"options",{get:function(){return this._options},enumerable:!1,configurable:!0}),e.RegisterExtension=function(t,r){e.UnregisterExtension(t)&&m.Tools.Warn("Extension with the name ".concat(t," already exists")),e._ExtensionFactories[t]=r,e._ExtensionNames.push(t)},e.UnregisterExtension=function(t){if(!e._ExtensionFactories[t])return!1;delete e._ExtensionFactories[t];var r=e._ExtensionNames.indexOf(t);return-1!==r&&e._ExtensionNames.splice(r,1),!0},e.prototype._reorderIndicesBasedOnPrimitiveMode=function(e,t,r,n,a){switch(t){case m.Material.TriangleFillMode:n||(n=0);for(var o=e.indexStart,i=e.indexStart+e.indexCount;o<i;o+=3){var s=n+4*o,u=a.getUInt32(s+4),l=a.getUInt32(s+8);a.setUInt32(l,s+4),a.setUInt32(u,s+8)}break;case m.Material.TriangleFanDrawMode:o=e.indexStart+e.indexCount-1;for(var c=e.indexStart;o>=c;--o)a.setUInt32(r[o],n),n+=4;break;case m.Material.TriangleStripDrawMode:e.indexCount>=3&&(a.setUInt32(r[e.indexStart+2],n+4),a.setUInt32(r[e.indexStart+1],n+8))}},e.prototype._reorderVertexAttributeDataBasedOnPrimitiveMode=function(e,t,r,n,a,o){switch(t){case m.Material.TriangleFillMode:this._reorderTriangleFillMode(e,r,n,a,o);break;case m.Material.TriangleStripDrawMode:this._reorderTriangleStripDrawMode(e,r,n,a,o);break;case m.Material.TriangleFanDrawMode:this._reorderTriangleFanMode(e,r,n,a,o)}},e.prototype._reorderTriangleFillMode=function(e,t,r,n,a){var o=this._getVertexBufferFromMesh(t,e.getMesh());if(o){var i=o.byteStride/m.VertexBuffer.GetTypeByteLength(o.type);if(e.verticesCount%3!=0)m.Tools.Error("The submesh vertices for the triangle fill mode is not divisible by 3!");else{var s=[],u=0;switch(t){case m.VertexBuffer.PositionKind:case m.VertexBuffer.NormalKind:for(var l=e.verticesStart;l<e.verticesStart+e.verticesCount;l+=3)u=l*i,s.push(m.Vector3.FromArray(r,u)),s.push(m.Vector3.FromArray(r,u+2*i)),s.push(m.Vector3.FromArray(r,u+i));break;case m.VertexBuffer.TangentKind:for(l=e.verticesStart;l<e.verticesStart+e.verticesCount;l+=3)u=l*i,s.push(m.Vector4.FromArray(r,u)),s.push(m.Vector4.FromArray(r,u+2*i)),s.push(m.Vector4.FromArray(r,u+i));break;case m.VertexBuffer.ColorKind:var c=o.getSize();for(l=e.verticesStart;l<e.verticesStart+e.verticesCount;l+=c)u=l*i,4===c?(s.push(m.Vector4.FromArray(r,u)),s.push(m.Vector4.FromArray(r,u+2*i)),s.push(m.Vector4.FromArray(r,u+i))):(s.push(m.Vector3.FromArray(r,u)),s.push(m.Vector3.FromArray(r,u+2*i)),s.push(m.Vector3.FromArray(r,u+i)));break;case m.VertexBuffer.UVKind:case m.VertexBuffer.UV2Kind:for(l=e.verticesStart;l<e.verticesStart+e.verticesCount;l+=3)u=l*i,s.push(m.Vector2.FromArray(r,u)),s.push(m.Vector2.FromArray(r,u+2*i)),s.push(m.Vector2.FromArray(r,u+i));break;default:m.Tools.Error("Unsupported Vertex Buffer type: ".concat(t))}this._writeVertexAttributeData(s,n,t,a)}}else m.Tools.Warn("reorderTriangleFillMode: Vertex Buffer Kind ".concat(t," not present!"))},e.prototype._reorderTriangleStripDrawMode=function(e,t,r,n,a){var o=this._getVertexBufferFromMesh(t,e.getMesh());if(o){var i=o.byteStride/m.VertexBuffer.GetTypeByteLength(o.type),s=[],u=0;switch(t){case m.VertexBuffer.PositionKind:case m.VertexBuffer.NormalKind:u=e.verticesStart,s.push(m.Vector3.FromArray(r,u+2*i)),s.push(m.Vector3.FromArray(r,u+i));break;case m.VertexBuffer.TangentKind:for(var l=e.verticesStart+e.verticesCount-1;l>=e.verticesStart;--l)u=l*i,s.push(m.Vector4.FromArray(r,u));break;case m.VertexBuffer.ColorKind:for(l=e.verticesStart+e.verticesCount-1;l>=e.verticesStart;--l)u=l*i,4===o.getSize()?s.push(m.Vector4.FromArray(r,u)):s.push(m.Vector3.FromArray(r,u));break;case m.VertexBuffer.UVKind:case m.VertexBuffer.UV2Kind:for(l=e.verticesStart+e.verticesCount-1;l>=e.verticesStart;--l)u=l*i,s.push(m.Vector2.FromArray(r,u));break;default:m.Tools.Error("Unsupported Vertex Buffer type: ".concat(t))}this._writeVertexAttributeData(s,n+12,t,a)}else m.Tools.Warn("reorderTriangleStripDrawMode: Vertex buffer kind ".concat(t," not present!"))},e.prototype._reorderTriangleFanMode=function(e,t,r,n,a){var o=this._getVertexBufferFromMesh(t,e.getMesh());if(o){var i=o.byteStride/m.VertexBuffer.GetTypeByteLength(o.type),s=[],u=0;switch(t){case m.VertexBuffer.PositionKind:case m.VertexBuffer.NormalKind:for(var l=e.verticesStart+e.verticesCount-1;l>=e.verticesStart;--l)u=l*i,s.push(m.Vector3.FromArray(r,u));break;case m.VertexBuffer.TangentKind:for(l=e.verticesStart+e.verticesCount-1;l>=e.verticesStart;--l)u=l*i,s.push(m.Vector4.FromArray(r,u));break;case m.VertexBuffer.ColorKind:for(l=e.verticesStart+e.verticesCount-1;l>=e.verticesStart;--l)u=l*i,s.push(m.Vector4.FromArray(r,u)),4===o.getSize()?s.push(m.Vector4.FromArray(r,u)):s.push(m.Vector3.FromArray(r,u));break;case m.VertexBuffer.UVKind:case m.VertexBuffer.UV2Kind:for(l=e.verticesStart+e.verticesCount-1;l>=e.verticesStart;--l)u=l*i,s.push(m.Vector2.FromArray(r,u));break;default:m.Tools.Error("Unsupported Vertex Buffer type: ".concat(t))}this._writeVertexAttributeData(s,n,t,a)}else m.Tools.Warn("reorderTriangleFanMode: Vertex buffer kind ".concat(t," not present!"))},e.prototype._writeVertexAttributeData=function(e,t,r,n){for(var a=0,o=e;a<o.length;a++){var i=o[a];r===m.VertexBuffer.NormalKind?i.normalize():r===m.VertexBuffer.TangentKind&&i instanceof m.Vector4&&x._NormalizeTangentFromRef(i);for(var s=0,u=i.asArray();s<u.length;s++){var l=u[s];n.setFloat32(l,t),t+=4}}},e.prototype._writeAttributeData=function(e,t,r,n,a,o){var i,s=[];switch(e){case m.VertexBuffer.PositionKind:for(var u=0,l=r.length/n;u<l;++u){i=u*n;var c=m.Vector3.FromArray(r,i);s.push(c.asArray())}break;case m.VertexBuffer.NormalKind:u=0;for(var f=r.length/n;u<f;++u)i=u*n,c=m.Vector3.FromArray(r,i),s.push(c.normalize().asArray());break;case m.VertexBuffer.TangentKind:u=0;for(var p=r.length/n;u<p;++u)i=u*n,c=m.Vector4.FromArray(r,i),x._NormalizeTangentFromRef(c),s.push(c.asArray());break;case m.VertexBuffer.ColorKind:for(var h=o.material,d=!h||"StandardMaterial"===h.getClassName(),g=(c=3===n?new m.Color3:new m.Color4,this._babylonScene.getEngine().useExactSrgbConversions),_=(u=0,r.length/n);u<_;++u)i=u*n,3===n?(m.Color3.FromArrayToRef(r,i,c),d&&c.toLinearSpaceToRef(c,g)):(m.Color4.FromArrayToRef(r,i,c),d&&c.toLinearSpaceToRef(c,g)),s.push(c.asArray());break;case m.VertexBuffer.UVKind:case m.VertexBuffer.UV2Kind:u=0;for(var y=r.length/n;u<y;++u)i=u*n,c=m.Vector2.FromArray(r,i),s.push(c.asArray());break;case m.VertexBuffer.MatricesIndicesKind:case m.VertexBuffer.MatricesIndicesExtraKind:u=0;for(var T=r.length/n;u<T;++u)i=u*n,c=m.Vector4.FromArray(r,i),s.push(c.asArray());break;case m.VertexBuffer.MatricesWeightsKind:case m.VertexBuffer.MatricesWeightsExtraKind:u=0;for(var v=r.length/n;u<v;++u)i=u*n,c=m.Vector4.FromArray(r,i),s.push(c.asArray());break;default:m.Tools.Warn("Unsupported Vertex Buffer Type: "+e),s=[]}var b=R(a,t);if(b)for(var A=0,M=s;A<M.length;A++)for(var E=0,w=M[A];E<w.length;E++)b(w[E])},e.prototype._createMorphTargetBufferViewKind=function(e,t,r,n,a,o,i){var s,u,l=[],c=m.TmpVectors.Vector3[0];switch(e){case m.VertexBuffer.PositionKind:var f=a.getPositions();if(!f)return null;var p=n.getVerticesData(m.VertexBuffer.PositionKind,void 0,void 0,!0),h=0,d=new m.Vector3(1/0,1/0,1/0),g=new m.Vector3(-1/0,-1/0,-1/0);s=p.length/3;for(var _=h;_<s;++_){var y=m.Vector3.FromArray(p,3*_);m.Vector3.FromArray(f,3*_).subtractToRef(y,c),d.copyFromFloats(Math.min(c.x,d.x),Math.min(c.y,d.y),Math.min(c.z,d.z)),g.copyFromFloats(Math.max(c.x,g.x),Math.max(c.y,g.y),Math.max(c.z,g.z)),l.push(c.x,c.y,c.z)}u={min:d,max:g};break;case m.VertexBuffer.NormalKind:var T=a.getNormals();if(!T)return null;var v=n.getVerticesData(m.VertexBuffer.NormalKind,void 0,void 0,!0);for(h=0,s=v.length/3,_=h;_<s;++_){var b=m.Vector3.FromArray(v,3*_).normalize();m.Vector3.FromArray(T,3*_).normalize().subtractToRef(b,c),l.push(c.x,c.y,c.z)}break;case m.VertexBuffer.TangentKind:var A=a.getTangents();if(!A)return null;t="VEC3",i=12;var M=n.getVerticesData(m.VertexBuffer.TangentKind,void 0,void 0,!0);for(h=0,s=M.length/4,_=h;_<s;++_){var E=m.Vector3.FromArray(M,4*_);x._NormalizeTangentFromRef(E);var w=m.Vector3.FromArray(A,3*_);x._NormalizeTangentFromRef(w),w.subtractToRef(E,c),l.push(c.x,c.y,c.z)}break;default:return null}var F=R(o,r);if(!F)return null;var C=m.VertexBuffer.GetTypeByteLength(r),S=l.length*C,V=x._CreateBufferView(0,o.getByteOffset(),S,i,"".concat(e," - ").concat(a.name," (Morph Target)"));this._bufferViews.push(V);for(var I=this._bufferViews.length-1,B=0,P=l;B<P.length;B++)F(P[B]);return{bufferViewIndex:I,vertexCount:s,accessorType:t,minMax:u}},e.prototype._generateJSON=function(e,t,r){var n,a,o,i=this,s={byteLength:this._totalByteLength},u=this._totalByteLength;return s.byteLength&&(this._glTF.buffers=[s]),this._nodes&&this._nodes.length&&(this._glTF.nodes=this._nodes),this._meshes&&this._meshes.length&&(this._glTF.meshes=this._meshes),this._scenes&&this._scenes.length&&(this._glTF.scenes=this._scenes,this._glTF.scene=0),this._cameras&&this._cameras.length&&(this._glTF.cameras=this._cameras),this._bufferViews&&this._bufferViews.length&&(this._glTF.bufferViews=this._bufferViews),this._accessors&&this._accessors.length&&(this._glTF.accessors=this._accessors),this._animations&&this._animations.length&&(this._glTF.animations=this._animations),this._materials&&this._materials.length&&(this._glTF.materials=this._materials),this._textures&&this._textures.length&&(this._glTF.textures=this._textures),this._samplers&&this._samplers.length&&(this._glTF.samplers=this._samplers),this._skins&&this._skins.length&&(this._glTF.skins=this._skins),this._images&&this._images.length&&(e?(this._glTF.images=[],this._images.forEach((function(e){e.uri&&(a=i._imageData[e.uri],i._orderedImageData.push(a),n=e.uri.split(".")[0]+" image",o=x._CreateBufferView(0,u,a.data.byteLength,void 0,n),u+=a.data.byteLength,i._bufferViews.push(o),e.bufferView=i._bufferViews.length-1,e.name=n,e.mimeType=a.mimeType,e.uri=void 0,i._glTF.images||(i._glTF.images=[]),i._glTF.images.push(e))})),s.byteLength=u):this._glTF.images=this._images),e||(s.uri=t+".bin"),r?JSON.stringify(this._glTF,null,2):JSON.stringify(this._glTF)},e.prototype._generateGLTFAsync=function(e,t){var r=this;return void 0===t&&(t=!0),this._generateBinaryAsync().then((function(n){r._extensionsOnExporting();var a=r._generateJSON(!1,e,!0),o=new Blob([n],{type:"application/octet-stream"}),i=e+".gltf",s=e+".bin",u=new T;if(u.glTFFiles[i]=a,u.glTFFiles[s]=o,r._imageData)for(var l in r._imageData)u.glTFFiles[l]=new Blob([r._imageData[l].data],{type:r._imageData[l].mimeType});return t&&r.dispose(),u}))},e.prototype._generateBinaryAsync=function(){var e=this,t=new V(4);return this._createSceneAsync(t).then((function(){return e._localEngine&&e._localEngine.dispose(),t.getArrayBuffer()}))},e.prototype._getPadding=function(e){var t=e%4;return 0===t?t:4-t},e.prototype._generateGLBAsync=function(e,t){var r=this;return void 0===t&&(t=!0),this._generateBinaryAsync().then((function(n){r._extensionsOnExporting();var a,o=r._generateJSON(!0),i=e+".glb",s=o.length,u=0;"undefined"!=typeof TextEncoder&&(s=(a=(new TextEncoder).encode(o)).length);for(var l=0;l<r._orderedImageData.length;++l)u+=r._orderedImageData[l].data.byteLength;var c=r._getPadding(s),f=r._getPadding(n.byteLength),p=r._getPadding(u),h=28+s+c+n.byteLength+f+u+p,d=new ArrayBuffer(12),m=new DataView(d);m.setUint32(0,1179937895,!0),m.setUint32(4,2,!0),m.setUint32(8,h,!0);var g=new ArrayBuffer(8+s+c),_=new DataView(g);_.setUint32(0,s+c,!0),_.setUint32(4,1313821514,!0);var x=new Uint8Array(g,8);if(a)x.set(a);else{var y="_".charCodeAt(0);for(l=0;l<s;++l){var v=o.charCodeAt(l);v!=o.codePointAt(l)?x[l]=y:x[l]=v}}var b=new Uint8Array(g,8+s);for(l=0;l<c;++l)b[l]=32;var A=new ArrayBuffer(8),M=new DataView(A);M.setUint32(0,n.byteLength+u+p,!0),M.setUint32(4,5130562,!0);var E=new ArrayBuffer(f),w=new Uint8Array(E);for(l=0;l<f;++l)w[l]=0;var F=new ArrayBuffer(p),C=new Uint8Array(F);for(l=0;l<p;++l)C[l]=0;var R=[d,g,A,n];for(l=0;l<r._orderedImageData.length;++l)R.push(r._orderedImageData[l].data);R.push(E),R.push(F);var S=new Blob(R,{type:"application/octet-stream"}),V=new T;return V.glTFFiles[i]=S,null!=r._localEngine&&r._localEngine.dispose(),t&&r.dispose(),V}))},e.prototype._setNodeTransformation=function(e,t){t.getPivotPoint().equalsToFloats(0,0,0)||m.Tools.Warn("Pivot points are not supported in the glTF serializer"),t.position.equalsToFloats(0,0,0)||(e.translation=t.position.asArray()),t.scaling.equalsToFloats(1,1,1)||(e.scale=t.scaling.asArray());var r=m.Quaternion.FromEulerAngles(t.rotation.x,t.rotation.y,t.rotation.z);t.rotationQuaternion&&r.multiplyInPlace(t.rotationQuaternion),m.Quaternion.IsIdentity(r)||(e.rotation=r.normalize().asArray())},e.prototype._setCameraTransformation=function(e,t){var r=m.TmpVectors.Vector3[0],n=m.TmpVectors.Quaternion[0];t.getWorldMatrix().decompose(void 0,n,r),r.equalsToFloats(0,0,0)||(e.translation=r.asArray()),n.multiplyInPlace(F),m.Quaternion.IsIdentity(n)||(e.rotation=n.asArray())},e.prototype._getVertexBufferFromMesh=function(e,t){if(t.isVerticesDataPresent(e,!0)){var r=t.getVertexBuffer(e,!0);if(r)return r}return null},e.prototype._createBufferViewKind=function(e,t,r,n,a){var o=r instanceof m.Mesh?r:r instanceof m.InstancedMesh?r.sourceMesh:null;if(o){var i=o.getVertexBuffer(e,!0),s=o.getVerticesData(e,void 0,void 0,!0);if(i&&s){var u=m.VertexBuffer.GetTypeByteLength(t),l=s.length*u,c=x._CreateBufferView(0,n.getByteOffset(),l,a,e+" - "+o.name);this._bufferViews.push(c),this._writeAttributeData(e,t,s,a/u,n,r)}}},e.prototype._getMeshPrimitiveMode=function(e){if(e instanceof m.LinesMesh)return m.Material.LineListDrawMode;if(e instanceof m.InstancedMesh||e instanceof m.Mesh){var t=e instanceof m.Mesh?e:e.sourceMesh;if("number"==typeof t.overrideRenderingFillMode)return t.overrideRenderingFillMode}return e.material?e.material.fillMode:m.Material.TriangleFillMode},e.prototype._setPrimitiveMode=function(e,t){switch(t){case m.Material.TriangleFillMode:break;case m.Material.TriangleStripDrawMode:e.mode=5;break;case m.Material.TriangleFanDrawMode:e.mode=6;break;case m.Material.PointListDrawMode:case m.Material.PointFillMode:e.mode=0;break;case m.Material.LineLoopDrawMode:e.mode=2;break;case m.Material.LineListDrawMode:e.mode=1;break;case m.Material.LineStripDrawMode:e.mode=3}},e.prototype._setAttributeKind=function(e,t){switch(t){case m.VertexBuffer.PositionKind:e.POSITION=this._accessors.length-1;break;case m.VertexBuffer.NormalKind:e.NORMAL=this._accessors.length-1;break;case m.VertexBuffer.ColorKind:e.COLOR_0=this._accessors.length-1;break;case m.VertexBuffer.TangentKind:e.TANGENT=this._accessors.length-1;break;case m.VertexBuffer.UVKind:e.TEXCOORD_0=this._accessors.length-1;break;case m.VertexBuffer.UV2Kind:e.TEXCOORD_1=this._accessors.length-1;break;case m.VertexBuffer.MatricesIndicesKind:e.JOINTS_0=this._accessors.length-1;break;case m.VertexBuffer.MatricesIndicesExtraKind:e.JOINTS_1=this._accessors.length-1;break;case m.VertexBuffer.MatricesWeightsKind:e.WEIGHTS_0=this._accessors.length-1;break;case m.VertexBuffer.MatricesWeightsExtraKind:e.WEIGHTS_1=this._accessors.length-1;break;default:m.Tools.Warn("Unsupported Vertex Buffer Type: "+t)}},e.prototype._setPrimitiveAttributesAsync=function(e,t,r){var n,a,o,i,s,u,l,c,f,p=[],h=null;t instanceof m.Mesh?h=t:t instanceof m.InstancedMesh&&(h=t.sourceMesh);var d=[{kind:m.VertexBuffer.PositionKind,accessorType:"VEC3",accessorComponentType:5126,byteStride:12},{kind:m.VertexBuffer.NormalKind,accessorType:"VEC3",accessorComponentType:5126,byteStride:12},{kind:m.VertexBuffer.ColorKind,accessorType:"VEC4",accessorComponentType:5126,byteStride:16},{kind:m.VertexBuffer.TangentKind,accessorType:"VEC4",accessorComponentType:5126,byteStride:16},{kind:m.VertexBuffer.UVKind,accessorType:"VEC2",accessorComponentType:5126,byteStride:8},{kind:m.VertexBuffer.UV2Kind,accessorType:"VEC2",accessorComponentType:5126,byteStride:8},{kind:m.VertexBuffer.MatricesIndicesKind,accessorType:"VEC4",accessorComponentType:5123,byteStride:8},{kind:m.VertexBuffer.MatricesIndicesExtraKind,accessorType:"VEC4",accessorComponentType:5123,byteStride:8},{kind:m.VertexBuffer.MatricesWeightsKind,accessorType:"VEC4",accessorComponentType:5126,byteStride:16},{kind:m.VertexBuffer.MatricesWeightsExtraKind,accessorType:"VEC4",accessorComponentType:5126,byteStride:16}];if(h){for(var g=null,_=this._getMeshPrimitiveMode(h),y={},T=h.morphTargetManager,v=0,b=d;v<b.length;v++){var A=(X=b[v]).kind,M=X.accessorComponentType;if(h.isVerticesDataPresent(A,!0)){var E=this._getVertexBufferFromMesh(A,h);if(X.byteStride=E?E.getSize()*m.VertexBuffer.GetTypeByteLength(X.accessorComponentType):4*m.VertexBuffer.DeduceStride(A),12===X.byteStride&&(X.accessorType="VEC3"),this._createBufferViewKind(A,M,t,r,X.byteStride),X.bufferViewIndex=this._bufferViews.length-1,y[A]=X.bufferViewIndex,T)for(var w=0;w<T.numTargets;++w){var F=T.getTarget(w);(ee=this._createMorphTargetBufferViewKind(A,X.accessorType,M,h,F,r,X.byteStride))&&(X.morphTargetInfo||(X.morphTargetInfo=[]),X.morphTargetInfo[w]=ee)}}}if(h.getTotalIndices()){var C=h.getIndices();if(C){var R=4*C.length;c=x._CreateBufferView(0,r.getByteOffset(),R,void 0,"Indices - "+h.name),this._bufferViews.push(c),g=this._bufferViews.length-1;for(var S=0,V=C.length;S<V;++S)r.setUInt32(C[S])}}if(h.subMeshes)for(var I=0,B=h.subMeshes;I<B.length;I++){var P=B[I],O=P.getMaterial()||h.getScene().defaultMaterial,N=null;if(O)if(h instanceof m.LinesMesh){var L={name:h.name+" material"};(!h.color.equals(m.Color3.White())||h.alpha<1)&&(L.pbrMetallicRoughness={baseColorFactor:h.color.asArray().concat([h.alpha])}),this._materials.push(L),N=this._materials.length-1}else if(O instanceof m.MultiMaterial){var U=O.subMaterials[P.materialIndex];U&&(O=U,N=this._materialMap[O.uniqueId])}else N=this._materialMap[O.uniqueId];var k=null!=N?this._materials[N]:null,K={attributes:{}};this._setPrimitiveMode(K,_);for(var D=0,G=d;D<G.length;D++)if(((A=(X=G[D]).kind)!==m.VertexBuffer.UVKind&&A!==m.VertexBuffer.UV2Kind||this._options.exportUnusedUVs||k&&this._glTFMaterialExporter._hasTexturesPresent(k))&&(Y=h.getVerticesData(A,void 0,void 0,!0))&&(E=this._getVertexBufferFromMesh(A,h))){var q=E.getSize(),W=X.bufferViewIndex;if(null!=W){f={min:null,max:null},A==m.VertexBuffer.PositionKind&&(f=x._CalculateMinMaxPositions(Y,0,Y.length/q));var z=x._CreateAccessor(W,A+" - "+t.name,X.accessorType,X.accessorComponentType,Y.length/q,0,f.min,f.max);this._accessors.push(z),this._setAttributeKind(K.attributes,A)}}if(g&&(z=x._CreateAccessor(g,"indices - "+t.name,"SCALAR",5125,P.indexCount,4*P.indexStart,null,null),this._accessors.push(z),K.indices=this._accessors.length-1),Object.keys(K.attributes).length>0){if(O._getEffectiveOrientation(h)===(this._babylonScene.useRightHandedSystem?m.Material.ClockWiseSideOrientation:m.Material.CounterClockWiseSideOrientation)){null==(te=null!=g?this._bufferViews[g].byteOffset:null)&&(te=0);var H=null;if(null!=g&&(H=h.getIndices()),H)this._reorderIndicesBasedOnPrimitiveMode(P,_,H,te,r);else for(var j=0,Q=d;j<Q.length;j++){var Y,X=Q[j];if(Y=h.getVerticesData(X.kind,void 0,void 0,!0)){var Z=this._bufferViews[y[X.kind]].byteOffset||0;this._reorderVertexAttributeDataBasedOnPrimitiveMode(P,_,X.kind,Y,Z,r)}}}null!=N&&(K.material=N)}if(T)for(e.extras||(e.extras={}),e.extras.targetNames=[],w=0;w<T.numTargets;++w){F=T.getTarget(w),e.extras.targetNames.push(F.name);for(var J=0,$=d;J<$.length;J++){var ee;if(ee=null===(n=(X=$[J]).morphTargetInfo)||void 0===n?void 0:n[w]){var te=0;z=x._CreateAccessor(ee.bufferViewIndex,"".concat(X.kind," - ").concat(F.name," (Morph Target)"),ee.accessorType,X.accessorComponentType,ee.vertexCount,te,null!==(i=null===(o=null===(a=ee.minMax)||void 0===a?void 0:a.min)||void 0===o?void 0:o.asArray())&&void 0!==i?i:null,null!==(l=null===(u=null===(s=ee.minMax)||void 0===s?void 0:s.max)||void 0===u?void 0:u.asArray())&&void 0!==l?l:null),this._accessors.push(z),K.targets||(K.targets=[]),K.targets[w]||(K.targets[w]={}),this._setAttributeKind(K.targets[w],X.kind)}}}e.primitives.push(K),this._extensionsPostExportMeshPrimitiveAsync("postExport",K,P,r),p.push()}}return Promise.all(p).then((function(){}))},e.prototype._createSceneAsync=function(e){var t,r,n,a,o=this,i={nodes:[]},s=M(M(M(M([],this._babylonScene.transformNodes,!0),this._babylonScene.meshes,!0),this._babylonScene.lights,!0),this._babylonScene.cameras,!0),u=new Set;if(this._babylonScene.metadata&&(this._options.metadataSelector?i.extras=this._options.metadataSelector(this._babylonScene.metadata):this._babylonScene.metadata.gltf&&(i.extras=this._babylonScene.metadata.gltf.extras)),(null===(t=this._options.removeNoopRootNodes)||void 0===t||t)&&!this._options.includeCoordinateSystemConversionNodes)for(var l=0,c=this._babylonScene.rootNodes;l<c.length;l++){var f=c[l];C(f,this._babylonScene.useRightHandedSystem)&&(u.add(f),s.splice(s.indexOf(f),1))}var p=new Map;this._babylonScene.cameras.forEach((function(e){if(!o._options.shouldExportNode||o._options.shouldExportNode(e)){var t={type:e.mode===m.Camera.PERSPECTIVE_CAMERA?"perspective":"orthographic"};if(e.name&&(t.name=e.name),"perspective"===t.type)t.perspective={aspectRatio:e.getEngine().getAspectRatio(e),yfov:e.fovMode===m.Camera.FOVMODE_VERTICAL_FIXED?e.fov:e.fov*e.getEngine().getAspectRatio(e),znear:e.minZ,zfar:e.maxZ};else if("orthographic"===t.type){var r=e.orthoLeft&&e.orthoRight?.5*(e.orthoRight-e.orthoLeft):.5*e.getEngine().getRenderWidth(),n=e.orthoBottom&&e.orthoTop?.5*(e.orthoTop-e.orthoBottom):.5*e.getEngine().getRenderHeight();t.orthographic={xmag:r,ymag:n,znear:e.minZ,zfar:e.maxZ}}p.set(e,o._cameras.length),o._cameras.push(t)}}));var h=this._getExportNodes(s),d=h[0],g=h[1];return this._glTFMaterialExporter._convertMaterialsToGLTFAsync(g,"image/png",!0).then((function(){return o._createNodeMapAndAnimationsAsync(d,e).then((function(t){return o._createSkinsAsync(t,e).then((function(l){if(o._nodeMap=t,o._totalByteLength=e.getByteOffset(),null==o._totalByteLength)throw new Error("undefined byte length!");for(var c=0,f=s;c<f.length;c++){var h=f[c];if(void 0!==(r=o._nodeMap[h.uniqueId])&&(n=o._nodes[r],h.metadata&&(o._options.metadataSelector?n.extras=o._options.metadataSelector(h.metadata):h.metadata.gltf&&(n.extras=h.metadata.gltf.extras)),h instanceof m.Camera&&(n.camera=p.get(h)),o._options.shouldExportNode&&!o._options.shouldExportNode(h)?m.Tools.Log("Omitting "+h.name+" from scene."):(h.parent||o._babylonScene.useRightHandedSystem||(y=n,T=void 0,v=void 0,b=void 0,T=m.Vector3.FromArrayToRef(y.translation||[0,0,0],0,m.TmpVectors.Vector3[0]),v=m.Quaternion.FromArrayToRef(y.rotation||[0,0,0,1],0,m.TmpVectors.Quaternion[0]),b=m.Vector3.FromArrayToRef(y.scale||[1,1,1],0,m.TmpVectors.Vector3[1]),m.Matrix.ComposeToRef(b,v,T,m.TmpVectors.Matrix[0]).multiplyToRef(w,m.TmpVectors.Matrix[0]).decompose(b,v,T),T.equalsToFloats(0,0,0)?delete y.translation:y.translation=T.asArray(),m.Quaternion.IsIdentity(v)?delete y.rotation:y.rotation=v.asArray(),b.equalsToFloats(1,1,1)?delete y.scale:y.scale=b.asArray()),h.parent&&!u.has(h.parent)||i.nodes.push(r)),h instanceof m.Mesh&&h.skeleton&&(n.skin=l[h.skeleton.uniqueId]),a=h.getDescendants(!0),!n.children&&a&&a.length)){for(var d=[],g=0,_=a;g<_.length;g++){var x=_[g];null!=o._nodeMap[x.uniqueId]&&d.push(o._nodeMap[x.uniqueId])}d.length&&(n.children=d)}}var y,T,v,b;i.nodes.length&&o._scenes.push(i)}))}))}))},e.prototype._getExportNodes=function(e){for(var t=[],r=new Set,n=0,a=e;n<a.length;n++){var o=a[n];if(!this._options.shouldExportNode||this._options.shouldExportNode(o)){t.push(o);var i=o;if(i.subMeshes&&i.subMeshes.length>0){var s=i.material||i.getScene().defaultMaterial;if(s instanceof m.MultiMaterial)for(var u=0,l=s.subMaterials;u<l.length;u++){var c=l[u];c&&r.add(c)}else r.add(s)}}else"Excluding node ".concat(o.name)}return[t,r]},e.prototype._createNodeMapAndAnimationsAsync=function(e,t){for(var r,n=this,a=Promise.resolve(),o={},i={name:"runtime animations",channels:[],samplers:[]},s=[],u=function(e){a=a.then((function(){return n._createNodeAsync(e,t).then((function(a){var u=n._extensionsPostExportNodeAsync("createNodeAsync",a,e,o,t);return null==u?(m.Tools.Warn("Not exporting node ".concat(e.name)),Promise.resolve()):u.then((function(a){a&&(n._nodes.push(a),r=n._nodes.length-1,o[e.uniqueId]=r,n._babylonScene.animationGroups.length||(y._CreateMorphTargetAnimationFromMorphTargetAnimations(e,i,s,o,n._nodes,t,n._bufferViews,n._accessors,n._animationSampleRate,n._options.shouldExportAnimation),e.animations.length&&y._CreateNodeAnimationFromNodeAnimations(e,i,s,o,n._nodes,t,n._bufferViews,n._accessors,n._animationSampleRate,n._options.shouldExportAnimation)))}))}))}))},l=0,c=e;l<c.length;l++)u(c[l]);return a.then((function(){return i.channels.length&&i.samplers.length&&n._animations.push(i),s.forEach((function(e){e.channels.length&&e.samplers.length&&n._animations.push(e)})),n._babylonScene.animationGroups.length&&y._CreateNodeAndMorphAnimationFromAnimationGroups(n._babylonScene,n._animations,o,t,n._bufferViews,n._accessors,n._animationSampleRate,n._options.shouldExportAnimation),o}))},e.prototype._createNodeAsync=function(e,t){var r=this;return Promise.resolve().then((function(){var n={},a={primitives:[]};if(e.name&&(n.name=e.name),e instanceof m.TransformNode){if(r._setNodeTransformation(n,e),e instanceof m.Mesh){var o=e.morphTargetManager;if(o&&o.numTargets>0){a.weights=[];for(var i=0;i<o.numTargets;++i)a.weights.push(o.getTarget(i).influence)}}return r._setPrimitiveAttributesAsync(a,e,t).then((function(){return a.primitives.length&&(r._meshes.push(a),n.mesh=r._meshes.length-1),n}))}return e instanceof m.Camera?(r._setCameraTransformation(n,e),n):n}))},e.prototype._createSkinsAsync=function(e,t){for(var r,n=Promise.resolve(),a={},o=0,i=this._babylonScene.skeletons;o<i.length;o++){var s=i[o];if(!(s.bones.length<=0)){for(var u={joints:[]},l=[],c={},f=-1,p=0;p<s.bones.length;++p)-1!==(h=null!==(r=(d=s.bones[p]).getIndex())&&void 0!==r?r:p)&&(c[h]=d,h>f&&(f=h));for(var h=0;h<=f;++h){var d=c[h];l.push(d.getInvertedAbsoluteTransform());var g=d.getTransformNode();g&&null!==e[g.uniqueId]&&void 0!==e[g.uniqueId]?u.joints.push(e[g.uniqueId]):m.Tools.Warn("Exporting a bone without a linked transform node is currently unsupported")}if(u.joints.length>0){var _=64*l.length,y=t.getByteOffset(),T=x._CreateBufferView(0,y,_,void 0,"InverseBindMatrices - "+s.name);this._bufferViews.push(T);var v=this._bufferViews.length-1,b=x._CreateAccessor(v,"InverseBindMatrices - "+s.name,"MAT4",5126,l.length,null,null,null),A=this._accessors.push(b)-1;u.inverseBindMatrices=A,this._skins.push(u),a[s.uniqueId]=this._skins.length-1,l.forEach((function(e){e.m.forEach((function(e){t.setFloat32(e)}))}))}}}return n.then((function(){return a}))},e._ExtensionNames=new Array,e._ExtensionFactories={},e}(),V=function(){function e(e){this._arrayBuffer=new ArrayBuffer(e),this._dataView=new DataView(this._arrayBuffer),this._byteOffset=0}return e.prototype._resizeBuffer=function(e){var t=new ArrayBuffer(e),r=Math.min(this._arrayBuffer.byteLength,e),n=new Uint8Array(this._arrayBuffer,0,r);return new Uint8Array(t).set(n,0),this._arrayBuffer=t,this._dataView=new DataView(this._arrayBuffer),t},e.prototype.getArrayBuffer=function(){return this._resizeBuffer(this.getByteOffset())},e.prototype.getByteOffset=function(){if(null==this._byteOffset)throw new Error("Byte offset is undefined!");return this._byteOffset},e.prototype.setUInt8=function(e,t){null!=t?t<this._byteOffset?this._dataView.setUint8(t,e):m.Tools.Error("BinaryWriter: byteoffset is greater than the current binary buffer length!"):(this._byteOffset+1>this._arrayBuffer.byteLength&&this._resizeBuffer(2*this._arrayBuffer.byteLength),this._dataView.setUint8(this._byteOffset,e),this._byteOffset+=1)},e.prototype.setUInt16=function(e,t){null!=t?t<this._byteOffset?this._dataView.setUint16(t,e,!0):m.Tools.Error("BinaryWriter: byteoffset is greater than the current binary buffer length!"):(this._byteOffset+2>this._arrayBuffer.byteLength&&this._resizeBuffer(2*this._arrayBuffer.byteLength),this._dataView.setUint16(this._byteOffset,e,!0),this._byteOffset+=2)},e.prototype.getUInt32=function(e){if(e<this._byteOffset)return this._dataView.getUint32(e,!0);throw m.Tools.Error("BinaryWriter: byteoffset is greater than the current binary buffer length!"),new Error("BinaryWriter: byteoffset is greater than the current binary buffer length!")},e.prototype.getVector3Float32FromRef=function(e,t){t+8>this._byteOffset?m.Tools.Error("BinaryWriter: byteoffset is greater than the current binary buffer length!"):(e.x=this._dataView.getFloat32(t,!0),e.y=this._dataView.getFloat32(t+4,!0),e.z=this._dataView.getFloat32(t+8,!0))},e.prototype.setVector3Float32FromRef=function(e,t){t+8>this._byteOffset?m.Tools.Error("BinaryWriter: byteoffset is greater than the current binary buffer length!"):(this._dataView.setFloat32(t,e.x,!0),this._dataView.setFloat32(t+4,e.y,!0),this._dataView.setFloat32(t+8,e.z,!0))},e.prototype.getVector4Float32FromRef=function(e,t){t+12>this._byteOffset?m.Tools.Error("BinaryWriter: byteoffset is greater than the current binary buffer length!"):(e.x=this._dataView.getFloat32(t,!0),e.y=this._dataView.getFloat32(t+4,!0),e.z=this._dataView.getFloat32(t+8,!0),e.w=this._dataView.getFloat32(t+12,!0))},e.prototype.setVector4Float32FromRef=function(e,t){t+12>this._byteOffset?m.Tools.Error("BinaryWriter: byteoffset is greater than the current binary buffer length!"):(this._dataView.setFloat32(t,e.x,!0),this._dataView.setFloat32(t+4,e.y,!0),this._dataView.setFloat32(t+8,e.z,!0),this._dataView.setFloat32(t+12,e.w,!0))},e.prototype.setFloat32=function(e,t){isNaN(e)&&m.Tools.Error("Invalid data being written!"),null!=t&&(t<this._byteOffset?this._dataView.setFloat32(t,e,!0):m.Tools.Error("BinaryWriter: byteoffset is greater than the current binary length!")),this._byteOffset+4>this._arrayBuffer.byteLength&&this._resizeBuffer(2*this._arrayBuffer.byteLength),this._dataView.setFloat32(this._byteOffset,e,!0),this._byteOffset+=4},e.prototype.setUInt32=function(e,t){null!=t?t<this._byteOffset?this._dataView.setUint32(t,e,!0):m.Tools.Error("BinaryWriter: byteoffset is greater than the current binary buffer length!"):(this._byteOffset+4>this._arrayBuffer.byteLength&&this._resizeBuffer(2*this._arrayBuffer.byteLength),this._dataView.setUint32(this._byteOffset,e,!0),this._byteOffset+=4)},e.prototype.setInt16=function(e,t){null!=t?t<this._byteOffset?this._dataView.setInt16(t,e,!0):m.Tools.Error("BinaryWriter: byteoffset is greater than the current binary buffer length!"):(this._byteOffset+2>this._arrayBuffer.byteLength&&this._resizeBuffer(2*this._arrayBuffer.byteLength),this._dataView.setInt16(this._byteOffset,e,!0),this._byteOffset+=2)},e.prototype.setByte=function(e,t){null!=t?t<this._byteOffset?this._dataView.setInt8(t,e):m.Tools.Error("BinaryWriter: byteoffset is greater than the current binary buffer length!"):(this._byteOffset+1>this._arrayBuffer.byteLength&&this._resizeBuffer(2*this._arrayBuffer.byteLength),this._dataView.setInt8(this._byteOffset,e),this._byteOffset++)},e}(),I=0,B=function(){function e(){}return e.GLTFAsync=function(e,t,r){return e.whenReadyAsync().then((function(){var n=t.replace(/\.[^/.]+$/,"");return new S(e,r)._generateGLTFAsync(n)}))},e._PreExportAsync=function(e,t){return Promise.resolve().then((function(){return t&&t.exportWithoutWaitingForScene?Promise.resolve():e.whenReadyAsync()}))},e._PostExportAsync=function(e,t,r){return Promise.resolve().then((function(){return r&&r.exportWithoutWaitingForScene,t}))},e.GLBAsync=function(e,t,r){var n=this;return this._PreExportAsync(e,r).then((function(){var a=t.replace(/\.[^/.]+$/,"");return new S(e,r)._generateGLBAsync(a).then((function(t){return n._PostExportAsync(e,t,r)}))}))},e}(),P="KHR_texture_transform",O=function(){function e(){this.name=P,this.enabled=!0,this.required=!1,this._wasUsed=!1}return e.prototype.dispose=function(){},Object.defineProperty(e.prototype,"wasUsed",{get:function(){return this._wasUsed},enumerable:!1,configurable:!0}),e.prototype.postExportTexture=function(e,t,r){if(r&&(0===r.uAng&&0===r.wAng&&0===r.vAng||0===r.uRotationCenter&&0===r.vRotationCenter)){var n={},a=!1;if(0===r.uOffset&&0===r.vOffset||(n.offset=[r.uOffset,r.vOffset],a=!0),1===r.uScale&&1===r.vScale||(n.scale=[r.uScale,r.vScale],a=!0),0!==r.wAng&&(n.rotation=-r.wAng,a=!0),0!==r.coordinatesIndex&&(n.texCoord=r.coordinatesIndex,a=!0),!a)return;this._wasUsed=!0,t.extensions||(t.extensions={}),t.extensions[P]=n}},e.prototype.preExportTextureAsync=function(e,t){return new Promise((function(r,n){t.getScene()?0!==t.uAng||0!==t.vAng?(m.Tools.Warn("".concat(e,": Texture ").concat(t.name," with rotation in the u or v axis is not supported in glTF.")),r(null)):0===t.wAng||0===t.uRotationCenter&&0===t.vRotationCenter?r(t):(m.Tools.Warn("".concat(e,": Texture ").concat(t.name," with rotation not centered at the origin cannot be exported with ").concat(P)),r(null)):n("".concat(e,': "scene" is not defined for Babylon texture ').concat(t.name,"!"))}))},e}();S.RegisterExtension(P,(function(){return new O}));var N="KHR_lights_punctual",L=function(){function e(e){this.name=N,this.enabled=!0,this.required=!1,this._exporter=e}return e.prototype.dispose=function(){this._lights=null},Object.defineProperty(e.prototype,"wasUsed",{get:function(){return!!this._lights},enumerable:!1,configurable:!0}),e.prototype.onExporting=function(){this._exporter._glTF.extensions[N]=this._lights},e.prototype.postExportNodeAsync=function(e,t,r,n){var a=this;return new Promise((function(o){if(t&&r instanceof m.ShadowLight){var i=void 0,s=r.getTypeID()==m.Light.LIGHTTYPEID_POINTLIGHT?"point":r.getTypeID()==m.Light.LIGHTTYPEID_DIRECTIONALLIGHT?"directional":r.getTypeID()==m.Light.LIGHTTYPEID_SPOTLIGHT?"spot":null;if(null==s)m.Logger.Warn("".concat(e,": Light ").concat(r.name," is not supported in ").concat(N));else{if(r.position.equalsToFloats(0,0,0)||(t.translation=r.position.asArray()),"point"!==s){var u=r.direction,l=-Math.atan2(u.z,u.x)+Math.PI/2,c=Math.sqrt(u.x*u.x+u.z*u.z),f=-Math.atan2(u.y,c),p=m.Quaternion.RotationYawPitchRoll(l+Math.PI,f,0);m.Quaternion.IsIdentity(p)||(t.rotation=p.asArray())}if(r.falloffType!==m.Light.FALLOFF_GLTF&&m.Logger.Warn("".concat(e,": Light falloff for ").concat(r.name," does not match the ").concat(N," specification!")),i={type:s},r.diffuse.equals(m.Color3.White())||(i.color=r.diffuse.asArray()),1!==r.intensity&&(i.intensity=r.intensity),r.range!==Number.MAX_VALUE&&(i.range=r.range),"spot"===s){var h=r;h.angle!==Math.PI/2&&(null==i.spot&&(i.spot={}),i.spot.outerConeAngle=h.angle/2),0!==h.innerAngle&&(null==i.spot&&(i.spot={}),i.spot.innerConeAngle=h.innerAngle/2)}a._lights||(a._lights={lights:[]}),a._lights.lights.push(i);var d={light:a._lights.lights.length-1},g=r.parent;if(g&&1==g.getChildren().length){var _=a._exporter._nodes[n[g.uniqueId]];if(_){var x=m.Vector3.FromArrayToRef(_.translation||[0,0,0],0,m.TmpVectors.Vector3[0]),y=m.Quaternion.FromArrayToRef(_.rotation||[0,0,0,1],0,m.TmpVectors.Quaternion[0]),T=m.Vector3.FromArrayToRef(_.scale||[1,1,1],0,m.TmpVectors.Vector3[1]),v=m.Matrix.ComposeToRef(T,y,x,m.TmpVectors.Matrix[0]),b=m.Vector3.FromArrayToRef(t.translation||[0,0,0],0,m.TmpVectors.Vector3[2]),A=m.Quaternion.FromArrayToRef(t.rotation||[0,0,0,1],0,m.TmpVectors.Quaternion[1]),M=m.Matrix.ComposeToRef(m.Vector3.OneReadOnly,A,b,m.TmpVectors.Matrix[1]);return v.multiplyToRef(M,M),M.decompose(T,y,x),x.equalsToFloats(0,0,0)?delete _.translation:_.translation=x.asArray(),m.Quaternion.IsIdentity(y)?delete _.rotation:_.rotation=y.asArray(),T.equalsToFloats(1,1,1)?delete _.scale:_.scale=T.asArray(),_.extensions||(_.extensions={}),_.extensions[N]=d,void o(null)}}t.extensions||(t.extensions={}),t.extensions[N]=d}}o(t)}))},e}();S.RegisterExtension(N,(function(e){return new L(e)}));var U="KHR_materials_clearcoat",k=function(){function e(e){this.name=U,this.enabled=!0,this.required=!1,this._wasUsed=!1,this._exporter=e}return e.prototype.dispose=function(){},Object.defineProperty(e.prototype,"wasUsed",{get:function(){return this._wasUsed},enumerable:!1,configurable:!0}),e.prototype.postExportMaterialAdditionalTextures=function(e,t,r){var n=[];return r instanceof m.PBRBaseMaterial&&r.clearCoat.isEnabled?(r.clearCoat.texture&&n.push(r.clearCoat.texture),!r.clearCoat.useRoughnessFromMainTexture&&r.clearCoat.textureRoughness&&n.push(r.clearCoat.textureRoughness),r.clearCoat.bumpTexture&&n.push(r.clearCoat.bumpTexture),n):[]},e.prototype.postExportMaterialAsync=function(e,t,r){var n=this;return new Promise((function(e){if(r instanceof m.PBRBaseMaterial){if(!r.clearCoat.isEnabled)return void e(t);n._wasUsed=!0,t.extensions=t.extensions||{};var a,o=n._exporter._glTFMaterialExporter._getTextureInfo(r.clearCoat.texture);a=r.clearCoat.useRoughnessFromMainTexture?n._exporter._glTFMaterialExporter._getTextureInfo(r.clearCoat.texture):n._exporter._glTFMaterialExporter._getTextureInfo(r.clearCoat.textureRoughness),r.clearCoat.isTintEnabled&&m.Tools.Warn("Clear Color tint is not supported for glTF export. Ignoring for: ".concat(r.name)),r.clearCoat.remapF0OnInterfaceChange&&m.Tools.Warn("Clear Color F0 remapping is not supported for glTF export. Ignoring for: ".concat(r.name));var i=n._exporter._glTFMaterialExporter._getTextureInfo(r.clearCoat.bumpTexture),s={clearcoatFactor:r.clearCoat.intensity,clearcoatTexture:null!=o?o:void 0,clearcoatRoughnessFactor:r.clearCoat.roughness,clearcoatRoughnessTexture:null!=a?a:void 0,clearcoatNormalTexture:null!=i?i:void 0,hasTextures:function(){return null!==s.clearcoatTexture||null!==s.clearcoatRoughnessTexture||null!==s.clearcoatRoughnessTexture}};t.extensions[U]=s}e(t)}))},e}();S.RegisterExtension(U,(function(e){return new k(e)}));var K="KHR_materials_iridescence",D=function(){function e(e){this.name=K,this.enabled=!0,this.required=!1,this._wasUsed=!1,this._exporter=e}return e.prototype.dispose=function(){},Object.defineProperty(e.prototype,"wasUsed",{get:function(){return this._wasUsed},enumerable:!1,configurable:!0}),e.prototype.postExportMaterialAdditionalTextures=function(e,t,r){var n=[];return r instanceof m.PBRBaseMaterial&&r.iridescence.isEnabled?(r.iridescence.texture&&n.push(r.iridescence.texture),r.iridescence.thicknessTexture&&r.iridescence.thicknessTexture!==r.iridescence.texture&&n.push(r.iridescence.thicknessTexture),n):[]},e.prototype.postExportMaterialAsync=function(e,t,r){var n=this;return new Promise((function(e){if(r instanceof m.PBRBaseMaterial){if(!r.iridescence.isEnabled)return void e(t);n._wasUsed=!0,t.extensions=t.extensions||{};var a=n._exporter._glTFMaterialExporter._getTextureInfo(r.iridescence.texture),o=n._exporter._glTFMaterialExporter._getTextureInfo(r.iridescence.thicknessTexture),i={iridescenceFactor:r.iridescence.intensity,iridescenceIor:r.iridescence.indexOfRefraction,iridescenceThicknessMinimum:r.iridescence.minimumThickness,iridescenceThicknessMaximum:r.iridescence.maximumThickness,iridescenceTexture:null!=a?a:void 0,iridescenceThicknessTexture:null!=o?o:void 0,hasTextures:function(){return null!==i.iridescenceTexture||null!==i.iridescenceThicknessTexture}};t.extensions[K]=i}e(t)}))},e}();S.RegisterExtension(K,(function(e){return new D(e)}));var G="KHR_materials_anisotropy",q=function(){function e(e){this.name=G,this.enabled=!0,this.required=!1,this._wasUsed=!1,this._exporter=e}return e.prototype.dispose=function(){},Object.defineProperty(e.prototype,"wasUsed",{get:function(){return this._wasUsed},enumerable:!1,configurable:!0}),e.prototype.postExportMaterialAdditionalTextures=function(e,t,r){var n=[];return r instanceof m.PBRBaseMaterial&&r.anisotropy.isEnabled&&!r.anisotropy.legacy?(r.anisotropy.texture&&n.push(r.anisotropy.texture),n):[]},e.prototype.postExportMaterialAsync=function(e,t,r){var n=this;return new Promise((function(e){if(r instanceof m.PBRBaseMaterial){if(!r.anisotropy.isEnabled||r.anisotropy.legacy)return void e(t);n._wasUsed=!0,t.extensions=t.extensions||{};var a=n._exporter._glTFMaterialExporter._getTextureInfo(r.anisotropy.texture),o={anisotropyStrength:r.anisotropy.intensity,anisotropyRotation:r.anisotropy.angle,anisotropyTexture:null!=a?a:void 0,hasTextures:function(){return null!==o.anisotropyTexture}};t.extensions[G]=o}e(t)}))},e}();S.RegisterExtension(G,(function(e){return new q(e)}));var W="KHR_materials_sheen",z=function(){function e(e){this.name=W,this.enabled=!0,this.required=!1,this._wasUsed=!1,this._exporter=e}return e.prototype.dispose=function(){},Object.defineProperty(e.prototype,"wasUsed",{get:function(){return this._wasUsed},enumerable:!1,configurable:!0}),e.prototype.postExportMaterialAdditionalTextures=function(e,t,r){return r instanceof m.PBRMaterial&&r.sheen.isEnabled&&r.sheen.texture?[r.sheen.texture]:[]},e.prototype.postExportMaterialAsync=function(e,t,r){var n=this;return new Promise((function(e){var a,o,i,s;if(r instanceof m.PBRMaterial){if(!r.sheen.isEnabled)return void e(t);n._wasUsed=!0,null==t.extensions&&(t.extensions={});var u={sheenColorFactor:r.sheen.color.asArray(),sheenRoughnessFactor:null!==(a=r.sheen.roughness)&&void 0!==a?a:0,hasTextures:function(){return null!==u.sheenColorTexture||null!==u.sheenRoughnessTexture}};r.sheen.texture&&(u.sheenColorTexture=null!==(o=n._exporter._glTFMaterialExporter._getTextureInfo(r.sheen.texture))&&void 0!==o?o:void 0),r.sheen.textureRoughness&&!r.sheen.useRoughnessFromMainTexture?u.sheenRoughnessTexture=null!==(i=n._exporter._glTFMaterialExporter._getTextureInfo(r.sheen.textureRoughness))&&void 0!==i?i:void 0:r.sheen.texture&&r.sheen.useRoughnessFromMainTexture&&(u.sheenRoughnessTexture=null!==(s=n._exporter._glTFMaterialExporter._getTextureInfo(r.sheen.texture))&&void 0!==s?s:void 0),t.extensions[W]=u}e(t)}))},e}();S.RegisterExtension(W,(function(e){return new z(e)}));var H="KHR_materials_unlit",j=function(){function e(){this.name=H,this.enabled=!0,this.required=!1,this._wasUsed=!1}return Object.defineProperty(e.prototype,"wasUsed",{get:function(){return this._wasUsed},enumerable:!1,configurable:!0}),e.prototype.dispose=function(){},e.prototype.postExportMaterialAsync=function(e,t,r){var n=this;return new Promise((function(e){var a=!1;r instanceof m.PBRMaterial?a=r.unlit:r instanceof m.StandardMaterial&&(a=r.disableLighting),a&&(n._wasUsed=!0,null==t.extensions&&(t.extensions={}),t.extensions[H]={}),e(t)}))},e}();S.RegisterExtension(H,(function(){return new j}));var Q="KHR_materials_ior",Y=function(){function e(){this.name=Q,this.enabled=!0,this.required=!1,this._wasUsed=!1}return e.prototype.dispose=function(){},Object.defineProperty(e.prototype,"wasUsed",{get:function(){return this._wasUsed},enumerable:!1,configurable:!0}),e.prototype._isExtensionEnabled=function(e){return!e.unlit&&null!=e.indexOfRefraction&&1.5!=e.indexOfRefraction},e.prototype.postExportMaterialAsync=function(e,t,r){var n=this;return new Promise((function(e){if(r instanceof m.PBRMaterial&&n._isExtensionEnabled(r)){n._wasUsed=!0;var a={ior:r.indexOfRefraction};t.extensions=t.extensions||{},t.extensions[Q]=a}e(t)}))},e}();S.RegisterExtension(Q,(function(e){return new Y}));var X="KHR_materials_specular",Z=function(){function e(e){this.name=X,this.enabled=!0,this.required=!1,this._wasUsed=!1,this._exporter=e}return e.prototype.dispose=function(){},Object.defineProperty(e.prototype,"wasUsed",{get:function(){return this._wasUsed},enumerable:!1,configurable:!0}),e.prototype.postExportMaterialAdditionalTextures=function(e,t,r){var n=[];return r instanceof m.PBRMaterial&&this._isExtensionEnabled(r)?(r.metallicReflectanceTexture&&n.push(r.metallicReflectanceTexture),r.reflectanceTexture&&n.push(r.reflectanceTexture),n):n},e.prototype._isExtensionEnabled=function(e){return!e.unlit&&(null!=e.metallicF0Factor&&1!=e.metallicF0Factor||null!=e.metallicReflectanceColor&&!e.metallicReflectanceColor.equalsFloats(1,1,1)||this._hasTexturesExtension(e))},e.prototype._hasTexturesExtension=function(e){return null!=e.metallicReflectanceTexture||null!=e.reflectanceTexture},e.prototype.postExportMaterialAsync=function(e,t,r){var n=this;return new Promise((function(e){var a,o;if(r instanceof m.PBRMaterial&&n._isExtensionEnabled(r)){n._wasUsed=!0,t.extensions=t.extensions||{};var i=null!==(a=n._exporter._glTFMaterialExporter._getTextureInfo(r.metallicReflectanceTexture))&&void 0!==a?a:void 0,s=null!==(o=n._exporter._glTFMaterialExporter._getTextureInfo(r.reflectanceTexture))&&void 0!==o?o:void 0,u={specularFactor:1==r.metallicF0Factor?void 0:r.metallicF0Factor,specularTexture:i,specularColorFactor:r.metallicReflectanceColor.equalsFloats(1,1,1)?void 0:r.metallicReflectanceColor.asArray(),specularColorTexture:s,hasTextures:function(){return n._hasTexturesExtension(r)}};t.extensions[X]=u}e(t)}))},e}();S.RegisterExtension(X,(function(e){return new Z(e)}));var J="KHR_materials_volume",$=function(){function e(e){this.name=J,this.enabled=!0,this.required=!1,this._wasUsed=!1,this._exporter=e}return e.prototype.dispose=function(){},Object.defineProperty(e.prototype,"wasUsed",{get:function(){return this._wasUsed},enumerable:!1,configurable:!0}),e.prototype.postExportMaterialAdditionalTextures=function(e,t,r){var n=[];return r instanceof m.PBRMaterial&&this._isExtensionEnabled(r)?(r.subSurface.thicknessTexture&&n.push(r.subSurface.thicknessTexture),n):n},e.prototype._isExtensionEnabled=function(e){if(e.unlit)return!1;var t=e.subSurface;return!(!t.isRefractionEnabled&&!t.isTranslucencyEnabled)&&(null!=t.maximumThickness&&0!=t.maximumThickness||null!=t.tintColorAtDistance&&t.tintColorAtDistance!=Number.POSITIVE_INFINITY||null!=t.tintColor&&t.tintColor!=m.Color3.White()||this._hasTexturesExtension(e))},e.prototype._hasTexturesExtension=function(e){return null!=e.subSurface.thicknessTexture},e.prototype.postExportMaterialAsync=function(e,t,r){var n=this;return new Promise((function(e){var a;if(r instanceof m.PBRMaterial&&n._isExtensionEnabled(r)){n._wasUsed=!0;var o=r.subSurface,i={thicknessFactor:0==o.maximumThickness?void 0:o.maximumThickness,thicknessTexture:null!==(a=n._exporter._glTFMaterialExporter._getTextureInfo(o.thicknessTexture))&&void 0!==a?a:void 0,attenuationDistance:o.tintColorAtDistance==Number.POSITIVE_INFINITY?void 0:o.tintColorAtDistance,attenuationColor:o.tintColor.equalsFloats(1,1,1)?void 0:o.tintColor.asArray(),hasTextures:function(){return n._hasTexturesExtension(r)}};t.extensions=t.extensions||{},t.extensions[J]=i}e(t)}))},e}();S.RegisterExtension(J,(function(e){return new $(e)}));var ee="KHR_materials_dispersion",te=function(){function e(){this.name=ee,this.enabled=!0,this.required=!1,this._wasUsed=!1}return e.prototype.dispose=function(){},Object.defineProperty(e.prototype,"wasUsed",{get:function(){return this._wasUsed},enumerable:!1,configurable:!0}),e.prototype._isExtensionEnabled=function(e){if(e.unlit)return!1;var t=e.subSurface;return!(!t.isRefractionEnabled&&!t.isDispersionEnabled)},e.prototype.postExportMaterialAsync=function(e,t,r){var n=this;return new Promise((function(e){if(r instanceof m.PBRMaterial&&n._isExtensionEnabled(r)){n._wasUsed=!0;var a={dispersion:r.subSurface.dispersion};t.extensions=t.extensions||{},t.extensions[ee]=a}e(t)}))},e}();S.RegisterExtension(ee,(function(){return new te}));var re="KHR_materials_transmission",ne=function(){function e(e){this.name=re,this.enabled=!0,this.required=!1,this._wasUsed=!1,this._exporter=e}return e.prototype.dispose=function(){},Object.defineProperty(e.prototype,"wasUsed",{get:function(){return this._wasUsed},enumerable:!1,configurable:!0}),e.prototype.postExportMaterialAdditionalTextures=function(e,t,r){var n=[];return r instanceof m.PBRMaterial&&this._isExtensionEnabled(r)?(r.subSurface.thicknessTexture&&n.push(r.subSurface.thicknessTexture),n):n},e.prototype._isExtensionEnabled=function(e){if(e.unlit)return!1;var t=e.subSurface;return t.isRefractionEnabled&&null!=t.refractionIntensity&&0!=t.refractionIntensity||this._hasTexturesExtension(e)},e.prototype._hasTexturesExtension=function(e){return null!=e.subSurface.refractionIntensityTexture},e.prototype.postExportMaterialAsync=function(e,t,r){return b(this,void 0,void 0,(function(){var n,a,o,i,s=this;return A(this,(function(u){switch(u.label){case 0:return r instanceof m.PBRMaterial&&this._isExtensionEnabled(r)?(this._wasUsed=!0,n=r.subSurface,a=0===n.refractionIntensity?void 0:n.refractionIntensity,o={transmissionFactor:a,hasTextures:function(){return s._hasTexturesExtension(r)}},n.refractionIntensityTexture?n.useGltfStyleTextures?[4,this._exporter._glTFMaterialExporter._exportTextureInfoAsync(n.refractionIntensityTexture,"image/png")]:[3,2]:[3,3]):[3,4];case 1:return(i=u.sent())&&(o.transmissionTexture=i),[3,3];case 2:m.Logger.Warn("".concat(e,": Exporting a subsurface refraction intensity texture without `useGltfStyleTextures` is not supported")),u.label=3;case 3:t.extensions||(t.extensions={}),t.extensions[re]=o,u.label=4;case 4:return[2,t]}}))}))},e}();S.RegisterExtension(re,(function(e){return new ne(e)}));var ae="EXT_mesh_gpu_instancing",oe=function(){function e(e){this.name=ae,this.enabled=!0,this.required=!1,this._wasUsed=!1,this._exporter=e}return e.prototype.dispose=function(){},Object.defineProperty(e.prototype,"wasUsed",{get:function(){return this._wasUsed},enumerable:!1,configurable:!0}),e.prototype.postExportNodeAsync=function(e,t,r,n,a){var o=this;return new Promise((function(e){if(t&&r instanceof m.Mesh&&r.hasThinInstances&&a){o._wasUsed=!0;for(var n=m.Vector3.Zero(),i=m.Quaternion.Identity(),s=m.Vector3.One(),u=r.thinInstanceGetWorldMatrices(),l=m.TmpVectors.Vector3[2],c=m.TmpVectors.Quaternion[1],f=m.TmpVectors.Vector3[3],p=!1,h=!1,d=!1,g=new Float32Array(3*r.thinInstanceCount),_=new Float32Array(4*r.thinInstanceCount),x=new Float32Array(3*r.thinInstanceCount),y=0,T=0,v=u;T<v.length;T++)v[T].decompose(f,c,l),g.set(l.asArray(),3*y),_.set(c.normalize().asArray(),4*y),x.set(f.asArray(),3*y),p=p||!l.equalsWithEpsilon(n),h=h||!c.equalsWithEpsilon(i),d=d||!f.equalsWithEpsilon(s),y++;var b={attributes:{}};p&&(b.attributes.TRANSLATION=o._buildAccessor(g,"VEC3",r.thinInstanceCount,a,5126)),h&&(b.attributes.ROTATION=o._buildAccessor(_,"VEC4",r.thinInstanceCount,a,5126)),d&&(b.attributes.SCALE=o._buildAccessor(x,"VEC3",r.thinInstanceCount,a,5126)),t.extensions=t.extensions||{},t.extensions[ae]=b}e(t)}))},e.prototype._buildAccessor=function(e,t,r,n,a){var o=n.getByteOffset();switch(a){case 5126:for(var i=0;i!=e.length;i++)n.setFloat32(e[i]);break;case 5120:for(i=0;i!=e.length;i++)n.setByte(127*e[i]);break;case 5122:for(i=0;i!=e.length;i++)n.setInt16(32767*e[i])}var s={buffer:0,byteOffset:o,byteLength:e.length*m.VertexBuffer.GetTypeByteLength(a)},u=this._exporter._bufferViews.length;this._exporter._bufferViews.push(s);var l=this._exporter._accessors.length,c={bufferView:u,componentType:a,count:r,type:t,normalized:5120==a||5122==a};return this._exporter._accessors.push(c),l},e}();S.RegisterExtension(ae,(function(e){return new oe(e)}));var ie="KHR_materials_emissive_strength",se=function(){function e(){this.name=ie,this.enabled=!0,this.required=!1,this._wasUsed=!1}return e.prototype.dispose=function(){},Object.defineProperty(e.prototype,"wasUsed",{get:function(){return this._wasUsed},enumerable:!1,configurable:!0}),e.prototype.postExportMaterialAsync=function(e,t,r){var n=this;return new Promise((function(e){if(!(r instanceof m.PBRMaterial))return e(t);var a=r.emissiveColor.asArray(),o=Math.max.apply(Math,a);if(o>1){n._wasUsed=!0,t.extensions||(t.extensions={});var i={emissiveStrength:o},s=r.emissiveColor.scale(1/i.emissiveStrength);t.emissiveFactor=s.asArray(),t.extensions[ie]=i}return e(t)}))},e}();S.RegisterExtension(ie,(function(e){return new se}));var ue="KHR_materials_diffuse_transmission",le=function(){function e(e){this.name=ue,this.enabled=!0,this.required=!1,this._wasUsed=!1,this._exporter=e}return e.prototype.dispose=function(){},Object.defineProperty(e.prototype,"wasUsed",{get:function(){return this._wasUsed},enumerable:!1,configurable:!0}),e.prototype.postExportMaterialAdditionalTextures=function(e,t,r){var n=[];return r instanceof m.PBRMaterial&&this._isExtensionEnabled(r)?(r.subSurface.thicknessTexture&&n.push(r.subSurface.thicknessTexture),n):n},e.prototype._isExtensionEnabled=function(e){if(e.unlit)return!1;var t=e.subSurface;return!!t.isTranslucencyEnabled&&!e.unlit&&!t.useAlbedoToTintTranslucency&&t.useGltfStyleTextures&&1===t.volumeIndexOfRefraction&&0===t.minimumThickness&&0===t.maximumThickness},e.prototype._hasTexturesExtension=function(e){return null!=e.subSurface.translucencyIntensityTexture||null!=e.subSurface.translucencyColorTexture},e.prototype.postExportMaterialAsync=function(e,t,r){var n=this;return new Promise((function(e){var a,o;if(r instanceof m.PBRMaterial&&n._isExtensionEnabled(r)){n._wasUsed=!0;var i=r.subSurface,s={diffuseTransmissionFactor:1==i.translucencyIntensity?void 0:i.translucencyIntensity,diffuseTransmissionTexture:null!==(a=n._exporter._glTFMaterialExporter._getTextureInfo(i.translucencyIntensityTexture))&&void 0!==a?a:void 0,diffuseTransmissionColorFactor:!i.translucencyColor||i.translucencyColor.equalsFloats(1,1,1)?void 0:i.translucencyColor.asArray(),diffuseTransmissionColorTexture:null!==(o=n._exporter._glTFMaterialExporter._getTextureInfo(i.translucencyColorTexture))&&void 0!==o?o:void 0,hasTextures:function(){return n._hasTexturesExtension(r)}};t.extensions=t.extensions||{},t.extensions[ue]=s}e(t)}))},e}();S.RegisterExtension(ue,(function(e){return new le(e)}));var ce=function(){function e(){}return e.CreateSTL=function(e,t,r,n,a,o,i,s){void 0===t&&(t=!0),void 0===r&&(r="stlmesh"),void 0===n&&(n=!1),void 0===a&&(a=!0),void 0===o&&(o=!1),void 0===i&&(i=!1),void 0===s&&(s=!1);var u=function(e,t,r){var n=[3*e[r],3*e[r+1],3*e[r+2]],a=[new m.Vector3(t[n[0]],t[n[0]+2],t[n[0]+1]),new m.Vector3(t[n[1]],t[n[1]+2],t[n[1]+1]),new m.Vector3(t[n[2]],t[n[2]+2],t[n[2]+1])],o=a[0].subtract(a[1]),i=a[2].subtract(a[1]);return{v:a,n:m.Vector3.Cross(i,o).normalize()}},l=function(e,t,r,n){return t=c(e,t,r.x,n),t=c(e,t,r.y,n),c(e,t,r.z,n)},c=function(e,t,r,n){return e.setFloat32(t,r,n),t+4},f=function(e){if(i){var t=e;e instanceof m.InstancedMesh&&(t=e.sourceMesh);var r=t.getVerticesData(m.VertexBuffer.PositionKind,!0,!0);if(!r)return[];var n=m.Vector3.Zero(),a=void 0;for(a=0;a<r.length;a+=3)m.Vector3.TransformCoordinatesFromFloatsToRef(r[a],r[a+1],r[a+2],e.computeWorldMatrix(!0),n).toArray(r,a);return r}return e.getVerticesData(m.VertexBuffer.PositionKind)||[]};i&&(o=!0);var p="",h=0,d=0;if(n){for(var g=0;g<e.length;g++)h+=(T=(x=e[g]).getIndices())?T.length/3:0;var _=new ArrayBuffer(84+50*h);d+=80,(p=new DataView(_)).setUint32(d,h,a),d+=4}else s||(p="solid stlmesh\r\n");for(g=0;g<e.length;g++){var x=e[g];!n&&s&&(p+="solid "+x.name+"\r\n"),!o&&x instanceof m.Mesh&&x.bakeCurrentTransformIntoVertices();for(var y=f(x),T=x.getIndices()||[],v=0;v<T.length;v+=3){var b=u(T,y,v);n?(d=l(p,d,b.n,a),d=l(p,d,b.v[0],a),d=l(p,d,b.v[1],a),d=l(p,d,b.v[2],a),d+=2):(p+="\tfacet normal "+b.n.x+" "+b.n.y+" "+b.n.z+"\r\n",p+="\t\touter loop\r\n",p+="\t\t\tvertex "+b.v[0].x+" "+b.v[0].y+" "+b.v[0].z+"\r\n",p+="\t\t\tvertex "+b.v[1].x+" "+b.v[1].y+" "+b.v[1].z+"\r\n",p+="\t\t\tvertex "+b.v[2].x+" "+b.v[2].y+" "+b.v[2].z+"\r\n",p+="\t\tendloop\r\n",p+="\tendfacet\r\n")}!n&&s&&(p+="endsolid "+name+"\r\n")}if(n||s||(p+="endsolid stlmesh"),t){var A=document.createElement("a"),M=new Blob([p],{type:"application/octet-stream"});A.href=window.URL.createObjectURL(M),A.download=r+".stl",A.click()}return p},e}();function fe(e,t,r){void 0===r&&(r=3);for(var n=[],a=0;a<e.length/r;a++){var o=e[a*r],i=e[a*r+1],s=e[a*r+2];n.push("(".concat(o.toPrecision(t.precision),", ").concat(i.toPrecision(t.precision),", ").concat(s.toPrecision(t.precision),")"))}return n.join(", ")}function pe(e,t){for(var r=[],n=0;n<e.length/2;n++){var a=e[2*n],o=e[2*n+1];r.push("(".concat(a.toPrecision(t.precision),", ").concat((1-o).toPrecision(t.precision),")"))}return r.join(", ")}function he(e,t){var r=function(e,t){var r=e.getVerticesData(m.VertexBuffer.PositionKind),n=e.getVerticesData(m.VertexBuffer.PositionKind);if(r&&n)return'\n\tdef Mesh "'.concat("Geometry",'"\n\t{\n\t\tint[] faceVertexCounts = [').concat(function(e){var t,r=(null===(t=e.getIndices())||void 0===t?void 0:t.length)?e.getTotalIndices():e.getTotalVertices();return Array(r/3).fill(3).join(", ")}(e),"]\n\t\tint[] faceVertexIndices = [").concat(function(e){var t=e.getIndices(),r=[];if(null!==t)for(var n=0;n<t.length;n++)r.push(t[n]);else{var a=e.getTotalVertices();for(n=0;n<a;n++)r.push(n)}return r.join(", ")}(e),"]\n\t\tnormal3f[] normals = [").concat(fe(n,t),'] (\n\t\t\tinterpolation = "vertex"\n\t\t)\n\t\tpoint3f[] points = [').concat(fe(r,t),"]\n        ").concat(function(e,t){for(var r="",n=0;n<4;n++){var a=n>0?n:"",o=e.getVerticesData(m.VertexBuffer.UVKind+(a||""));o&&(r+="\n\t\ttexCoord2f[] primvars:st".concat(a," = [").concat(pe(o,t),'] (\n\t\t\tinterpolation = "vertex"\n\t\t)'))}var i=e.getVerticesData(m.VertexBuffer.ColorKind);return i&&(r+="\n\tcolor3f[] primvars:displayColor = [".concat(fe(i,t,4),'] (\n\t\tinterpolation = "vertex"\n\t\t)')),r}(e,t),'\n\t\tuniform token subdivisionScheme = "none"\n\t}\n')}(e,t);return'\n        def "Geometry"\n        {\n        '.concat(r,"\n        }\n        ")}function de(e){var t='#usda 1.0\n    (\n        customLayerData = {\n            string creator = "Babylon.js USDZExportAsync"\n        }\n        defaultPrim = "Root"\n        metersPerUnit = 1\n        upAxis = "Y"\n    )';return t+=e,fflate.strToU8(t)}function me(e){var t=e.m;return"( ".concat(ge(t,0),", ").concat(ge(t,4),", ").concat(ge(t,8),", ").concat(ge(t,12)," )")}function ge(e,t){return"(".concat(e[t+0],", ").concat(e[t+1],", ").concat(e[t+2],", ").concat(e[t+3],")")}function _e(e){var t="Object_"+e.uniqueId,r=e.getWorldMatrix().clone();r.determinant()<0&&r.multiplyToRef(m.Matrix.Scaling(-1,1,1),r);var n=me(r);return'def Xform "'.concat(t,'" (\n\tprepend references = @./geometries/Geometry_').concat(e.geometry.uniqueId,'.usda@</Geometry>\n\tprepend apiSchemas = ["MaterialBindingAPI"]\n)\n{\n\tmatrix4d xformOp:transform = ').concat(n,'\n\tuniform token[] xformOpOrder = ["xformOp:transform"]\t\n\n    rel material:binding = </Materials/Material_').concat(e.material.uniqueId,">\n}\n\n")}function xe(e){switch(e){case m.Constants.TEXTURE_CLAMP_ADDRESSMODE:return"clamp";case m.Constants.TEXTURE_MIRROR_ADDRESSMODE:return"mirror";case m.Constants.TEXTURE_WRAP_ADDRESSMODE:default:return"repeat"}}function ye(e){return"(".concat(e.x,", ").concat(e.y,")")}function Te(e){return"(".concat(e.r,", ").concat(e.g,", ").concat(e.b,")")}function ve(e,t,r,n,a,o){var i=e.getInternalTexture().uniqueId+"_"+e.invertY;a[i]=e;var s=e.coordinatesIndex>0?"st"+e.coordinatesIndex:"st",u=new m.Vector2(e.uScale,e.vScale),l=new m.Vector2(e.uOffset,e.vOffset),c=e.wAng,f=Math.sin(c),p=Math.cos(c);return l.y=1-l.y-u.y,l.x+=f*u.x,l.y+=(1-p)*u.y,'\n    def Shader "PrimvarReader_'.concat(r,'"\n    {\n        uniform token info:id = "UsdPrimvarReader_float2"\n        float2 inputs:fallback = (0.0, 0.0)\n        token inputs:varname = "').concat(s,'"\n        float2 outputs:result\n    }\n\n    def Shader "Transform2d_').concat(r,'"\n    {\n        uniform token info:id = "UsdTransform2d"\n        token inputs:in.connect = </Materials/Material_').concat(t.uniqueId,"/PrimvarReader_").concat(r,".outputs:result>\n        float inputs:rotation = ").concat((c*(180/Math.PI)).toFixed(o.precision),"\n        float2 inputs:scale = ").concat(ye(u),"\n        float2 inputs:translation = ").concat(ye(l),'\n        float2 outputs:result\n    }\n\n    def Shader "Texture_').concat(e.uniqueId,"_").concat(r,'"\n    {\n        uniform token info:id = "UsdUVTexture"\n        asset inputs:file = @textures/Texture_').concat(i,".png@\n        float2 inputs:st.connect = </Materials/Material_").concat(t.uniqueId,"/Transform2d_").concat(r,".outputs:result>\n        ").concat(n?"float4 inputs:scale = "+function(e){return"(".concat(e.r,", ").concat(e.g,", ").concat(e.b,", 1.0)")}(n):"",'\n        token inputs:sourceColorSpace = "').concat(e.gammaSpace?"raw":"sRGB",'"\n        token inputs:wrapS = "').concat(xe(e.wrapU),'"\n        token inputs:wrapT = "').concat(xe(e.wrapV),'"\n        float outputs:r\n        float outputs:g\n        float outputs:b\n        float3 outputs:rgb\n        ').concat(t.needAlphaBlending()?"float outputs:a":"","\n    }")}function be(e,t,r){var n="\t\t\t",a=[],o=[],i=function(e){switch(e.getClassName()){case"StandardMaterial":return{diffuseMap:e.diffuseTexture,diffuse:e.diffuseColor,alphaCutOff:e.alphaCutOff,emissiveMap:e.emissiveTexture,emissive:e.emissiveColor,roughnessMap:null,normalMap:null,metalnessMap:null,roughness:1,metalness:0,aoMap:null,aoMapIntensity:0,alphaMap:e.opacityTexture,ior:1};case"PBRMaterial":return{diffuseMap:e.albedoTexture,diffuse:e.albedoColor,alphaCutOff:e.alphaCutOff,emissiveMap:e.emissiveTexture,emissive:e.emissiveColor,normalMap:e.bumpTexture,roughnessMap:e.metallicTexture,roughnessChannel:e.useRoughnessFromMetallicTextureAlpha?"a":"g",roughness:e.roughness||1,metalnessMap:e.metallicTexture,metalnessChannel:e.useMetallnessFromMetallicTextureBlue?"b":"r",metalness:e.metallic||0,aoMap:e.ambientTexture,aoMapChannel:e.useAmbientInGrayScale?"r":"rgb",aoMapIntensity:e.ambientTextureStrength,alphaMap:e.opacityTexture,ior:e.indexOfRefraction};case"PBRMetallicRoughnessMaterial":return{diffuseMap:e.baseTexture,diffuse:e.baseColor,alphaCutOff:e.alphaCutOff,emissiveMap:e.emissiveTexture,emissive:e.emissiveColor,normalMap:e.normalTexture,roughnessMap:e.metallicTexture,roughnessChannel:e.useRoughnessFromMetallicTextureAlpha?"a":"g",roughness:e.roughness||1,metalnessMap:e.metallicTexture,metalnessChannel:e.useMetallnessFromMetallicTextureBlue?"b":"r",metalness:e.metallic||0,aoMap:e.ambientTexture,aoMapChannel:e.useAmbientInGrayScale?"r":"rgb",aoMapIntensity:e.ambientTextureStrength,alphaMap:e.opacityTexture,ior:e.indexOfRefraction};default:return{diffuseMap:null,diffuse:null,emissiveMap:null,emissemissiveiveColor:null,normalMap:null,roughnessMap:null,metalnessMap:null,alphaCutOff:0,roughness:0,metalness:0,aoMap:null,aoMapIntensity:0,alphaMap:null,ior:1}}}(e),s=i.diffuseMap,u=i.diffuse,l=i.alphaCutOff,c=i.emissiveMap,f=i.emissive,p=i.normalMap,h=i.roughnessMap,d=i.roughnessChannel,g=i.roughness,_=i.metalnessMap,x=i.metalnessChannel,y=i.metalness,T=i.aoMap,v=i.aoMapChannel,b=i.aoMapIntensity,A=i.alphaMap,M=i.ior;return null!==s?(a.push("".concat(n,"color3f inputs:diffuseColor.connect = </Materials/Material_").concat(e.uniqueId,"/Texture_").concat(s.uniqueId,"_diffuse.outputs:rgb>")),e.needAlphaBlending()?a.push("".concat(n,"float inputs:opacity.connect = </Materials/Material_").concat(e.uniqueId,"/Texture_").concat(s.uniqueId,"_diffuse.outputs:a>")):e.needAlphaTesting()&&(a.push("".concat(n,"float inputs:opacity.connect = </Materials/Material_").concat(e.uniqueId,"/Texture_").concat(s.uniqueId,"_diffuse.outputs:a>")),a.push("".concat(n,"float inputs:opacityThreshold = ").concat(l))),o.push(ve(s,e,"diffuse",u,t,r))):a.push("".concat(n,"color3f inputs:diffuseColor = ").concat(Te(u||m.Color3.White()))),null!==c?(a.push("".concat(n,"color3f inputs:emissiveColor.connect = </Materials/Material_").concat(e.uniqueId,"/Texture_").concat(c.uniqueId,"_emissive.outputs:rgb>")),o.push(ve(c,e,"emissive",f,t,r))):f&&f.toLuminance()>0&&a.push("".concat(n,"color3f inputs:emissiveColor = ").concat(Te(f))),null!==p&&(a.push("".concat(n,"normal3f inputs:normal.connect = </Materials/Material_").concat(e.uniqueId,"/Texture_").concat(p.uniqueId,"_normal.outputs:rgb>")),o.push(ve(p,e,"normal",null,t,r))),null!==T&&(a.push("".concat(n,"float inputs:occlusion.connect = </Materials/Material_").concat(e.uniqueId,"/Texture_").concat(T.uniqueId,"_occlusion.outputs:").concat(v,">")),o.push(ve(T,e,"occlusion",new m.Color3(b,b,b),t,r))),null!==h?(a.push("".concat(n,"float inputs:roughness.connect = </Materials/Material_").concat(e.uniqueId,"/Texture_").concat(h.uniqueId,"_roughness.outputs:").concat(d,">")),o.push(ve(h,e,"roughness",new m.Color3(g,g,g),t,r))):a.push("".concat(n,"float inputs:roughness = ").concat(g)),null!==_?(a.push("".concat(n,"float inputs:metallic.connect = </Materials/Material_").concat(e.uniqueId,"/Texture_").concat(_.uniqueId,"_metallic.outputs:").concat(x,">")),o.push(ve(_,e,"metallic",new m.Color3(y,y,y),t,r))):a.push("".concat(n,"float inputs:metallic = ").concat(y)),null!==A?(a.push("".concat(n,"float inputs:opacity.connect = </Materials/Material_").concat(e.uniqueId,"/Texture_").concat(A.uniqueId,"_opacity.outputs:r>")),a.push("".concat(n,"float inputs:opacityThreshold = 0.0001")),o.push(ve(A,e,"opacity",null,t,r))):a.push("".concat(n,"float inputs:opacity = ").concat(e.alpha)),a.push("".concat(n,"float inputs:ior = ").concat(M)),'\n\tdef Material "Material_'.concat(e.uniqueId,'"\n\t{\n\t\tdef Shader "PreviewSurface"\n\t\t{\n\t\t\tuniform token info:id = "UsdPreviewSurface"\n').concat(a.join("\n"),"\n\t\t\tint inputs:useSpecularWorkflow = 0\n\t\t\ttoken outputs:surface\n\t\t}\n\n\t\ttoken outputs:surface.connect = </Materials/Material_").concat(e.uniqueId,"/PreviewSurface.outputs:surface>\n\n").concat(o.join("\n"),"\n\n\t}\n")}function Ae(e,t,r){return b(this,void 0,void 0,(function(){var n,a,o,i,s,u,l,c,f,p,h,d,g,_,x,y,T,b,M,E,w,F,C,R,S,V,I,B;return A(this,(function(A){switch(A.label){case 0:return n=v({fflateUrl:"https://unpkg.com/fflate@0.8.2",includeAnchoringProperties:!0,anchoringType:"plane",planeAnchoringAlignment:"horizontal",modelFileName:"model.usda",precision:5,exportCamera:!1,cameraSensorWidth:35},t),"undefined"!=typeof fflate?[3,2]:[4,m.Tools.LoadScriptAsync(n.fflateUrl)];case 1:A.sent(),A.label=2;case 2:for((a={})[n.modelFileName]=null,o='#usda 1.0\n    (\n        customLayerData = {\n            string creator = "Babylon.js USDZExportAsync"\n        }\n        defaultPrim = "Root"\n        metersPerUnit = 1\n        upAxis = "Y"\n    )',o+=function(e){var t=!0===e.includeAnchoringProperties?'\n\t\ttoken preliminary:anchoring:type = "'.concat(e.anchoringType,'"\n\t\ttoken preliminary:planeAnchoring:alignment = "').concat(e.planeAnchoringAlignment,'"'):"";return'def Xform "Root"\n    {\n        def Scope "Scenes" (\n            kind = "sceneLibrary"\n        )\n        {\n            def Xform "Scene" (\n                customData = {\n                    bool preliminary_collidesWithEnvironment = 0\n                    string sceneName = "Scene"\n                }\n                sceneName = "Scene"\n            )\n            {'.concat(t,"\n            ")}(n),i={},s=0,u=e.meshes;s<u.length;s++)0!==(l=u[s]).getTotalVertices()&&(f=(c=l).geometry,(p=c.material)&&f&&(!r||r(c))&&(-1!==["StandardMaterial","PBRMaterial","PBRMetallicRoughnessMaterial"].indexOf(p.getClassName())?((h="geometries/Geometry_"+f.uniqueId+".usda")in a||(d=he(f,n),a[h]=de(d)),p.uniqueId in i||(i[p.uniqueId]=p),o+=_e(c)):m.Tools.Warn("USDZExportAsync does not support this material type: "+p.getClassName())));for(y in e.activeCamera&&n.exportCamera&&(o+=function(e,t){var r="Camera_"+e.uniqueId,n=me(m.Matrix.RotationY(Math.PI).multiply(e.getWorldMatrix()));if(e.mode===m.Constants.ORTHOGRAPHIC_CAMERA)return'def Camera "'.concat(r,'"\n\t\t{\n\t\t\tmatrix4d xformOp:transform = ').concat(n,'\n\t\t\tuniform token[] xformOpOrder = ["xformOp:transform"]\n\n\t\t\tfloat2 clippingRange = (').concat(e.minZ.toPrecision(t.precision),", ").concat(e.maxZ.toPrecision(t.precision),")\n\t\t\tfloat horizontalAperture = ").concat((10*(Math.abs(e.orthoLeft||1)+Math.abs(e.orthoRight||1))).toPrecision(t.precision),"\n\t\t\tfloat verticalAperture = ").concat((10*(Math.abs(e.orthoTop||1)+Math.abs(e.orthoBottom||1))).toPrecision(t.precision),'\n\t\t\ttoken projection = "orthographic"\n\t\t}\n\t\n\t');var a=e.getEngine().getAspectRatio(e),o=t.cameraSensorWidth||35;return'def Camera "'.concat(r,'"\n\t\t{\n\t\t\tmatrix4d xformOp:transform = ').concat(n,'\n\t\t\tuniform token[] xformOpOrder = ["xformOp:transform"]\n\n\t\t\tfloat2 clippingRange = (').concat(e.minZ.toPrecision(t.precision),", ").concat(e.maxZ.toPrecision(t.precision),")\n\t\t\tfloat focalLength = ").concat((o/(2*Math.tan(.5*e.fov))).toPrecision(t.precision),'\n            token projection = "perspective"\n\t\t\tfloat horizontalAperture = ').concat((o*a).toPrecision(t.precision),"\n\t\t\tfloat verticalAperture = ").concat((o/a).toPrecision(t.precision),"            \n\t\t}\n\t\n\t")}(e.activeCamera,n)),o+="\n            }\n        }\n    }",o+=function(e,t,r){var n=[];for(var a in e){var o=e[a];n.push(be(o,t,r))}return'\n    def "Materials"\n{\n'.concat(n.join(""),"\n}\n\n")}(i,g={},n),a[n.modelFileName]=fflate.strToU8(o),x=[],_=g)x.push(y);T=0,A.label=3;case 3:return T<x.length?(y=x[T])in _?(M=g[b=y],E=M.getSize(),[4,M.readPixels()]):[3,6]:[3,7];case 4:if(!(w=A.sent()))throw new Error("Texture data is not available");return[4,m.DumpTools.DumpDataAsync(E.width,E.height,w,"image/png",void 0,!1,!0)];case 5:F=A.sent(),a["textures/Texture_".concat(b,".png")]=new Uint8Array(F).slice(),A.label=6;case 6:return T++,[3,3];case 7:for(R in C=0,a)(S=a[R])&&(V=34+R.length,4!=(I=63&(C+=V))&&(B=new Uint8Array(64-I),a[R]=[S,{extra:{12345:B}}]),C=S.length);return[2,fflate.zipSync(a,{level:0})]}}))}))}var Me=void 0!==n.g?n.g:"undefined"!=typeof window?window:void 0;if(void 0!==Me){Me.BABYLON=Me.BABYLON||{};var Ee=Me.BABYLON;Ee.GLTF2=Ee.GLTF2||{},Ee.GLTF2.Exporter=Ee.GLTF2.Exporter||{},Ee.GLTF2.Exporter.Extensions=Ee.GLTF2.Exporter.Extensions||{};var we=[];for(var Fe in i)Ee[Fe]=i[Fe],we.push(Fe);for(var Fe in s)Ee[Fe]=s[Fe],we.push(Fe);for(var Fe in u)Ee[Fe]=u[Fe],we.push(Fe);for(var Fe in l)Ee.GLTF2.Exporter.Extensions[Fe]=l[Fe],we.push(Fe);for(var Fe in c)we.indexOf(Fe)>-1||(Ee.GLTF2.Exporter[Fe]=c[Fe])}var Ce=void 0!==n.g?n.g:"undefined"!=typeof window?window:void 0;if(void 0!==Ce)for(var Re in o)Ce.BABYLON[Re]=o[Re];var Se=void 0!==n.g?n.g:"undefined"!=typeof window?window:void 0;if(void 0!==Se)for(var Ve in f)Se.BABYLON[Ve]=f[Ve];var Ie=void 0!==n.g?n.g:"undefined"!=typeof window?window:void 0;if(void 0!==Ie)for(var Be in p)Ie.BABYLON[Be]=p[Be];const Pe=h;return a.default})()));
//# sourceMappingURL=babylonjs.serializers.min.js.map