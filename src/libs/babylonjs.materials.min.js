!function(e,i){"object"==typeof exports&&"object"==typeof module?module.exports=i(require("babylonjs")):"function"==typeof define&&define.amd?define("babylonjs-materials",["babylonjs"],i):"object"==typeof exports?exports["babylonjs-materials"]=i(require("babylonjs")):e.MATERIALS=i(e.BABYLON)}("undefined"!=typeof self?self:"undefined"!=typeof global?global:this,(e=>(()=>{"use strict";var i={597:i=>{i.exports=e}},t={};function n(e){var r=t[e];if(void 0!==r)return r.exports;var o=t[e]={exports:{}};return i[e](o,o.exports,n),o.exports}n.d=(e,i)=>{for(var t in i)n.o(i,t)&&!n.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:i[t]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,i)=>Object.prototype.hasOwnProperty.call(e,i),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};n.d(r,{default:()=>Z});var o={};n.r(o),n.d(o,{CellMaterial:()=>c,CustomMaterial:()=>m,CustomShaderStructure:()=>h,FireMaterial:()=>x,FurMaterial:()=>E,GradientMaterial:()=>P,GridMaterial:()=>A,LavaMaterial:()=>L,MixMaterial:()=>F,NormalMaterial:()=>N,PBRCustomMaterial:()=>T,ShaderAlbedoParts:()=>v,ShaderAlebdoParts:()=>g,ShaderSpecialParts:()=>p,ShadowOnlyMaterial:()=>O,SimpleMaterial:()=>U,SkyMaterial:()=>w,TerrainMaterial:()=>z,TriPlanarMaterial:()=>H,WaterMaterial:()=>X});var s={};n.r(s),n.d(s,{CellMaterial:()=>c,CustomMaterial:()=>m,CustomShaderStructure:()=>h,FireMaterial:()=>x,FurMaterial:()=>E,GradientMaterial:()=>P,GridMaterial:()=>A,LavaMaterial:()=>L,MixMaterial:()=>F,NormalMaterial:()=>N,PBRCustomMaterial:()=>T,ShaderAlbedoParts:()=>v,ShaderAlebdoParts:()=>g,ShaderSpecialParts:()=>p,ShadowOnlyMaterial:()=>O,SimpleMaterial:()=>U,SkyMaterial:()=>w,TerrainMaterial:()=>z,TriPlanarMaterial:()=>H,WaterMaterial:()=>X});var a=function(e,i){return a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var t in i)Object.prototype.hasOwnProperty.call(i,t)&&(e[t]=i[t])},a(e,i)};function f(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function t(){this.constructor=e}a(e,i),e.prototype=null===i?Object.create(i):(t.prototype=i.prototype,new t)}function l(e,i,t,n){var r,o=arguments.length,s=o<3?i:null===n?n=Object.getOwnPropertyDescriptor(i,t):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,i,t,n);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(i,t,s):r(i,t))||s);return o>3&&s&&Object.defineProperty(i,t,s),s}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var u=n(597);u.ShaderStore.ShadersStore.cellPixelShader="precision highp float;uniform vec4 vEyePosition;uniform vec4 vDiffuseColor;varying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\n#endif\n#include<helperFunctions>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#include<lightsFragmentFunctions>\n#include<shadowsFragmentFunctions>\n#ifdef DIFFUSE\nvarying vec2 vDiffuseUV;uniform sampler2D diffuseSampler;uniform vec2 vDiffuseInfos;\n#endif\n#include<clipPlaneFragmentDeclaration>\n#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\n#include<logDepthDeclaration>\n#include<fogFragmentDeclaration>\nvec3 computeCustomDiffuseLighting(lightingInfo info,vec3 diffuseBase,float shadow)\n{diffuseBase=info.diffuse*shadow;\n#ifdef CELLBASIC\nfloat level=1.0;if (info.ndl<0.5)\nlevel=0.5;diffuseBase.rgb*vec3(level,level,level);\n#else\nfloat ToonThresholds[4];ToonThresholds[0]=0.95;ToonThresholds[1]=0.5;ToonThresholds[2]=0.2;ToonThresholds[3]=0.03;float ToonBrightnessLevels[5];ToonBrightnessLevels[0]=1.0;ToonBrightnessLevels[1]=0.8;ToonBrightnessLevels[2]=0.6;ToonBrightnessLevels[3]=0.35;ToonBrightnessLevels[4]=0.2;if (info.ndl>ToonThresholds[0])\n{diffuseBase.rgb*=ToonBrightnessLevels[0];}\nelse if (info.ndl>ToonThresholds[1])\n{diffuseBase.rgb*=ToonBrightnessLevels[1];}\nelse if (info.ndl>ToonThresholds[2])\n{diffuseBase.rgb*=ToonBrightnessLevels[2];}\nelse if (info.ndl>ToonThresholds[3])\n{diffuseBase.rgb*=ToonBrightnessLevels[3];}\nelse\n{diffuseBase.rgb*=ToonBrightnessLevels[4];}\n#endif\nreturn max(diffuseBase,vec3(0.2));}\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void)\n{\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);vec4 baseColor=vec4(1.,1.,1.,1.);vec3 diffuseColor=vDiffuseColor.rgb;float alpha=vDiffuseColor.a;\n#ifdef DIFFUSE\nbaseColor=texture2D(diffuseSampler,vDiffuseUV);\n#ifdef ALPHATEST\nif (baseColor.a<0.4)\ndiscard;\n#endif\n#include<depthPrePass>\nbaseColor.rgb*=vDiffuseInfos.y;\n#endif\n#ifdef VERTEXCOLOR\nbaseColor.rgb*=vColor.rgb;\n#endif\n#ifdef NORMAL\nvec3 normalW=normalize(vNormalW);\n#else\nvec3 normalW=vec3(1.0,1.0,1.0);\n#endif\nlightingInfo info;vec3 diffuseBase=vec3(0.,0.,0.);float shadow=1.;float glossiness=0.;float aggShadow=0.;float numLights=0.;\n#ifdef SPECULARTERM\nvec3 specularBase=vec3(0.,0.,0.);\n#endif \n#include<lightFragment>[0..maxSimultaneousLights]\n#if defined(VERTEXALPHA) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nalpha*=vColor.a;\n#endif\nvec3 finalDiffuse=clamp(diffuseBase*diffuseColor,0.0,1.0)*baseColor.rgb;vec4 color=vec4(finalDiffuse,alpha);\n#include<logDepthFragment>\n#include<fogFragment>\ngl_FragColor=color;\n#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}";u.ShaderStore.ShadersStore.cellVertexShader="precision highp float;attribute vec3 position;\n#ifdef NORMAL\nattribute vec3 normal;\n#endif\n#ifdef UV1\nattribute vec2 uv;\n#endif\n#ifdef UV2\nattribute vec2 uv2;\n#endif\n#ifdef VERTEXCOLOR\nattribute vec4 color;\n#endif\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\n#include<instancesDeclaration>\nuniform mat4 view;uniform mat4 viewProjection;\n#ifdef DIFFUSE\nvarying vec2 vDiffuseUV;uniform mat4 diffuseMatrix;uniform vec2 vDiffuseInfos;\n#endif\n#ifdef POINTSIZE\nuniform float pointSize;\n#endif\nvarying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\n#endif\n#include<clipPlaneVertexDeclaration>\n#include<logDepthDeclaration>\n#include<fogVertexDeclaration>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\nvec4 worldPos=finalWorld*vec4(position,1.0);gl_Position=viewProjection*worldPos;vPositionW=vec3(worldPos);\n#ifdef NORMAL\nvNormalW=normalize(vec3(finalWorld*vec4(normal,0.0)));\n#endif\n#ifndef UV1\nvec2 uv=vec2(0.,0.);\n#endif\n#ifndef UV2\nvec2 uv2=vec2(0.,0.);\n#endif\n#ifdef DIFFUSE\nif (vDiffuseInfos.x==0.)\n{vDiffuseUV=vec2(diffuseMatrix*vec4(uv,1.0,0.0));}\nelse\n{vDiffuseUV=vec2(diffuseMatrix*vec4(uv2,1.0,0.0));}\n#endif\n#include<clipPlaneVertex>\n#include<fogVertex>\n#include<shadowsVertex>[0..maxSimultaneousLights]\n#include<vertexColorMixing>\n#if defined(POINTSIZE) && !defined(WEBGPU)\ngl_PointSize=pointSize;\n#endif\n#include<logDepthVertex>\n#define CUSTOM_VERTEX_MAIN_END\n}\n";var d=function(e){function i(){var i=e.call(this)||this;return i.DIFFUSE=!1,i.CLIPPLANE=!1,i.CLIPPLANE2=!1,i.CLIPPLANE3=!1,i.CLIPPLANE4=!1,i.CLIPPLANE5=!1,i.CLIPPLANE6=!1,i.ALPHATEST=!1,i.POINTSIZE=!1,i.FOG=!1,i.NORMAL=!1,i.UV1=!1,i.UV2=!1,i.VERTEXCOLOR=!1,i.VERTEXALPHA=!1,i.NUM_BONE_INFLUENCERS=0,i.BonesPerMesh=0,i.INSTANCES=!1,i.INSTANCESCOLOR=!1,i.NDOTL=!0,i.CUSTOMUSERLIGHTING=!0,i.CELLBASIC=!0,i.DEPTHPREPASS=!1,i.IMAGEPROCESSINGPOSTPROCESS=!1,i.SKIPFINALCOLORCLAMP=!1,i.LOGARITHMICDEPTH=!1,i.rebuild(),i}return f(i,e),i}(u.MaterialDefines),c=function(e){function i(i,t){var n=e.call(this,i,t)||this;return n.diffuseColor=new u.Color3(1,1,1),n._computeHighLevel=!1,n._disableLighting=!1,n._maxSimultaneousLights=4,n}return f(i,e),i.prototype.needAlphaBlending=function(){return this.alpha<1},i.prototype.needAlphaTesting=function(){return!1},i.prototype.getAlphaTestTexture=function(){return null},i.prototype.isReadyForSubMesh=function(e,i,t){var n=i._drawWrapper;if(this.isFrozen&&n.effect&&n._wasPreviouslyReady&&n._wasPreviouslyUsingInstances===t)return!0;i.materialDefines||(i.materialDefines=new d);var r=i.materialDefines,o=this.getScene();if(this._isReadyForSubMesh(i))return!0;var s=o.getEngine();if(r._areTexturesDirty&&(r._needUVs=!1,o.texturesEnabled&&this._diffuseTexture&&u.MaterialFlags.DiffuseTextureEnabled)){if(!this._diffuseTexture.isReady())return!1;r._needUVs=!0,r.DIFFUSE=!0}if(r.CELLBASIC=!this.computeHighLevel,(0,u.PrepareDefinesForMisc)(e,o,this._useLogarithmicDepth,this.pointsCloud,this.fogEnabled,this._shouldTurnAlphaTestOn(e),r),r._needNormals=(0,u.PrepareDefinesForLights)(o,e,r,!1,this._maxSimultaneousLights,this._disableLighting),(0,u.PrepareDefinesForFrameBoundValues)(o,s,this,r,!!t),(0,u.PrepareDefinesForAttributes)(e,r,!0,!0),r.isDirty){r.markAsProcessed(),o.resetCachedMaterial();var a=new u.EffectFallbacks;r.FOG&&a.addFallback(1,"FOG"),(0,u.HandleFallbacksForShadows)(r,a,this.maxSimultaneousLights),r.NUM_BONE_INFLUENCERS>0&&a.addCPUSkinningFallback(0,e),r.IMAGEPROCESSINGPOSTPROCESS=o.imageProcessingConfiguration.applyByPostProcess;var f=[u.VertexBuffer.PositionKind];r.NORMAL&&f.push(u.VertexBuffer.NormalKind),r.UV1&&f.push(u.VertexBuffer.UVKind),r.UV2&&f.push(u.VertexBuffer.UV2Kind),r.VERTEXCOLOR&&f.push(u.VertexBuffer.ColorKind),(0,u.PrepareAttributesForBones)(f,e,r,a),(0,u.PrepareAttributesForInstances)(f,r);var l=r.toString(),c=["world","view","viewProjection","vEyePosition","vLightsType","vDiffuseColor","vFogInfos","vFogColor","pointSize","vDiffuseInfos","mBones","diffuseMatrix","logarithmicDepthConstant"],h=["diffuseSampler"],p=[];(0,u.addClipPlaneUniforms)(c),(0,u.PrepareUniformsAndSamplersList)({uniformsNames:c,uniformBuffersNames:p,samplers:h,defines:r,maxSimultaneousLights:this.maxSimultaneousLights}),i.setEffect(o.getEngine().createEffect("cell",{attributes:f,uniformsNames:c,uniformBuffersNames:p,samplers:h,defines:l,fallbacks:a,onCompiled:this.onCompiled,onError:this.onError,indexParameters:{maxSimultaneousLights:this.maxSimultaneousLights-1}},s),r,this._materialContext)}return!(!i.effect||!i.effect.isReady()||(r._renderId=o.getRenderId(),n._wasPreviouslyReady=!0,n._wasPreviouslyUsingInstances=!!t,0))},i.prototype.bindForSubMesh=function(e,i,t){var n=this.getScene(),r=t.materialDefines;if(r){var o=t.effect;o&&(this._activeEffect=o,this.bindOnlyWorldMatrix(e),this._activeEffect.setMatrix("viewProjection",n.getTransformMatrix()),(0,u.BindBonesParameters)(i,this._activeEffect),this._mustRebind(n,o,t)&&(this._diffuseTexture&&u.MaterialFlags.DiffuseTextureEnabled&&(this._activeEffect.setTexture("diffuseSampler",this._diffuseTexture),this._activeEffect.setFloat2("vDiffuseInfos",this._diffuseTexture.coordinatesIndex,this._diffuseTexture.level),this._activeEffect.setMatrix("diffuseMatrix",this._diffuseTexture.getTextureMatrix())),(0,u.bindClipPlane)(this._activeEffect,this,n),this.pointsCloud&&this._activeEffect.setFloat("pointSize",this.pointSize),this._useLogarithmicDepth&&(0,u.BindLogDepth)(r,o,n),n.bindEyePosition(o)),this._activeEffect.setColor4("vDiffuseColor",this.diffuseColor,this.alpha*i.visibility),n.lightsEnabled&&!this.disableLighting&&(0,u.BindLights)(n,i,this._activeEffect,r,this._maxSimultaneousLights),n.fogEnabled&&i.applyFog&&n.fogMode!==u.Scene.FOGMODE_NONE&&this._activeEffect.setMatrix("view",n.getViewMatrix()),(0,u.BindFogParameters)(n,i,this._activeEffect),this._afterBind(i,this._activeEffect,t))}},i.prototype.getAnimatables=function(){var e=[];return this._diffuseTexture&&this._diffuseTexture.animations&&this._diffuseTexture.animations.length>0&&e.push(this._diffuseTexture),e},i.prototype.getActiveTextures=function(){var i=e.prototype.getActiveTextures.call(this);return this._diffuseTexture&&i.push(this._diffuseTexture),i},i.prototype.hasTexture=function(i){return!!e.prototype.hasTexture.call(this,i)||this._diffuseTexture===i},i.prototype.dispose=function(i){this._diffuseTexture&&this._diffuseTexture.dispose(),e.prototype.dispose.call(this,i)},i.prototype.getClassName=function(){return"CellMaterial"},i.prototype.clone=function(e){var t=this;return u.SerializationHelper.Clone((function(){return new i(e,t.getScene())}),this)},i.prototype.serialize=function(){var i=e.prototype.serialize.call(this);return i.customType="BABYLON.CellMaterial",i},i.Parse=function(e,t,n){return u.SerializationHelper.Parse((function(){return new i(e.name,t)}),e,t,n)},l([(0,u.serializeAsTexture)("diffuseTexture")],i.prototype,"_diffuseTexture",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture",void 0),l([(0,u.serializeAsColor3)("diffuse")],i.prototype,"diffuseColor",void 0),l([(0,u.serialize)("computeHighLevel")],i.prototype,"_computeHighLevel",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"computeHighLevel",void 0),l([(0,u.serialize)("disableLighting")],i.prototype,"_disableLighting",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"disableLighting",void 0),l([(0,u.serialize)("maxSimultaneousLights")],i.prototype,"_maxSimultaneousLights",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"maxSimultaneousLights",void 0),i}(u.PushMaterial);(0,u.RegisterClass)("BABYLON.CellMaterial",c);var h=function(){},p=function(){},m=function(e){function i(t,n){var r=e.call(this,t,n,!0)||this;return r.CustomParts=new p,r.customShaderNameResolve=r.Builder,r.FragmentShader=u.Effect.ShadersStore.defaultPixelShader,r.VertexShader=u.Effect.ShadersStore.defaultVertexShader,i.ShaderIndexer++,r._createdShaderName="custom_"+i.ShaderIndexer,r}return f(i,e),i.prototype.AttachAfterBind=function(e,i){if(this._newUniformInstances)for(var t in this._newUniformInstances)"vec2"==(n=t.toString().split("-"))[0]?i.setVector2(n[1],this._newUniformInstances[t]):"vec3"==n[0]?this._newUniformInstances[t]instanceof u.Color3?i.setColor3(n[1],this._newUniformInstances[t]):i.setVector3(n[1],this._newUniformInstances[t]):"vec4"==n[0]?(this._newUniformInstances[t]instanceof u.Color4?i.setDirectColor4(n[1],this._newUniformInstances[t]):i.setVector4(n[1],this._newUniformInstances[t]),i.setVector4(n[1],this._newUniformInstances[t])):"mat4"==n[0]?i.setMatrix(n[1],this._newUniformInstances[t]):"float"==n[0]&&i.setFloat(n[1],this._newUniformInstances[t]);if(this._newSamplerInstances)for(var t in this._newSamplerInstances){var n;"sampler2D"==(n=t.toString().split("-"))[0]&&this._newSamplerInstances[t].isReady&&this._newSamplerInstances[t].isReady()&&i.setTexture(n[1],this._newSamplerInstances[t])}},i.prototype.ReviewUniform=function(e,i){if("uniform"==e&&this._newUniforms)for(var t=0;t<this._newUniforms.length;t++)-1==this._customUniform[t].indexOf("sampler")&&i.push(this._newUniforms[t].replace(/\[\d*\]/g,""));if("sampler"==e&&this._newUniforms)for(t=0;t<this._newUniforms.length;t++)-1!=this._customUniform[t].indexOf("sampler")&&i.push(this._newUniforms[t].replace(/\[\d*\]/g,""));return i},i.prototype.Builder=function(e,i,t,n,r,o){o&&this._customAttributes&&this._customAttributes.length>0&&o.push.apply(o,this._customAttributes),this.ReviewUniform("uniform",i),this.ReviewUniform("sampler",n);var s=this._createdShaderName;return u.Effect.ShadersStore[s+"VertexShader"]&&u.Effect.ShadersStore[s+"PixelShader"]||(u.Effect.ShadersStore[s+"VertexShader"]=this._injectCustomCode(this.VertexShader,"vertex"),u.Effect.ShadersStore[s+"PixelShader"]=this._injectCustomCode(this.FragmentShader,"fragment")),s},i.prototype._injectCustomCode=function(e,i){var t=this._getCustomCode(i);for(var n in t){var r=t[n];if(r&&r.length>0){var o="#define "+n;e=e.replace(o,"\n"+r+"\n"+o)}}return e},i.prototype._getCustomCode=function(e){var i,t;return"vertex"===e?{CUSTOM_VERTEX_BEGIN:this.CustomParts.Vertex_Begin,CUSTOM_VERTEX_DEFINITIONS:((null===(i=this._customUniform)||void 0===i?void 0:i.join("\n"))||"")+(this.CustomParts.Vertex_Definitions||""),CUSTOM_VERTEX_MAIN_BEGIN:this.CustomParts.Vertex_MainBegin,CUSTOM_VERTEX_UPDATE_POSITION:this.CustomParts.Vertex_Before_PositionUpdated,CUSTOM_VERTEX_UPDATE_NORMAL:this.CustomParts.Vertex_Before_NormalUpdated,CUSTOM_VERTEX_MAIN_END:this.CustomParts.Vertex_MainEnd,CUSTOM_VERTEX_UPDATE_WORLDPOS:this.CustomParts.Vertex_After_WorldPosComputed}:{CUSTOM_FRAGMENT_BEGIN:this.CustomParts.Fragment_Begin,CUSTOM_FRAGMENT_DEFINITIONS:((null===(t=this._customUniform)||void 0===t?void 0:t.join("\n"))||"")+(this.CustomParts.Fragment_Definitions||""),CUSTOM_FRAGMENT_MAIN_BEGIN:this.CustomParts.Fragment_MainBegin,CUSTOM_FRAGMENT_UPDATE_DIFFUSE:this.CustomParts.Fragment_Custom_Diffuse,CUSTOM_FRAGMENT_UPDATE_ALPHA:this.CustomParts.Fragment_Custom_Alpha,CUSTOM_FRAGMENT_BEFORE_LIGHTS:this.CustomParts.Fragment_Before_Lights,CUSTOM_FRAGMENT_BEFORE_FRAGCOLOR:this.CustomParts.Fragment_Before_FragColor,CUSTOM_FRAGMENT_MAIN_END:this.CustomParts.Fragment_MainEnd,CUSTOM_FRAGMENT_BEFORE_FOG:this.CustomParts.Fragment_Before_Fog}},i.prototype._afterBind=function(i,t,n){if(void 0===t&&(t=null),t){this.AttachAfterBind(i,t);try{e.prototype._afterBind.call(this,i,t,n)}catch(e){}}},i.prototype.AddUniform=function(e,i,t){return this._customUniform||(this._customUniform=new Array,this._newUniforms=new Array,this._newSamplerInstances={},this._newUniformInstances={}),t&&(-1!=i.indexOf("sampler")?this._newSamplerInstances[i+"-"+e]=t:this._newUniformInstances[i+"-"+e]=t),this._customUniform.push("uniform "+i+" "+e+";"),this._newUniforms.push(e),this},i.prototype.AddAttribute=function(e){return this._customAttributes||(this._customAttributes=[]),this._customAttributes.push(e),this},i.prototype.Fragment_Begin=function(e){return this.CustomParts.Fragment_Begin=e,this},i.prototype.Fragment_Definitions=function(e){return this.CustomParts.Fragment_Definitions=e,this},i.prototype.Fragment_MainBegin=function(e){return this.CustomParts.Fragment_MainBegin=e,this},i.prototype.Fragment_MainEnd=function(e){return this.CustomParts.Fragment_MainEnd=e,this},i.prototype.Fragment_Custom_Diffuse=function(e){return this.CustomParts.Fragment_Custom_Diffuse=e.replace("result","diffuseColor"),this},i.prototype.Fragment_Custom_Alpha=function(e){return this.CustomParts.Fragment_Custom_Alpha=e.replace("result","alpha"),this},i.prototype.Fragment_Before_Lights=function(e){return this.CustomParts.Fragment_Before_Lights=e,this},i.prototype.Fragment_Before_Fog=function(e){return this.CustomParts.Fragment_Before_Fog=e,this},i.prototype.Fragment_Before_FragColor=function(e){return this.CustomParts.Fragment_Before_FragColor=e.replace("result","color"),this},i.prototype.Vertex_Begin=function(e){return this.CustomParts.Vertex_Begin=e,this},i.prototype.Vertex_Definitions=function(e){return this.CustomParts.Vertex_Definitions=e,this},i.prototype.Vertex_MainBegin=function(e){return this.CustomParts.Vertex_MainBegin=e,this},i.prototype.Vertex_Before_PositionUpdated=function(e){return this.CustomParts.Vertex_Before_PositionUpdated=e.replace("result","positionUpdated"),this},i.prototype.Vertex_Before_NormalUpdated=function(e){return this.CustomParts.Vertex_Before_NormalUpdated=e.replace("result","normalUpdated"),this},i.prototype.Vertex_After_WorldPosComputed=function(e){return this.CustomParts.Vertex_After_WorldPosComputed=e,this},i.prototype.Vertex_MainEnd=function(e){return this.CustomParts.Vertex_MainEnd=e,this},i.ShaderIndexer=1,i}(u.StandardMaterial);(0,u.RegisterClass)("BABYLON.CustomMaterial",m);var v=function(){},g=v,T=function(e){function i(t,n){var r=e.call(this,t,n,!0)||this;return r.CustomParts=new v,r.customShaderNameResolve=r.Builder,r.FragmentShader=u.Effect.ShadersStore.pbrPixelShader,r.VertexShader=u.Effect.ShadersStore.pbrVertexShader,r.FragmentShader=r.FragmentShader.replace(/#include<pbrBlockAlbedoOpacity>/g,u.Effect.IncludesShadersStore.pbrBlockAlbedoOpacity),r.FragmentShader=r.FragmentShader.replace(/#include<pbrBlockReflectivity>/g,u.Effect.IncludesShadersStore.pbrBlockReflectivity),r.FragmentShader=r.FragmentShader.replace(/#include<pbrBlockFinalColorComposition>/g,u.Effect.IncludesShadersStore.pbrBlockFinalColorComposition),i.ShaderIndexer++,r._createdShaderName="custompbr_"+i.ShaderIndexer,r}return f(i,e),i.prototype.AttachAfterBind=function(e,i){if(this._newUniformInstances)for(var t in this._newUniformInstances)"vec2"==(n=t.toString().split("-"))[0]?i.setVector2(n[1],this._newUniformInstances[t]):"vec3"==n[0]?this._newUniformInstances[t]instanceof u.Color3?i.setColor3(n[1],this._newUniformInstances[t]):i.setVector3(n[1],this._newUniformInstances[t]):"vec4"==n[0]?(this._newUniformInstances[t]instanceof u.Color4?i.setDirectColor4(n[1],this._newUniformInstances[t]):i.setVector4(n[1],this._newUniformInstances[t]),i.setVector4(n[1],this._newUniformInstances[t])):"mat4"==n[0]?i.setMatrix(n[1],this._newUniformInstances[t]):"float"==n[0]&&i.setFloat(n[1],this._newUniformInstances[t]);if(this._newSamplerInstances)for(var t in this._newSamplerInstances){var n;"sampler2D"==(n=t.toString().split("-"))[0]&&this._newSamplerInstances[t].isReady&&this._newSamplerInstances[t].isReady()&&i.setTexture(n[1],this._newSamplerInstances[t])}},i.prototype.ReviewUniform=function(e,i){if("uniform"==e&&this._newUniforms)for(var t=0;t<this._newUniforms.length;t++)-1==this._customUniform[t].indexOf("sampler")&&i.push(this._newUniforms[t].replace(/\[\d*\]/g,""));if("sampler"==e&&this._newUniforms)for(t=0;t<this._newUniforms.length;t++)-1!=this._customUniform[t].indexOf("sampler")&&i.push(this._newUniforms[t].replace(/\[\d*\]/g,""));return i},i.prototype.Builder=function(e,i,t,n,r,o,s){if(s){var a=s.processFinalCode;s.processFinalCode=function(e,i){if("vertex"===e)return a?a(e,i):i;var t=new u.ShaderCodeInliner(i);return t.inlineToken="#define pbr_inline",t.processCode(),a?a(e,t.code):t.code}}o&&this._customAttributes&&this._customAttributes.length>0&&o.push.apply(o,this._customAttributes),this.ReviewUniform("uniform",i),this.ReviewUniform("sampler",n);var f=this._createdShaderName;return u.Effect.ShadersStore[f+"VertexShader"]&&u.Effect.ShadersStore[f+"PixelShader"]||(u.Effect.ShadersStore[f+"VertexShader"]=this._injectCustomCode(this.VertexShader,"vertex"),u.Effect.ShadersStore[f+"PixelShader"]=this._injectCustomCode(this.FragmentShader,"fragment")),f},i.prototype._injectCustomCode=function(e,i){var t=this._getCustomCode(i);for(var n in t){var r=t[n];if(r&&r.length>0){var o="#define "+n;e=e.replace(o,"\n"+r+"\n"+o)}}return e},i.prototype._getCustomCode=function(e){var i,t;return"vertex"===e?{CUSTOM_VERTEX_BEGIN:this.CustomParts.Vertex_Begin,CUSTOM_VERTEX_DEFINITIONS:((null===(i=this._customUniform)||void 0===i?void 0:i.join("\n"))||"")+(this.CustomParts.Vertex_Definitions||""),CUSTOM_VERTEX_MAIN_BEGIN:this.CustomParts.Vertex_MainBegin,CUSTOM_VERTEX_UPDATE_POSITION:this.CustomParts.Vertex_Before_PositionUpdated,CUSTOM_VERTEX_UPDATE_NORMAL:this.CustomParts.Vertex_Before_NormalUpdated,CUSTOM_VERTEX_MAIN_END:this.CustomParts.Vertex_MainEnd,CUSTOM_VERTEX_UPDATE_WORLDPOS:this.CustomParts.Vertex_After_WorldPosComputed}:{CUSTOM_FRAGMENT_BEGIN:this.CustomParts.Fragment_Begin,CUSTOM_FRAGMENT_MAIN_BEGIN:this.CustomParts.Fragment_MainBegin,CUSTOM_FRAGMENT_DEFINITIONS:((null===(t=this._customUniform)||void 0===t?void 0:t.join("\n"))||"")+(this.CustomParts.Fragment_Definitions||""),CUSTOM_FRAGMENT_UPDATE_ALBEDO:this.CustomParts.Fragment_Custom_Albedo,CUSTOM_FRAGMENT_UPDATE_ALPHA:this.CustomParts.Fragment_Custom_Alpha,CUSTOM_FRAGMENT_BEFORE_LIGHTS:this.CustomParts.Fragment_Before_Lights,CUSTOM_FRAGMENT_UPDATE_METALLICROUGHNESS:this.CustomParts.Fragment_Custom_MetallicRoughness,CUSTOM_FRAGMENT_UPDATE_MICROSURFACE:this.CustomParts.Fragment_Custom_MicroSurface,CUSTOM_FRAGMENT_BEFORE_FINALCOLORCOMPOSITION:this.CustomParts.Fragment_Before_FinalColorComposition,CUSTOM_FRAGMENT_BEFORE_FRAGCOLOR:this.CustomParts.Fragment_Before_FragColor,CUSTOM_FRAGMENT_MAIN_END:this.CustomParts.Fragment_MainEnd,CUSTOM_FRAGMENT_BEFORE_FOG:this.CustomParts.Fragment_Before_Fog}},i.prototype._afterBind=function(i,t,n){if(void 0===t&&(t=null),t){this.AttachAfterBind(i,t);try{e.prototype._afterBind.call(this,i,t,n)}catch(e){}}},i.prototype.AddUniform=function(e,i,t){return this._customUniform||(this._customUniform=new Array,this._newUniforms=new Array,this._newSamplerInstances={},this._newUniformInstances={}),t&&(-1!=i.indexOf("sampler")?this._newSamplerInstances[i+"-"+e]=t:this._newUniformInstances[i+"-"+e]=t),this._customUniform.push("uniform "+i+" "+e+";"),this._newUniforms.push(e),this},i.prototype.AddAttribute=function(e){return this._customAttributes||(this._customAttributes=[]),this._customAttributes.push(e),this},i.prototype.Fragment_Begin=function(e){return this.CustomParts.Fragment_Begin=e,this},i.prototype.Fragment_Definitions=function(e){return this.CustomParts.Fragment_Definitions=e,this},i.prototype.Fragment_MainBegin=function(e){return this.CustomParts.Fragment_MainBegin=e,this},i.prototype.Fragment_Custom_Albedo=function(e){return this.CustomParts.Fragment_Custom_Albedo=e.replace("result","surfaceAlbedo"),this},i.prototype.Fragment_Custom_Alpha=function(e){return this.CustomParts.Fragment_Custom_Alpha=e.replace("result","alpha"),this},i.prototype.Fragment_Before_Lights=function(e){return this.CustomParts.Fragment_Before_Lights=e,this},i.prototype.Fragment_Custom_MetallicRoughness=function(e){return this.CustomParts.Fragment_Custom_MetallicRoughness=e,this},i.prototype.Fragment_Custom_MicroSurface=function(e){return this.CustomParts.Fragment_Custom_MicroSurface=e,this},i.prototype.Fragment_Before_Fog=function(e){return this.CustomParts.Fragment_Before_Fog=e,this},i.prototype.Fragment_Before_FinalColorComposition=function(e){return this.CustomParts.Fragment_Before_FinalColorComposition=e,this},i.prototype.Fragment_Before_FragColor=function(e){return this.CustomParts.Fragment_Before_FragColor=e.replace("result","color"),this},i.prototype.Fragment_MainEnd=function(e){return this.CustomParts.Fragment_MainEnd=e,this},i.prototype.Vertex_Begin=function(e){return this.CustomParts.Vertex_Begin=e,this},i.prototype.Vertex_Definitions=function(e){return this.CustomParts.Vertex_Definitions=e,this},i.prototype.Vertex_MainBegin=function(e){return this.CustomParts.Vertex_MainBegin=e,this},i.prototype.Vertex_Before_PositionUpdated=function(e){return this.CustomParts.Vertex_Before_PositionUpdated=e.replace("result","positionUpdated"),this},i.prototype.Vertex_Before_NormalUpdated=function(e){return this.CustomParts.Vertex_Before_NormalUpdated=e.replace("result","normalUpdated"),this},i.prototype.Vertex_After_WorldPosComputed=function(e){return this.CustomParts.Vertex_After_WorldPosComputed=e,this},i.prototype.Vertex_MainEnd=function(e){return this.CustomParts.Vertex_MainEnd=e,this},i.ShaderIndexer=1,i}(u.PBRMaterial);(0,u.RegisterClass)("BABYLON.PBRCustomMaterial",T);u.ShaderStore.ShadersStore.firePixelShader="precision highp float;uniform vec4 vEyePosition;varying vec3 vPositionW;\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\n#endif\n#ifdef DIFFUSE\nvarying vec2 vDiffuseUV;uniform sampler2D diffuseSampler;uniform vec2 vDiffuseInfos;\n#endif\nuniform sampler2D distortionSampler;uniform sampler2D opacitySampler;\n#ifdef DIFFUSE\nvarying vec2 vDistortionCoords1;varying vec2 vDistortionCoords2;varying vec2 vDistortionCoords3;\n#endif\n#include<clipPlaneFragmentDeclaration>\n#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\n#include<logDepthDeclaration>\n#include<fogFragmentDeclaration>\nvec4 bx2(vec4 x)\n{return vec4(2.0)*x-vec4(1.0);}\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);vec4 baseColor=vec4(1.,1.,1.,1.);float alpha=1.0;\n#ifdef DIFFUSE\nconst float distortionAmount0 =0.092;const float distortionAmount1 =0.092;const float distortionAmount2 =0.092;vec2 heightAttenuation=vec2(0.3,0.39);vec4 noise0=texture2D(distortionSampler,vDistortionCoords1);vec4 noise1=texture2D(distortionSampler,vDistortionCoords2);vec4 noise2=texture2D(distortionSampler,vDistortionCoords3);vec4 noiseSum=bx2(noise0)*distortionAmount0+bx2(noise1)*distortionAmount1+bx2(noise2)*distortionAmount2;vec4 perturbedBaseCoords=vec4(vDiffuseUV,0.0,1.0)+noiseSum*(vDiffuseUV.y*heightAttenuation.x+heightAttenuation.y);vec4 opacityColor=texture2D(opacitySampler,perturbedBaseCoords.xy);\n#ifdef ALPHATEST\nif (opacityColor.r<0.1)\ndiscard;\n#endif\n#include<depthPrePass>\nbaseColor=texture2D(diffuseSampler,perturbedBaseCoords.xy)*2.0;baseColor*=opacityColor;baseColor.rgb*=vDiffuseInfos.y;\n#endif\n#ifdef VERTEXCOLOR\nbaseColor.rgb*=vColor.rgb;\n#endif\nvec3 diffuseBase=vec3(1.0,1.0,1.0);\n#if defined(VERTEXALPHA) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nalpha*=vColor.a;\n#endif\nvec4 color=vec4(baseColor.rgb,alpha);\n#include<logDepthFragment>\n#include<fogFragment>\ngl_FragColor=color;\n#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}";u.ShaderStore.ShadersStore.fireVertexShader="precision highp float;attribute vec3 position;\n#ifdef UV1\nattribute vec2 uv;\n#endif\n#ifdef UV2\nattribute vec2 uv2;\n#endif\n#ifdef VERTEXCOLOR\nattribute vec4 color;\n#endif\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\n#include<instancesDeclaration>\nuniform mat4 view;uniform mat4 viewProjection;\n#ifdef DIFFUSE\nvarying vec2 vDiffuseUV;\n#endif\n#ifdef POINTSIZE\nuniform float pointSize;\n#endif\nvarying vec3 vPositionW;\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\n#endif\n#include<clipPlaneVertexDeclaration>\n#include<logDepthDeclaration>\n#include<fogVertexDeclaration>\nuniform float time;uniform float speed;\n#ifdef DIFFUSE\nvarying vec2 vDistortionCoords1;varying vec2 vDistortionCoords2;varying vec2 vDistortionCoords3;\n#endif\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\nvec4 worldPos=finalWorld*vec4(position,1.0);gl_Position=viewProjection*worldPos;vPositionW=vec3(worldPos);\n#ifdef DIFFUSE\nvDiffuseUV=uv;vDiffuseUV.y-=0.2;\n#endif\n#include<clipPlaneVertex>\n#include<logDepthVertex>\n#include<fogVertex>\n#include<vertexColorMixing>\n#if defined(POINTSIZE) && !defined(WEBGPU)\ngl_PointSize=pointSize;\n#endif\n#ifdef DIFFUSE\nvec3 layerSpeed=vec3(-0.2,-0.52,-0.1)*speed;vDistortionCoords1.x=uv.x;vDistortionCoords1.y=uv.y+layerSpeed.x*time/1000.0;vDistortionCoords2.x=uv.x;vDistortionCoords2.y=uv.y+layerSpeed.y*time/1000.0;vDistortionCoords3.x=uv.x;vDistortionCoords3.y=uv.y+layerSpeed.z*time/1000.0;\n#endif\n#define CUSTOM_VERTEX_MAIN_END\n}\n";var _=function(e){function i(){var i=e.call(this)||this;return i.DIFFUSE=!1,i.CLIPPLANE=!1,i.CLIPPLANE2=!1,i.CLIPPLANE3=!1,i.CLIPPLANE4=!1,i.CLIPPLANE5=!1,i.CLIPPLANE6=!1,i.ALPHATEST=!1,i.DEPTHPREPASS=!1,i.POINTSIZE=!1,i.FOG=!1,i.UV1=!1,i.VERTEXCOLOR=!1,i.VERTEXALPHA=!1,i.BonesPerMesh=0,i.NUM_BONE_INFLUENCERS=0,i.INSTANCES=!1,i.INSTANCESCOLOR=!1,i.IMAGEPROCESSINGPOSTPROCESS=!1,i.SKIPFINALCOLORCLAMP=!1,i.LOGARITHMICDEPTH=!1,i.rebuild(),i}return f(i,e),i}(u.MaterialDefines),x=function(e){function i(i,t){var n=e.call(this,i,t)||this;return n.diffuseColor=new u.Color3(1,1,1),n.speed=1,n._scaledDiffuse=new u.Color3,n._lastTime=0,n}return f(i,e),i.prototype.needAlphaBlending=function(){return!1},i.prototype.needAlphaTesting=function(){return!0},i.prototype.getAlphaTestTexture=function(){return null},i.prototype.isReadyForSubMesh=function(e,i,t){var n=i._drawWrapper;if(this.isFrozen&&n._wasPreviouslyReady&&n._wasPreviouslyUsingInstances===t)return!0;i.materialDefines||(i.materialDefines=new _);var r=i.materialDefines,o=this.getScene();if(this._isReadyForSubMesh(i))return!0;var s=o.getEngine();if(r._areTexturesDirty&&(r._needUVs=!1,this._diffuseTexture&&u.MaterialFlags.DiffuseTextureEnabled)){if(!this._diffuseTexture.isReady())return!1;r._needUVs=!0,r.DIFFUSE=!0}if(r.ALPHATEST=!!this._opacityTexture,r._areMiscDirty&&(r.POINTSIZE=this.pointsCloud||o.forcePointsCloud,r.FOG=o.fogEnabled&&e.applyFog&&o.fogMode!==u.Scene.FOGMODE_NONE&&this.fogEnabled,r.LOGARITHMICDEPTH=this._useLogarithmicDepth),(0,u.PrepareDefinesForFrameBoundValues)(o,s,this,r,!!t),(0,u.PrepareDefinesForAttributes)(e,r,!1,!0),r.isDirty){r.markAsProcessed(),o.resetCachedMaterial();var a=new u.EffectFallbacks;r.FOG&&a.addFallback(1,"FOG"),r.NUM_BONE_INFLUENCERS>0&&a.addCPUSkinningFallback(0,e),r.IMAGEPROCESSINGPOSTPROCESS=o.imageProcessingConfiguration.applyByPostProcess;var f=[u.VertexBuffer.PositionKind];r.UV1&&f.push(u.VertexBuffer.UVKind),r.VERTEXCOLOR&&f.push(u.VertexBuffer.ColorKind),(0,u.PrepareAttributesForBones)(f,e,r,a),(0,u.PrepareAttributesForInstances)(f,r);var l=["world","view","viewProjection","vEyePosition","vFogInfos","vFogColor","pointSize","vDiffuseInfos","mBones","diffuseMatrix","logarithmicDepthConstant","time","speed"];(0,u.addClipPlaneUniforms)(l);var d=r.toString();i.setEffect(o.getEngine().createEffect("fire",{attributes:f,uniformsNames:l,uniformBuffersNames:[],samplers:["diffuseSampler","distortionSampler","opacitySampler"],defines:d,fallbacks:a,onCompiled:this.onCompiled,onError:this.onError,indexParameters:null,maxSimultaneousLights:4,transformFeedbackVaryings:null},s),r,this._materialContext)}return!(!i.effect||!i.effect.isReady()||(r._renderId=o.getRenderId(),n._wasPreviouslyReady=!0,n._wasPreviouslyUsingInstances=!!t,0))},i.prototype.bindForSubMesh=function(e,i,t){var n=this.getScene(),r=t.materialDefines;if(r){var o=t.effect;o&&(this._activeEffect=o,this.bindOnlyWorldMatrix(e),this._activeEffect.setMatrix("viewProjection",n.getTransformMatrix()),(0,u.BindBonesParameters)(i,this._activeEffect),this._mustRebind(n,o,t)&&(this._diffuseTexture&&u.MaterialFlags.DiffuseTextureEnabled&&(this._activeEffect.setTexture("diffuseSampler",this._diffuseTexture),this._activeEffect.setFloat2("vDiffuseInfos",this._diffuseTexture.coordinatesIndex,this._diffuseTexture.level),this._activeEffect.setMatrix("diffuseMatrix",this._diffuseTexture.getTextureMatrix()),this._activeEffect.setTexture("distortionSampler",this._distortionTexture),this._activeEffect.setTexture("opacitySampler",this._opacityTexture)),(0,u.bindClipPlane)(this._activeEffect,this,n),this.pointsCloud&&this._activeEffect.setFloat("pointSize",this.pointSize),this._useLogarithmicDepth&&(0,u.BindLogDepth)(r,o,n),n.bindEyePosition(o)),this._activeEffect.setColor4("vDiffuseColor",this._scaledDiffuse,this.alpha*i.visibility),n.fogEnabled&&i.applyFog&&n.fogMode!==u.Scene.FOGMODE_NONE&&this._activeEffect.setMatrix("view",n.getViewMatrix()),(0,u.BindFogParameters)(n,i,this._activeEffect),this._lastTime+=n.getEngine().getDeltaTime(),this._activeEffect.setFloat("time",this._lastTime),this._activeEffect.setFloat("speed",this.speed),this._afterBind(i,this._activeEffect,t))}},i.prototype.getAnimatables=function(){var e=[];return this._diffuseTexture&&this._diffuseTexture.animations&&this._diffuseTexture.animations.length>0&&e.push(this._diffuseTexture),this._distortionTexture&&this._distortionTexture.animations&&this._distortionTexture.animations.length>0&&e.push(this._distortionTexture),this._opacityTexture&&this._opacityTexture.animations&&this._opacityTexture.animations.length>0&&e.push(this._opacityTexture),e},i.prototype.getActiveTextures=function(){var i=e.prototype.getActiveTextures.call(this);return this._diffuseTexture&&i.push(this._diffuseTexture),this._distortionTexture&&i.push(this._distortionTexture),this._opacityTexture&&i.push(this._opacityTexture),i},i.prototype.hasTexture=function(i){return!!e.prototype.hasTexture.call(this,i)||this._diffuseTexture===i||this._distortionTexture===i||this._opacityTexture===i},i.prototype.getClassName=function(){return"FireMaterial"},i.prototype.dispose=function(i){this._diffuseTexture&&this._diffuseTexture.dispose(),this._distortionTexture&&this._distortionTexture.dispose(),e.prototype.dispose.call(this,i)},i.prototype.clone=function(e){var t=this;return u.SerializationHelper.Clone((function(){return new i(e,t.getScene())}),this)},i.prototype.serialize=function(){var i=e.prototype.serialize.call(this);return i.customType="BABYLON.FireMaterial",i.diffuseColor=this.diffuseColor.asArray(),i.speed=this.speed,this._diffuseTexture&&(i._diffuseTexture=this._diffuseTexture.serialize()),this._distortionTexture&&(i._distortionTexture=this._distortionTexture.serialize()),this._opacityTexture&&(i._opacityTexture=this._opacityTexture.serialize()),i},i.Parse=function(e,t,n){var r=new i(e.name,t);return r.diffuseColor=u.Color3.FromArray(e.diffuseColor),r.speed=e.speed,r.alpha=e.alpha,r.id=e.id,u.Tags.AddTagsTo(r,e.tags),r.backFaceCulling=e.backFaceCulling,r.wireframe=e.wireframe,e._diffuseTexture&&(r._diffuseTexture=u.Texture.Parse(e._diffuseTexture,t,n)),e._distortionTexture&&(r._distortionTexture=u.Texture.Parse(e._distortionTexture,t,n)),e._opacityTexture&&(r._opacityTexture=u.Texture.Parse(e._opacityTexture,t,n)),r},l([(0,u.serializeAsTexture)("diffuseTexture")],i.prototype,"_diffuseTexture",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture",void 0),l([(0,u.serializeAsTexture)("distortionTexture")],i.prototype,"_distortionTexture",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"distortionTexture",void 0),l([(0,u.serializeAsTexture)("opacityTexture")],i.prototype,"_opacityTexture",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"opacityTexture",void 0),l([(0,u.serializeAsColor3)("diffuse")],i.prototype,"diffuseColor",void 0),l([(0,u.serialize)()],i.prototype,"speed",void 0),i}(u.PushMaterial);(0,u.RegisterClass)("BABYLON.FireMaterial",x);u.ShaderStore.ShadersStore.furPixelShader="precision highp float;uniform vec4 vEyePosition;uniform vec4 vDiffuseColor;uniform vec4 furColor;uniform float furLength;varying vec3 vPositionW;varying float vfur_length;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\n#endif\n#include<helperFunctions>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#ifdef DIFFUSE\nvarying vec2 vDiffuseUV;uniform sampler2D diffuseSampler;uniform vec2 vDiffuseInfos;\n#endif\n#ifdef HIGHLEVEL\nuniform float furOffset;uniform float furOcclusion;uniform sampler2D furTexture;varying vec2 vFurUV;\n#endif\n#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\n#include<logDepthDeclaration>\n#include<lightsFragmentFunctions>\n#include<shadowsFragmentFunctions>\n#include<fogFragmentDeclaration>\n#include<clipPlaneFragmentDeclaration>\nfloat Rand(vec3 rv) {float x=dot(rv,vec3(12.9898,78.233,24.65487));return fract(sin(x)*43758.5453);}\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);vec4 baseColor=furColor;vec3 diffuseColor=vDiffuseColor.rgb;float alpha=vDiffuseColor.a;\n#ifdef DIFFUSE\nbaseColor*=texture2D(diffuseSampler,vDiffuseUV);\n#ifdef ALPHATEST\nif (baseColor.a<0.4)\ndiscard;\n#endif\n#include<depthPrePass>\nbaseColor.rgb*=vDiffuseInfos.y;\n#endif\n#ifdef VERTEXCOLOR\nbaseColor.rgb*=vColor.rgb;\n#endif\n#ifdef NORMAL\nvec3 normalW=normalize(vNormalW);\n#else\nvec3 normalW=vec3(1.0,1.0,1.0);\n#endif\n#ifdef HIGHLEVEL\nvec4 furTextureColor=texture2D(furTexture,vec2(vFurUV.x,vFurUV.y));if (furTextureColor.a<=0.0 || furTextureColor.g<furOffset) {discard;}\nfloat occlusion=mix(0.0,furTextureColor.b*1.2,furOffset);baseColor=vec4(baseColor.xyz*max(occlusion,furOcclusion),1.1-furOffset);\n#endif\nvec3 diffuseBase=vec3(0.,0.,0.);lightingInfo info;float shadow=1.;float glossiness=0.;float aggShadow=0.;float numLights=0.;\n#ifdef SPECULARTERM\nvec3 specularBase=vec3(0.,0.,0.);\n#endif\n#include<lightFragment>[0..maxSimultaneousLights]\n#if defined(VERTEXALPHA) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nalpha*=vColor.a;\n#endif\nvec3 finalDiffuse=clamp(diffuseBase.rgb*baseColor.rgb,0.0,1.0);\n#ifdef HIGHLEVEL\nvec4 color=vec4(finalDiffuse,alpha);\n#else\nfloat r=vfur_length/furLength*0.5;vec4 color=vec4(finalDiffuse*(0.5+r),alpha);\n#endif\n#include<logDepthFragment>\n#include<fogFragment>\ngl_FragColor=color;\n#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}";u.ShaderStore.ShadersStore.furVertexShader="precision highp float;attribute vec3 position;attribute vec3 normal;\n#ifdef UV1\nattribute vec2 uv;\n#endif\n#ifdef UV2\nattribute vec2 uv2;\n#endif\n#ifdef VERTEXCOLOR\nattribute vec4 color;\n#endif\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\nuniform float furLength;uniform float furAngle;\n#ifdef HIGHLEVEL\nuniform float furOffset;uniform vec3 furGravity;uniform float furTime;uniform float furSpacing;uniform float furDensity;\n#endif\n#ifdef HEIGHTMAP\nuniform sampler2D heightTexture;\n#endif\n#ifdef HIGHLEVEL\nvarying vec2 vFurUV;\n#endif\n#include<instancesDeclaration>\nuniform mat4 view;uniform mat4 viewProjection;\n#ifdef DIFFUSE\nvarying vec2 vDiffuseUV;uniform mat4 diffuseMatrix;uniform vec2 vDiffuseInfos;\n#endif\n#ifdef POINTSIZE\nuniform float pointSize;\n#endif\nvarying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\nvarying float vfur_length;\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\n#endif\n#include<clipPlaneVertexDeclaration>\n#include<logDepthDeclaration>\n#include<fogVertexDeclaration>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\nfloat Rand(vec3 rv) {float x=dot(rv,vec3(12.9898,78.233,24.65487));return fract(sin(x)*43758.5453);}\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\nfloat r=Rand(position);\n#ifdef HEIGHTMAP\n#if __VERSION__>100\nvfur_length=furLength*texture(heightTexture,uv).x;\n#else\nvfur_length=furLength*texture2D(heightTexture,uv).r;\n#endif\n#else \nvfur_length=(furLength*r);\n#endif\nvec3 tangent1=vec3(normal.y,-normal.x,0);vec3 tangent2=vec3(-normal.z,0,normal.x);r=Rand(tangent1*r);float J=(2.0+4.0*r);r=Rand(tangent2*r);float K=(2.0+2.0*r);tangent1=tangent1*J+tangent2*K;tangent1=normalize(tangent1);vec3 newPosition=position+normal*vfur_length*cos(furAngle)+tangent1*vfur_length*sin(furAngle);\n#ifdef HIGHLEVEL\nvec3 forceDirection=vec3(0.0,0.0,0.0);forceDirection.x=sin(furTime+position.x*0.05)*0.2;forceDirection.y=cos(furTime*0.7+position.y*0.04)*0.2;forceDirection.z=sin(furTime*0.7+position.z*0.04)*0.2;vec3 displacement=vec3(0.0,0.0,0.0);displacement=furGravity+forceDirection;float displacementFactor=pow(furOffset,3.0);vec3 aNormal=normal;aNormal.xyz+=displacement*displacementFactor;newPosition=vec3(newPosition.x,newPosition.y,newPosition.z)+(normalize(aNormal)*furOffset*furSpacing);\n#endif\n#ifdef NORMAL\nvNormalW=normalize(vec3(finalWorld*vec4(normal,0.0)));\n#endif\ngl_Position=viewProjection*finalWorld*vec4(newPosition,1.0);vec4 worldPos=finalWorld*vec4(newPosition,1.0);vPositionW=vec3(worldPos);\n#ifndef UV1\nvec2 uv=vec2(0.,0.);\n#endif\n#ifndef UV2\nvec2 uv2=vec2(0.,0.);\n#endif\n#ifdef DIFFUSE\nif (vDiffuseInfos.x==0.)\n{vDiffuseUV=vec2(diffuseMatrix*vec4(uv,1.0,0.0));}\nelse\n{vDiffuseUV=vec2(diffuseMatrix*vec4(uv2,1.0,0.0));}\n#ifdef HIGHLEVEL\nvFurUV=vDiffuseUV*furDensity;\n#endif\n#else\n#ifdef HIGHLEVEL\nvFurUV=uv*furDensity;\n#endif\n#endif\n#include<clipPlaneVertex>\n#include<logDepthVertex>\n#include<fogVertex>\n#include<shadowsVertex>[0..maxSimultaneousLights]\n#include<vertexColorMixing>\n#if defined(POINTSIZE) && !defined(WEBGPU)\ngl_PointSize=pointSize;\n#endif\n#define CUSTOM_VERTEX_MAIN_END\n}\n";var S=function(e){function i(){var i=e.call(this)||this;return i.DIFFUSE=!1,i.HEIGHTMAP=!1,i.CLIPPLANE=!1,i.CLIPPLANE2=!1,i.CLIPPLANE3=!1,i.CLIPPLANE4=!1,i.CLIPPLANE5=!1,i.CLIPPLANE6=!1,i.ALPHATEST=!1,i.DEPTHPREPASS=!1,i.POINTSIZE=!1,i.FOG=!1,i.NORMAL=!1,i.UV1=!1,i.UV2=!1,i.VERTEXCOLOR=!1,i.VERTEXALPHA=!1,i.NUM_BONE_INFLUENCERS=0,i.BonesPerMesh=0,i.INSTANCES=!1,i.INSTANCESCOLOR=!1,i.HIGHLEVEL=!1,i.IMAGEPROCESSINGPOSTPROCESS=!1,i.SKIPFINALCOLORCLAMP=!1,i.LOGARITHMICDEPTH=!1,i.rebuild(),i}return f(i,e),i}(u.MaterialDefines),E=function(e){function i(i,t){var n=e.call(this,i,t)||this;return n.diffuseColor=new u.Color3(1,1,1),n.furLength=1,n.furAngle=0,n.furColor=new u.Color3(.44,.21,.02),n.furOffset=0,n.furSpacing=12,n.furGravity=new u.Vector3(0,0,0),n.furSpeed=100,n.furDensity=20,n.furOcclusion=0,n._disableLighting=!1,n._maxSimultaneousLights=4,n.highLevelFur=!0,n._furTime=0,n}return f(i,e),Object.defineProperty(i.prototype,"furTime",{get:function(){return this._furTime},set:function(e){this._furTime=e},enumerable:!1,configurable:!0}),i.prototype.needAlphaBlending=function(){return this.alpha<1},i.prototype.needAlphaTesting=function(){return!1},i.prototype.getAlphaTestTexture=function(){return null},i.prototype.updateFur=function(){for(var e=1;e<this._meshes.length;e++){var i=this._meshes[e].material;i.furLength=this.furLength,i.furAngle=this.furAngle,i.furGravity=this.furGravity,i.furSpacing=this.furSpacing,i.furSpeed=this.furSpeed,i.furColor=this.furColor,i.diffuseTexture=this.diffuseTexture,i.furTexture=this.furTexture,i.highLevelFur=this.highLevelFur,i.furTime=this.furTime,i.furDensity=this.furDensity}},i.prototype.isReadyForSubMesh=function(e,i,t){var n=i._drawWrapper;if(this.isFrozen&&n.effect&&n._wasPreviouslyReady&&n._wasPreviouslyUsingInstances===t)return!0;i.materialDefines||(i.materialDefines=new S);var r=i.materialDefines,o=this.getScene();if(this._isReadyForSubMesh(i))return!0;var s=o.getEngine();if(r._areTexturesDirty&&o.texturesEnabled){if(this.diffuseTexture&&u.MaterialFlags.DiffuseTextureEnabled){if(!this.diffuseTexture.isReady())return!1;r._needUVs=!0,r.DIFFUSE=!0}if(this.heightTexture&&s.getCaps().maxVertexTextureImageUnits){if(!this.heightTexture.isReady())return!1;r._needUVs=!0,r.HEIGHTMAP=!0}}if(this.highLevelFur!==r.HIGHLEVEL&&(r.HIGHLEVEL=!0,r.markAsUnprocessed()),(0,u.PrepareDefinesForMisc)(e,o,this._useLogarithmicDepth,this.pointsCloud,this.fogEnabled,this._shouldTurnAlphaTestOn(e),r),r._needNormals=(0,u.PrepareDefinesForLights)(o,e,r,!1,this._maxSimultaneousLights,this._disableLighting),(0,u.PrepareDefinesForFrameBoundValues)(o,s,this,r,!!t),(0,u.PrepareDefinesForAttributes)(e,r,!0,!0),r.isDirty){r.markAsProcessed(),o.resetCachedMaterial();var a=new u.EffectFallbacks;r.FOG&&a.addFallback(1,"FOG"),(0,u.HandleFallbacksForShadows)(r,a,this.maxSimultaneousLights),r.NUM_BONE_INFLUENCERS>0&&a.addCPUSkinningFallback(0,e),r.IMAGEPROCESSINGPOSTPROCESS=o.imageProcessingConfiguration.applyByPostProcess;var f=[u.VertexBuffer.PositionKind];r.NORMAL&&f.push(u.VertexBuffer.NormalKind),r.UV1&&f.push(u.VertexBuffer.UVKind),r.UV2&&f.push(u.VertexBuffer.UV2Kind),r.VERTEXCOLOR&&f.push(u.VertexBuffer.ColorKind),(0,u.PrepareAttributesForBones)(f,e,r,a),(0,u.PrepareAttributesForInstances)(f,r);var l=r.toString(),d=["world","view","viewProjection","vEyePosition","vLightsType","vDiffuseColor","vFogInfos","vFogColor","pointSize","vDiffuseInfos","mBones","diffuseMatrix","logarithmicDepthConstant","furLength","furAngle","furColor","furOffset","furGravity","furTime","furSpacing","furDensity","furOcclusion"];(0,u.addClipPlaneUniforms)(d);var c=["diffuseSampler","heightTexture","furTexture"],h=[];(0,u.PrepareUniformsAndSamplersList)({uniformsNames:d,uniformBuffersNames:h,samplers:c,defines:r,maxSimultaneousLights:this.maxSimultaneousLights}),i.setEffect(o.getEngine().createEffect("fur",{attributes:f,uniformsNames:d,uniformBuffersNames:h,samplers:c,defines:l,fallbacks:a,onCompiled:this.onCompiled,onError:this.onError,indexParameters:{maxSimultaneousLights:this.maxSimultaneousLights}},s),r,this._materialContext)}return!(!i.effect||!i.effect.isReady()||(r._renderId=o.getRenderId(),n._wasPreviouslyReady=!0,n._wasPreviouslyUsingInstances=!!t,0))},i.prototype.bindForSubMesh=function(e,i,t){var n=this.getScene(),r=t.materialDefines;if(r){var o=t.effect;o&&(this._activeEffect=o,this.bindOnlyWorldMatrix(e),this._activeEffect.setMatrix("viewProjection",n.getTransformMatrix()),(0,u.BindBonesParameters)(i,this._activeEffect),this._mustRebind(n,o,t)&&(this._diffuseTexture&&u.MaterialFlags.DiffuseTextureEnabled&&(this._activeEffect.setTexture("diffuseSampler",this._diffuseTexture),this._activeEffect.setFloat2("vDiffuseInfos",this._diffuseTexture.coordinatesIndex,this._diffuseTexture.level),this._activeEffect.setMatrix("diffuseMatrix",this._diffuseTexture.getTextureMatrix())),this._heightTexture&&this._activeEffect.setTexture("heightTexture",this._heightTexture),(0,u.bindClipPlane)(this._activeEffect,this,n),this.pointsCloud&&this._activeEffect.setFloat("pointSize",this.pointSize),this._useLogarithmicDepth&&(0,u.BindLogDepth)(r,o,n),n.bindEyePosition(o)),this._activeEffect.setColor4("vDiffuseColor",this.diffuseColor,this.alpha*i.visibility),n.lightsEnabled&&!this.disableLighting&&(0,u.BindLights)(n,i,this._activeEffect,r,this.maxSimultaneousLights),n.fogEnabled&&i.applyFog&&n.fogMode!==u.Scene.FOGMODE_NONE&&this._activeEffect.setMatrix("view",n.getViewMatrix()),(0,u.BindFogParameters)(n,i,this._activeEffect),this._activeEffect.setFloat("furLength",this.furLength),this._activeEffect.setFloat("furAngle",this.furAngle),this._activeEffect.setColor4("furColor",this.furColor,1),this.highLevelFur&&(this._activeEffect.setVector3("furGravity",this.furGravity),this._activeEffect.setFloat("furOffset",this.furOffset),this._activeEffect.setFloat("furSpacing",this.furSpacing),this._activeEffect.setFloat("furDensity",this.furDensity),this._activeEffect.setFloat("furOcclusion",this.furOcclusion),this._furTime+=this.getScene().getEngine().getDeltaTime()/this.furSpeed,this._activeEffect.setFloat("furTime",this._furTime),this._activeEffect.setTexture("furTexture",this.furTexture)),this._afterBind(i,this._activeEffect,t))}},i.prototype.getAnimatables=function(){var e=[];return this.diffuseTexture&&this.diffuseTexture.animations&&this.diffuseTexture.animations.length>0&&e.push(this.diffuseTexture),this.heightTexture&&this.heightTexture.animations&&this.heightTexture.animations.length>0&&e.push(this.heightTexture),e},i.prototype.getActiveTextures=function(){var i=e.prototype.getActiveTextures.call(this);return this._diffuseTexture&&i.push(this._diffuseTexture),this._heightTexture&&i.push(this._heightTexture),i},i.prototype.hasTexture=function(i){return!!e.prototype.hasTexture.call(this,i)||this.diffuseTexture===i||this._heightTexture===i},i.prototype.dispose=function(i){if(this.diffuseTexture&&this.diffuseTexture.dispose(),this._meshes)for(var t=1;t<this._meshes.length;t++){var n=this._meshes[t].material;n&&n.dispose(i),this._meshes[t].dispose()}e.prototype.dispose.call(this,i)},i.prototype.clone=function(e){var t=this;return u.SerializationHelper.Clone((function(){return new i(e,t.getScene())}),this)},i.prototype.serialize=function(){var i=e.prototype.serialize.call(this);return i.customType="BABYLON.FurMaterial",this._meshes&&(i.sourceMeshName=this._meshes[0].name,i.quality=this._meshes.length),i},i.prototype.getClassName=function(){return"FurMaterial"},i.Parse=function(e,t,n){var r=u.SerializationHelper.Parse((function(){return new i(e.name,t)}),e,t,n);return e.sourceMeshName&&r.highLevelFur&&t.executeWhenReady((function(){var n=t.getMeshByName(e.sourceMeshName);if(n){var o=i.GenerateTexture("Fur Texture",t);r.furTexture=o,i.FurifyMesh(n,e.quality)}})),r},i.GenerateTexture=function(e,i){for(var t=new u.DynamicTexture("FurTexture "+e,256,i,!0),n=t.getContext(),r=0;r<2e4;++r)n.fillStyle="rgba(255, "+Math.floor(255*Math.random())+", "+Math.floor(255*Math.random())+", 1)",n.fillRect(Math.random()*t.getSize().width,Math.random()*t.getSize().height,2,2);return t.update(!1),t.wrapU=u.Texture.WRAP_ADDRESSMODE,t.wrapV=u.Texture.WRAP_ADDRESSMODE,t},i.FurifyMesh=function(e,t){var n,r=[e],o=e.material;if(!(o instanceof i))throw"The material of the source mesh must be a Fur Material";for(n=1;n<t;n++){var s=new i(o.name+n,e.getScene());e.getScene().materials.pop(),u.Tags.EnableFor(s),u.Tags.AddTagsTo(s,"furShellMaterial"),s.furLength=o.furLength,s.furAngle=o.furAngle,s.furGravity=o.furGravity,s.furSpacing=o.furSpacing,s.furSpeed=o.furSpeed,s.furColor=o.furColor,s.diffuseTexture=o.diffuseTexture,s.furOffset=n/t,s.furTexture=o.furTexture,s.highLevelFur=o.highLevelFur,s.furTime=o.furTime,s.furDensity=o.furDensity;var a=e.clone(e.name+n);a.material=s,a.skeleton=e.skeleton,a.position=u.Vector3.Zero(),r.push(a)}for(n=1;n<r.length;n++)r[n].parent=e;return e.material._meshes=r,r},l([(0,u.serializeAsTexture)("diffuseTexture")],i.prototype,"_diffuseTexture",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture",void 0),l([(0,u.serializeAsTexture)("heightTexture")],i.prototype,"_heightTexture",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"heightTexture",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"diffuseColor",void 0),l([(0,u.serialize)()],i.prototype,"furLength",void 0),l([(0,u.serialize)()],i.prototype,"furAngle",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"furColor",void 0),l([(0,u.serialize)()],i.prototype,"furOffset",void 0),l([(0,u.serialize)()],i.prototype,"furSpacing",void 0),l([(0,u.serializeAsVector3)()],i.prototype,"furGravity",void 0),l([(0,u.serialize)()],i.prototype,"furSpeed",void 0),l([(0,u.serialize)()],i.prototype,"furDensity",void 0),l([(0,u.serialize)()],i.prototype,"furOcclusion",void 0),l([(0,u.serialize)("disableLighting")],i.prototype,"_disableLighting",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"disableLighting",void 0),l([(0,u.serialize)("maxSimultaneousLights")],i.prototype,"_maxSimultaneousLights",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"maxSimultaneousLights",void 0),l([(0,u.serialize)()],i.prototype,"highLevelFur",void 0),l([(0,u.serialize)()],i.prototype,"furTime",null),i}(u.PushMaterial);(0,u.RegisterClass)("BABYLON.FurMaterial",E);u.ShaderStore.ShadersStore.gradientPixelShader="precision highp float;uniform vec4 vEyePosition;uniform vec4 topColor;uniform vec4 bottomColor;uniform float offset;uniform float scale;uniform float smoothness;varying vec3 vPositionW;varying vec3 vPosition;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\n#endif\n#include<helperFunctions>\n#include<__decl__lightFragment>[0]\n#include<__decl__lightFragment>[1]\n#include<__decl__lightFragment>[2]\n#include<__decl__lightFragment>[3]\n#include<lightsFragmentFunctions>\n#include<shadowsFragmentFunctions>\n#include<clipPlaneFragmentDeclaration>\n#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\n#include<logDepthDeclaration>\n#include<fogFragmentDeclaration>\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);float h=vPosition.y*scale+offset;float mysmoothness=clamp(smoothness,0.01,max(smoothness,10.));vec4 baseColor=mix(bottomColor,topColor,max(pow(max(h,0.0),mysmoothness),0.0));vec3 diffuseColor=baseColor.rgb;float alpha=baseColor.a;\n#ifdef ALPHATEST\nif (baseColor.a<0.4)\ndiscard;\n#endif\n#include<depthPrePass>\n#ifdef VERTEXCOLOR\nbaseColor.rgb*=vColor.rgb;\n#endif\n#ifdef NORMAL\nvec3 normalW=normalize(vNormalW);\n#else\nvec3 normalW=vec3(1.0,1.0,1.0);\n#endif\n#ifdef EMISSIVE\nvec3 diffuseBase=baseColor.rgb;\n#else\nvec3 diffuseBase=vec3(0.,0.,0.);\n#endif\nlightingInfo info;float shadow=1.;float glossiness=0.;float aggShadow=0.;float numLights=0.;\n#include<lightFragment>[0..maxSimultaneousLights]\n#if defined(VERTEXALPHA) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nalpha*=vColor.a;\n#endif\nvec3 finalDiffuse=clamp(diffuseBase*diffuseColor,0.0,1.0)*baseColor.rgb;vec4 color=vec4(finalDiffuse,alpha);\n#include<logDepthFragment>\n#include<fogFragment>\ngl_FragColor=color;\n#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}\n";u.ShaderStore.ShadersStore.gradientVertexShader="precision highp float;attribute vec3 position;\n#ifdef NORMAL\nattribute vec3 normal;\n#endif\n#ifdef UV1\nattribute vec2 uv;\n#endif\n#ifdef UV2\nattribute vec2 uv2;\n#endif\n#ifdef VERTEXCOLOR\nattribute vec4 color;\n#endif\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\n#include<instancesDeclaration>\nuniform mat4 view;uniform mat4 viewProjection;\n#ifdef POINTSIZE\nuniform float pointSize;\n#endif\nvarying vec3 vPositionW;varying vec3 vPosition;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\n#endif\n#include<clipPlaneVertexDeclaration>\n#include<logDepthDeclaration>\n#include<fogVertexDeclaration>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\nvec4 worldPos=finalWorld*vec4(position,1.0);gl_Position=viewProjection*worldPos;vPositionW=vec3(worldPos);vPosition=position;\n#ifdef NORMAL\nvNormalW=normalize(vec3(finalWorld*vec4(normal,0.0)));\n#endif\n#ifndef UV1\nvec2 uv=vec2(0.,0.);\n#endif\n#ifndef UV2\nvec2 uv2=vec2(0.,0.);\n#endif\n#include<clipPlaneVertex>\n#include<logDepthVertex>\n#include<fogVertex>\n#include<shadowsVertex>[0..maxSimultaneousLights]\n#include<vertexColorMixing>\n#if defined(POINTSIZE) && !defined(WEBGPU)\ngl_PointSize=pointSize;\n#endif\n#define CUSTOM_VERTEX_MAIN_END\n}\n";var C=function(e){function i(){var i=e.call(this)||this;return i.EMISSIVE=!1,i.CLIPPLANE=!1,i.CLIPPLANE2=!1,i.CLIPPLANE3=!1,i.CLIPPLANE4=!1,i.CLIPPLANE5=!1,i.CLIPPLANE6=!1,i.ALPHATEST=!1,i.DEPTHPREPASS=!1,i.POINTSIZE=!1,i.FOG=!1,i.NORMAL=!1,i.UV1=!1,i.UV2=!1,i.VERTEXCOLOR=!1,i.VERTEXALPHA=!1,i.NUM_BONE_INFLUENCERS=0,i.BonesPerMesh=0,i.INSTANCES=!1,i.INSTANCESCOLOR=!1,i.IMAGEPROCESSINGPOSTPROCESS=!1,i.SKIPFINALCOLORCLAMP=!1,i.LOGARITHMICDEPTH=!1,i.rebuild(),i}return f(i,e),i}(u.MaterialDefines),P=function(e){function i(i,t){var n=e.call(this,i,t)||this;return n._maxSimultaneousLights=4,n.topColor=new u.Color3(1,0,0),n.topColorAlpha=1,n.bottomColor=new u.Color3(0,0,1),n.bottomColorAlpha=1,n.offset=0,n.scale=1,n.smoothness=1,n._disableLighting=!1,n}return f(i,e),i.prototype.needAlphaBlending=function(){return this.alpha<1||this.topColorAlpha<1||this.bottomColorAlpha<1},i.prototype.needAlphaTesting=function(){return!0},i.prototype.getAlphaTestTexture=function(){return null},i.prototype.isReadyForSubMesh=function(e,i,t){var n=i._drawWrapper;if(this.isFrozen&&n.effect&&n._wasPreviouslyReady&&n._wasPreviouslyUsingInstances===t)return!0;i.materialDefines||(i.materialDefines=new C);var r=i.materialDefines,o=this.getScene();if(this._isReadyForSubMesh(i))return!0;var s=o.getEngine();if((0,u.PrepareDefinesForFrameBoundValues)(o,s,this,r,!!t),(0,u.PrepareDefinesForMisc)(e,o,this._useLogarithmicDepth,this.pointsCloud,this.fogEnabled,this._shouldTurnAlphaTestOn(e),r),r._needNormals=(0,u.PrepareDefinesForLights)(o,e,r,!1,this._maxSimultaneousLights,this._disableLighting),r.EMISSIVE=this._disableLighting,(0,u.PrepareDefinesForAttributes)(e,r,!1,!0),r.isDirty){r.markAsProcessed(),o.resetCachedMaterial();var a=new u.EffectFallbacks;r.FOG&&a.addFallback(1,"FOG"),(0,u.HandleFallbacksForShadows)(r,a),r.NUM_BONE_INFLUENCERS>0&&a.addCPUSkinningFallback(0,e),r.IMAGEPROCESSINGPOSTPROCESS=o.imageProcessingConfiguration.applyByPostProcess;var f=[u.VertexBuffer.PositionKind];r.NORMAL&&f.push(u.VertexBuffer.NormalKind),r.UV1&&f.push(u.VertexBuffer.UVKind),r.UV2&&f.push(u.VertexBuffer.UV2Kind),r.VERTEXCOLOR&&f.push(u.VertexBuffer.ColorKind),(0,u.PrepareAttributesForBones)(f,e,r,a),(0,u.PrepareAttributesForInstances)(f,r);var l=r.toString(),d=["world","view","viewProjection","vEyePosition","vLightsType","vFogInfos","vFogColor","pointSize","mBones","logarithmicDepthConstant","topColor","bottomColor","offset","smoothness","scale"];(0,u.addClipPlaneUniforms)(d);var c=[],h=[];(0,u.PrepareUniformsAndSamplersList)({uniformsNames:d,uniformBuffersNames:h,samplers:c,defines:r,maxSimultaneousLights:4}),i.setEffect(o.getEngine().createEffect("gradient",{attributes:f,uniformsNames:d,uniformBuffersNames:h,samplers:c,defines:l,fallbacks:a,onCompiled:this.onCompiled,onError:this.onError,indexParameters:{maxSimultaneousLights:4}},s),r,this._materialContext)}return!(!i.effect||!i.effect.isReady()||(r._renderId=o.getRenderId(),n._wasPreviouslyReady=!0,n._wasPreviouslyUsingInstances=!!t,0))},i.prototype.bindForSubMesh=function(e,i,t){var n=this.getScene(),r=t.materialDefines;if(r){var o=t.effect;o&&(this._activeEffect=o,this.bindOnlyWorldMatrix(e),this._activeEffect.setMatrix("viewProjection",n.getTransformMatrix()),(0,u.BindBonesParameters)(i,o),this._mustRebind(n,o,t)&&((0,u.bindClipPlane)(o,this,n),this.pointsCloud&&this._activeEffect.setFloat("pointSize",this.pointSize),this._useLogarithmicDepth&&(0,u.BindLogDepth)(r,o,n),n.bindEyePosition(o)),n.lightsEnabled&&!this.disableLighting&&(0,u.BindLights)(n,i,this._activeEffect,r,this.maxSimultaneousLights),n.fogEnabled&&i.applyFog&&n.fogMode!==u.Scene.FOGMODE_NONE&&this._activeEffect.setMatrix("view",n.getViewMatrix()),(0,u.BindFogParameters)(n,i,this._activeEffect),this._activeEffect.setColor4("topColor",this.topColor,this.topColorAlpha),this._activeEffect.setColor4("bottomColor",this.bottomColor,this.bottomColorAlpha),this._activeEffect.setFloat("offset",this.offset),this._activeEffect.setFloat("scale",this.scale),this._activeEffect.setFloat("smoothness",this.smoothness),this._afterBind(i,this._activeEffect,t))}},i.prototype.getAnimatables=function(){return[]},i.prototype.dispose=function(i){e.prototype.dispose.call(this,i)},i.prototype.clone=function(e){var t=this;return u.SerializationHelper.Clone((function(){return new i(e,t.getScene())}),this)},i.prototype.serialize=function(){var i=e.prototype.serialize.call(this);return i.customType="BABYLON.GradientMaterial",i},i.prototype.getClassName=function(){return"GradientMaterial"},i.Parse=function(e,t,n){return u.SerializationHelper.Parse((function(){return new i(e.name,t)}),e,t,n)},l([(0,u.serialize)("maxSimultaneousLights")],i.prototype,"_maxSimultaneousLights",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"maxSimultaneousLights",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"topColor",void 0),l([(0,u.serialize)()],i.prototype,"topColorAlpha",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"bottomColor",void 0),l([(0,u.serialize)()],i.prototype,"bottomColorAlpha",void 0),l([(0,u.serialize)()],i.prototype,"offset",void 0),l([(0,u.serialize)()],i.prototype,"scale",void 0),l([(0,u.serialize)()],i.prototype,"smoothness",void 0),l([(0,u.serialize)("disableLighting")],i.prototype,"_disableLighting",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"disableLighting",void 0),i}(u.PushMaterial);(0,u.RegisterClass)("BABYLON.GradientMaterial",P);u.ShaderStore.ShadersStore.gridPixelShader="#extension GL_OES_standard_derivatives : enable\n#define SQRT2 1.41421356\n#define PI 3.14159\nprecision highp float;uniform float visibility;uniform vec3 mainColor;uniform vec3 lineColor;uniform vec4 gridControl;uniform vec3 gridOffset;varying vec3 vPosition;varying vec3 vNormal;\n#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\n#include<logDepthDeclaration>\n#include<fogFragmentDeclaration>\n#ifdef OPACITY\nvarying vec2 vOpacityUV;uniform sampler2D opacitySampler;uniform vec2 vOpacityInfos;\n#endif\nfloat getDynamicVisibility(float position) {float majorGridFrequency=gridControl.y;if (floor(position+0.5)==floor(position/majorGridFrequency+0.5)*majorGridFrequency)\n{return 1.0;}\nreturn gridControl.z;}\nfloat getAnisotropicAttenuation(float differentialLength) {const float maxNumberOfLines=10.0;return clamp(1.0/(differentialLength+1.0)-1.0/maxNumberOfLines,0.0,1.0);}\nfloat isPointOnLine(float position,float differentialLength) {float fractionPartOfPosition=position-floor(position+0.5); \nfractionPartOfPosition/=differentialLength; \n#ifdef ANTIALIAS\nfractionPartOfPosition=clamp(fractionPartOfPosition,-1.,1.);float result=0.5+0.5*cos(fractionPartOfPosition*PI); \nreturn result;\n#else\nreturn abs(fractionPartOfPosition)<SQRT2/4. ? 1. : 0.;\n#endif\n}\nfloat contributionOnAxis(float position) {float differentialLength=length(vec2(dFdx(position),dFdy(position)));differentialLength*=SQRT2; \nfloat result=isPointOnLine(position,differentialLength);float dynamicVisibility=getDynamicVisibility(position);result*=dynamicVisibility;float anisotropicAttenuation=getAnisotropicAttenuation(differentialLength);result*=anisotropicAttenuation;return result;}\nfloat normalImpactOnAxis(float x) {float normalImpact=clamp(1.0-3.0*abs(x*x*x),0.0,1.0);return normalImpact;}\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\nfloat gridRatio=gridControl.x;vec3 gridPos=(vPosition+gridOffset.xyz)/gridRatio;float x=contributionOnAxis(gridPos.x);float y=contributionOnAxis(gridPos.y);float z=contributionOnAxis(gridPos.z);vec3 normal=normalize(vNormal);x*=normalImpactOnAxis(normal.x);y*=normalImpactOnAxis(normal.y);z*=normalImpactOnAxis(normal.z);\n#ifdef MAX_LINE\nfloat grid=clamp(max(max(x,y),z),0.,1.);\n#else\nfloat grid=clamp(x+y+z,0.,1.);\n#endif\nvec3 color=mix(mainColor,lineColor,grid);\n#ifdef FOG\n#include<fogFragment>\n#endif\nfloat opacity=1.0;\n#ifdef TRANSPARENT\nopacity=clamp(grid,0.08,gridControl.w*grid);\n#endif\n#ifdef OPACITY\nopacity*=texture2D(opacitySampler,vOpacityUV).a;\n#endif\ngl_FragColor=vec4(color.rgb,opacity*visibility);\n#ifdef TRANSPARENT\n#ifdef PREMULTIPLYALPHA\ngl_FragColor.rgb*=opacity;\n#endif\n#else\n#endif\n#include<logDepthFragment>\n#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}\n";u.ShaderStore.ShadersStore.gridVertexShader="precision highp float;attribute vec3 position;attribute vec3 normal;\n#ifdef UV1\nattribute vec2 uv;\n#endif\n#ifdef UV2\nattribute vec2 uv2;\n#endif\n#include<instancesDeclaration>\nuniform mat4 projection;uniform mat4 view;varying vec3 vPosition;varying vec3 vNormal;\n#include<logDepthDeclaration>\n#include<fogVertexDeclaration>\n#ifdef OPACITY\nvarying vec2 vOpacityUV;uniform mat4 opacityMatrix;uniform vec2 vOpacityInfos;\n#endif\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\n#include<instancesVertex>\nvec4 worldPos=finalWorld*vec4(position,1.0);\n#include<fogVertex>\nvec4 cameraSpacePosition=view*worldPos;gl_Position=projection*cameraSpacePosition;\n#ifdef OPACITY\n#ifndef UV1\nvec2 uv=vec2(0.,0.);\n#endif\n#ifndef UV2\nvec2 uv2=vec2(0.,0.);\n#endif\nif (vOpacityInfos.x==0.)\n{vOpacityUV=vec2(opacityMatrix*vec4(uv,1.0,0.0));}\nelse\n{vOpacityUV=vec2(opacityMatrix*vec4(uv2,1.0,0.0));}\n#endif \n#include<logDepthVertex>\nvPosition=position;vNormal=normal;\n#define CUSTOM_VERTEX_MAIN_END\n}";var y=function(e){function i(){var i=e.call(this)||this;return i.OPACITY=!1,i.ANTIALIAS=!1,i.TRANSPARENT=!1,i.FOG=!1,i.PREMULTIPLYALPHA=!1,i.MAX_LINE=!1,i.UV1=!1,i.UV2=!1,i.INSTANCES=!1,i.THIN_INSTANCES=!1,i.IMAGEPROCESSINGPOSTPROCESS=!1,i.SKIPFINALCOLORCLAMP=!1,i.LOGARITHMICDEPTH=!1,i.rebuild(),i}return f(i,e),i}(u.MaterialDefines),A=function(e){function i(i,t){var n=e.call(this,i,t)||this;return n.mainColor=u.Color3.Black(),n.lineColor=u.Color3.Teal(),n.gridRatio=1,n.gridOffset=u.Vector3.Zero(),n.majorUnitFrequency=10,n.minorUnitVisibility=.33,n.opacity=1,n.antialias=!0,n.preMultiplyAlpha=!1,n.useMaxLine=!1,n._gridControl=new u.Vector4(n.gridRatio,n.majorUnitFrequency,n.minorUnitVisibility,n.opacity),n}return f(i,e),i.prototype.needAlphaBlending=function(){return this.opacity<1||this._opacityTexture&&this._opacityTexture.isReady()},i.prototype.needAlphaBlendingForMesh=function(e){return e.visibility<1||this.needAlphaBlending()},i.prototype.isReadyForSubMesh=function(e,i,t){var n=i._drawWrapper;if(this.isFrozen&&n.effect&&n._wasPreviouslyReady&&n._wasPreviouslyUsingInstances===t)return!0;i.materialDefines||(i.materialDefines=new y);var r=i.materialDefines,o=this.getScene();if(this._isReadyForSubMesh(i))return!0;if(r.TRANSPARENT!==this.opacity<1&&(r.TRANSPARENT=!r.TRANSPARENT,r.markAsUnprocessed()),r.PREMULTIPLYALPHA!=this.preMultiplyAlpha&&(r.PREMULTIPLYALPHA=!r.PREMULTIPLYALPHA,r.markAsUnprocessed()),r.MAX_LINE!==this.useMaxLine&&(r.MAX_LINE=!r.MAX_LINE,r.markAsUnprocessed()),r.ANTIALIAS!==this.antialias&&(r.ANTIALIAS=!r.ANTIALIAS,r.markAsUnprocessed()),r._areTexturesDirty&&(r._needUVs=!1,o.texturesEnabled&&this._opacityTexture&&u.MaterialFlags.OpacityTextureEnabled)){if(!this._opacityTexture.isReady())return!1;r._needUVs=!0,r.OPACITY=!0}if((0,u.PrepareDefinesForMisc)(e,o,this._useLogarithmicDepth,!1,this.fogEnabled,!1,r),(0,u.PrepareDefinesForFrameBoundValues)(o,o.getEngine(),this,r,!!t),r.isDirty){r.markAsProcessed(),o.resetCachedMaterial(),(0,u.PrepareDefinesForAttributes)(e,r,!1,!1);var s=[u.VertexBuffer.PositionKind,u.VertexBuffer.NormalKind];r.UV1&&s.push(u.VertexBuffer.UVKind),r.UV2&&s.push(u.VertexBuffer.UV2Kind),r.IMAGEPROCESSINGPOSTPROCESS=o.imageProcessingConfiguration.applyByPostProcess,(0,u.PrepareAttributesForInstances)(s,r);var a=r.toString();i.setEffect(o.getEngine().createEffect("grid",s,["projection","mainColor","lineColor","gridControl","gridOffset","vFogInfos","vFogColor","world","view","opacityMatrix","vOpacityInfos","visibility","logarithmicDepthConstant"],["opacitySampler"],a,void 0,this.onCompiled,this.onError),r,this._materialContext)}return!(!i.effect||!i.effect.isReady()||(r._renderId=o.getRenderId(),n._wasPreviouslyReady=!0,n._wasPreviouslyUsingInstances=!!t,0))},i.prototype.bindForSubMesh=function(e,i,t){var n=this.getScene(),r=t.materialDefines;if(r){var o=t.effect;o&&(this._activeEffect=o,this._activeEffect.setFloat("visibility",i.visibility),r.INSTANCES&&!r.THIN_INSTANCE||this.bindOnlyWorldMatrix(e),this._activeEffect.setMatrix("view",n.getViewMatrix()),this._activeEffect.setMatrix("projection",n.getProjectionMatrix()),this._mustRebind(n,o,t)&&(this._activeEffect.setColor3("mainColor",this.mainColor),this._activeEffect.setColor3("lineColor",this.lineColor),this._activeEffect.setVector3("gridOffset",this.gridOffset),this._gridControl.x=this.gridRatio,this._gridControl.y=Math.round(this.majorUnitFrequency),this._gridControl.z=this.minorUnitVisibility,this._gridControl.w=this.opacity,this._activeEffect.setVector4("gridControl",this._gridControl),this._opacityTexture&&u.MaterialFlags.OpacityTextureEnabled&&(this._activeEffect.setTexture("opacitySampler",this._opacityTexture),this._activeEffect.setFloat2("vOpacityInfos",this._opacityTexture.coordinatesIndex,this._opacityTexture.level),this._activeEffect.setMatrix("opacityMatrix",this._opacityTexture.getTextureMatrix())),this._useLogarithmicDepth&&(0,u.BindLogDepth)(r,o,n)),(0,u.BindFogParameters)(n,i,this._activeEffect),this._afterBind(i,this._activeEffect,t))}},i.prototype.dispose=function(i){e.prototype.dispose.call(this,i)},i.prototype.clone=function(e){var t=this;return u.SerializationHelper.Clone((function(){return new i(e,t.getScene())}),this)},i.prototype.serialize=function(){var i=e.prototype.serialize.call(this);return i.customType="BABYLON.GridMaterial",i},i.prototype.getClassName=function(){return"GridMaterial"},i.Parse=function(e,t,n){return u.SerializationHelper.Parse((function(){return new i(e.name,t)}),e,t,n)},l([(0,u.serializeAsColor3)()],i.prototype,"mainColor",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"lineColor",void 0),l([(0,u.serialize)()],i.prototype,"gridRatio",void 0),l([(0,u.serializeAsVector3)()],i.prototype,"gridOffset",void 0),l([(0,u.serialize)()],i.prototype,"majorUnitFrequency",void 0),l([(0,u.serialize)()],i.prototype,"minorUnitVisibility",void 0),l([(0,u.serialize)()],i.prototype,"opacity",void 0),l([(0,u.serialize)()],i.prototype,"antialias",void 0),l([(0,u.serialize)()],i.prototype,"preMultiplyAlpha",void 0),l([(0,u.serialize)()],i.prototype,"useMaxLine",void 0),l([(0,u.serializeAsTexture)("opacityTexture")],i.prototype,"_opacityTexture",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"opacityTexture",void 0),i}(u.PushMaterial);(0,u.RegisterClass)("BABYLON.GridMaterial",A);u.ShaderStore.ShadersStore.lavaPixelShader="precision highp float;uniform vec4 vEyePosition;uniform vec4 vDiffuseColor;varying vec3 vPositionW;uniform float time;uniform float speed;uniform float movingSpeed;uniform vec3 fogColor;uniform sampler2D noiseTexture;uniform float fogDensity;varying float noise;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\n#endif\n#include<helperFunctions>\n#include<__decl__lightFragment>[0]\n#include<__decl__lightFragment>[1]\n#include<__decl__lightFragment>[2]\n#include<__decl__lightFragment>[3]\n#include<lightsFragmentFunctions>\n#include<shadowsFragmentFunctions>\n#ifdef DIFFUSE\nvarying vec2 vDiffuseUV;uniform sampler2D diffuseSampler;uniform vec2 vDiffuseInfos;\n#endif\n#include<clipPlaneFragmentDeclaration>\n#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\n#include<logDepthDeclaration>\n#include<fogFragmentDeclaration>\nfloat random( vec3 scale,float seed ){return fract( sin( dot( gl_FragCoord.xyz+seed,scale ) )*43758.5453+seed ) ;}\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);vec4 baseColor=vec4(1.,1.,1.,1.);vec3 diffuseColor=vDiffuseColor.rgb;float alpha=vDiffuseColor.a;\n#ifdef DIFFUSE\nvec4 noiseTex=texture2D( noiseTexture,vDiffuseUV );vec2 T1=vDiffuseUV+vec2( 1.5,-1.5 )*time *0.02;vec2 T2=vDiffuseUV+vec2( -0.5,2.0 )*time*0.01*speed;T1.x+=noiseTex.x*2.0;T1.y+=noiseTex.y*2.0;T2.x-=noiseTex.y*0.2+time*0.001*movingSpeed;T2.y+=noiseTex.z*0.2+time*0.002*movingSpeed;float p=texture2D( noiseTexture,T1*3.0 ).a;vec4 lavaColor=texture2D( diffuseSampler,T2*4.0);vec4 temp=lavaColor*( vec4( p,p,p,p )*2. )+( lavaColor*lavaColor-0.1 );baseColor=temp;float depth=gl_FragCoord.z*4.0;const float LOG2=1.442695;float fogFactor=exp2(-fogDensity*fogDensity*depth*depth*LOG2 );fogFactor=1.0-clamp( fogFactor,0.0,1.0 );baseColor=mix( baseColor,vec4( fogColor,baseColor.w ),fogFactor );diffuseColor=baseColor.rgb;\n#ifdef ALPHATEST\nif (baseColor.a<0.4)\ndiscard;\n#endif\n#include<depthPrePass>\nbaseColor.rgb*=vDiffuseInfos.y;\n#endif\n#ifdef VERTEXCOLOR\nbaseColor.rgb*=vColor.rgb;\n#endif\n#ifdef NORMAL\nvec3 normalW=normalize(vNormalW);\n#else\nvec3 normalW=vec3(1.0,1.0,1.0);\n#endif\n#ifdef UNLIT\nvec3 diffuseBase=vec3(1.,1.,1.);\n#else\nvec3 diffuseBase=vec3(0.,0.,0.);lightingInfo info;float shadow=1.;float glossiness=0.;float aggShadow=0.;float numLights=0.;\n#include<lightFragment>[0]\n#include<lightFragment>[1]\n#include<lightFragment>[2]\n#include<lightFragment>[3]\n#endif\n#if defined(VERTEXALPHA) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nalpha*=vColor.a;\n#endif\nvec3 finalDiffuse=clamp(diffuseBase*diffuseColor,0.0,1.0)*baseColor.rgb;vec4 color=vec4(finalDiffuse,alpha);\n#include<logDepthFragment>\n#include<fogFragment>\ngl_FragColor=color;\n#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}";u.ShaderStore.ShadersStore.lavaVertexShader="precision highp float;uniform float time;uniform float lowFrequencySpeed;varying float noise;attribute vec3 position;\n#ifdef NORMAL\nattribute vec3 normal;\n#endif\n#ifdef UV1\nattribute vec2 uv;\n#endif\n#ifdef UV2\nattribute vec2 uv2;\n#endif\n#ifdef VERTEXCOLOR\nattribute vec4 color;\n#endif\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\n#include<instancesDeclaration>\nuniform mat4 view;uniform mat4 viewProjection;\n#ifdef DIFFUSE\nvarying vec2 vDiffuseUV;uniform mat4 diffuseMatrix;uniform vec2 vDiffuseInfos;\n#endif\n#ifdef POINTSIZE\nuniform float pointSize;\n#endif\nvarying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\n#endif\n#include<clipPlaneVertexDeclaration>\n#include<logDepthDeclaration>\n#include<fogVertexDeclaration>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n/* NOISE FUNCTIONS */\nvec3 mod289(vec3 x)\n{return x-floor(x*(1.0/289.0))*289.0;}\nvec4 mod289(vec4 x)\n{return x-floor(x*(1.0/289.0))*289.0;}\nvec4 permute(vec4 x)\n{return mod289(((x*34.0)+1.0)*x);}\nvec4 taylorInvSqrt(vec4 r)\n{return 1.79284291400159-0.85373472095314*r;}\nvec3 fade(vec3 t) {return t*t*t*(t*(t*6.0-15.0)+10.0);}\nfloat pnoise(vec3 P,vec3 rep)\n{vec3 Pi0=mod(floor(P),rep); \nvec3 Pi1=mod(Pi0+vec3(1.0),rep); \nPi0=mod289(Pi0);Pi1=mod289(Pi1);vec3 Pf0=fract(P); \nvec3 Pf1=Pf0-vec3(1.0); \nvec4 ix=vec4(Pi0.x,Pi1.x,Pi0.x,Pi1.x);vec4 iy=vec4(Pi0.yy,Pi1.yy);vec4 iz0=Pi0.zzzz;vec4 iz1=Pi1.zzzz;vec4 ixy=permute(permute(ix)+iy);vec4 ixy0=permute(ixy+iz0);vec4 ixy1=permute(ixy+iz1);vec4 gx0=ixy0*(1.0/7.0);vec4 gy0=fract(floor(gx0)*(1.0/7.0))-0.5;gx0=fract(gx0);vec4 gz0=vec4(0.5)-abs(gx0)-abs(gy0);vec4 sz0=step(gz0,vec4(0.0));gx0-=sz0*(step(0.0,gx0)-0.5);gy0-=sz0*(step(0.0,gy0)-0.5);vec4 gx1=ixy1*(1.0/7.0);vec4 gy1=fract(floor(gx1)*(1.0/7.0))-0.5;gx1=fract(gx1);vec4 gz1=vec4(0.5)-abs(gx1)-abs(gy1);vec4 sz1=step(gz1,vec4(0.0));gx1-=sz1*(step(0.0,gx1)-0.5);gy1-=sz1*(step(0.0,gy1)-0.5);vec3 g000=vec3(gx0.x,gy0.x,gz0.x);vec3 g100=vec3(gx0.y,gy0.y,gz0.y);vec3 g010=vec3(gx0.z,gy0.z,gz0.z);vec3 g110=vec3(gx0.w,gy0.w,gz0.w);vec3 g001=vec3(gx1.x,gy1.x,gz1.x);vec3 g101=vec3(gx1.y,gy1.y,gz1.y);vec3 g011=vec3(gx1.z,gy1.z,gz1.z);vec3 g111=vec3(gx1.w,gy1.w,gz1.w);vec4 norm0=taylorInvSqrt(vec4(dot(g000,g000),dot(g010,g010),dot(g100,g100),dot(g110,g110)));g000*=norm0.x;g010*=norm0.y;g100*=norm0.z;g110*=norm0.w;vec4 norm1=taylorInvSqrt(vec4(dot(g001,g001),dot(g011,g011),dot(g101,g101),dot(g111,g111)));g001*=norm1.x;g011*=norm1.y;g101*=norm1.z;g111*=norm1.w;float n000=dot(g000,Pf0);float n100=dot(g100,vec3(Pf1.x,Pf0.yz));float n010=dot(g010,vec3(Pf0.x,Pf1.y,Pf0.z));float n110=dot(g110,vec3(Pf1.xy,Pf0.z));float n001=dot(g001,vec3(Pf0.xy,Pf1.z));float n101=dot(g101,vec3(Pf1.x,Pf0.y,Pf1.z));float n011=dot(g011,vec3(Pf0.x,Pf1.yz));float n111=dot(g111,Pf1);vec3 fade_xyz=fade(Pf0);vec4 n_z=mix(vec4(n000,n100,n010,n110),vec4(n001,n101,n011,n111),fade_xyz.z);vec2 n_yz=mix(n_z.xy,n_z.zw,fade_xyz.y);float n_xyz=mix(n_yz.x,n_yz.y,fade_xyz.x);return 2.2*n_xyz;}\n/* END FUNCTION */\nfloat turbulence( vec3 p ) {float w=100.0;float t=-.5;for (float f=1.0 ; f<=10.0 ; f++ ){float power=pow( 2.0,f );t+=abs( pnoise( vec3( power*p ),vec3( 10.0,10.0,10.0 ) )/power );}\nreturn t;}\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\n#ifdef NORMAL\nnoise=10.0* -.10*turbulence( .5*normal+time*1.15 );float b=lowFrequencySpeed*5.0*pnoise( 0.05*position +vec3(time*1.025),vec3( 100.0 ) );float displacement=- 1.5*noise+b;vec3 newPosition=position+normal*displacement;gl_Position=viewProjection*finalWorld*vec4( newPosition,1.0 );vec4 worldPos=finalWorld*vec4(newPosition,1.0);vPositionW=vec3(worldPos);vNormalW=normalize(vec3(finalWorld*vec4(normal,0.0)));\n#endif\n#ifndef UV1\nvec2 uv=vec2(0.,0.);\n#endif\n#ifndef UV2\nvec2 uv2=vec2(0.,0.);\n#endif\n#ifdef DIFFUSE\nif (vDiffuseInfos.x==0.)\n{vDiffuseUV=vec2(diffuseMatrix*vec4(uv,1.0,0.0));}\nelse\n{vDiffuseUV=vec2(diffuseMatrix*vec4(uv2,1.0,0.0));}\n#endif\n#include<clipPlaneVertex>\n#include<fogVertex>\n#include<shadowsVertex>[0..maxSimultaneousLights]\n#include<vertexColorMixing>\n#include<logDepthVertex>\n#if defined(POINTSIZE) && !defined(WEBGPU)\ngl_PointSize=pointSize;\n#endif\n#define CUSTOM_VERTEX_MAIN_END\n}";var I=function(e){function i(){var i=e.call(this)||this;return i.DIFFUSE=!1,i.CLIPPLANE=!1,i.CLIPPLANE2=!1,i.CLIPPLANE3=!1,i.CLIPPLANE4=!1,i.CLIPPLANE5=!1,i.CLIPPLANE6=!1,i.ALPHATEST=!1,i.DEPTHPREPASS=!1,i.POINTSIZE=!1,i.FOG=!1,i.LIGHT0=!1,i.LIGHT1=!1,i.LIGHT2=!1,i.LIGHT3=!1,i.SPOTLIGHT0=!1,i.SPOTLIGHT1=!1,i.SPOTLIGHT2=!1,i.SPOTLIGHT3=!1,i.HEMILIGHT0=!1,i.HEMILIGHT1=!1,i.HEMILIGHT2=!1,i.HEMILIGHT3=!1,i.DIRLIGHT0=!1,i.DIRLIGHT1=!1,i.DIRLIGHT2=!1,i.DIRLIGHT3=!1,i.POINTLIGHT0=!1,i.POINTLIGHT1=!1,i.POINTLIGHT2=!1,i.POINTLIGHT3=!1,i.SHADOW0=!1,i.SHADOW1=!1,i.SHADOW2=!1,i.SHADOW3=!1,i.SHADOWS=!1,i.SHADOWESM0=!1,i.SHADOWESM1=!1,i.SHADOWESM2=!1,i.SHADOWESM3=!1,i.SHADOWPOISSON0=!1,i.SHADOWPOISSON1=!1,i.SHADOWPOISSON2=!1,i.SHADOWPOISSON3=!1,i.SHADOWPCF0=!1,i.SHADOWPCF1=!1,i.SHADOWPCF2=!1,i.SHADOWPCF3=!1,i.SHADOWPCSS0=!1,i.SHADOWPCSS1=!1,i.SHADOWPCSS2=!1,i.SHADOWPCSS3=!1,i.NORMAL=!1,i.UV1=!1,i.UV2=!1,i.VERTEXCOLOR=!1,i.VERTEXALPHA=!1,i.NUM_BONE_INFLUENCERS=0,i.BonesPerMesh=0,i.INSTANCES=!1,i.INSTANCESCOLOR=!1,i.UNLIT=!1,i.IMAGEPROCESSINGPOSTPROCESS=!1,i.SKIPFINALCOLORCLAMP=!1,i.LOGARITHMICDEPTH=!1,i.rebuild(),i}return f(i,e),i}(u.MaterialDefines),L=function(e){function i(i,t){var n=e.call(this,i,t)||this;return n.speed=1,n.movingSpeed=1,n.lowFrequencySpeed=1,n.fogDensity=.15,n._lastTime=0,n.diffuseColor=new u.Color3(1,1,1),n._disableLighting=!1,n._unlit=!1,n._maxSimultaneousLights=4,n._scaledDiffuse=new u.Color3,n}return f(i,e),i.prototype.needAlphaBlending=function(){return this.alpha<1},i.prototype.needAlphaTesting=function(){return!1},i.prototype.getAlphaTestTexture=function(){return null},i.prototype.isReadyForSubMesh=function(e,i,t){var n=i._drawWrapper;if(this.isFrozen&&n.effect&&n._wasPreviouslyReady&&n._wasPreviouslyUsingInstances===t)return!0;i.materialDefines||(i.materialDefines=new I);var r=i.materialDefines,o=this.getScene();if(this._isReadyForSubMesh(i))return!0;var s=o.getEngine();if(r._areTexturesDirty&&(r._needUVs=!1,o.texturesEnabled&&this._diffuseTexture&&u.MaterialFlags.DiffuseTextureEnabled)){if(!this._diffuseTexture.isReady())return!1;r._needUVs=!0,r.DIFFUSE=!0}if((0,u.PrepareDefinesForMisc)(e,o,this._useLogarithmicDepth,this.pointsCloud,this.fogEnabled,this._shouldTurnAlphaTestOn(e),r),r._needNormals=!0,(0,u.PrepareDefinesForLights)(o,e,r,!1,this._maxSimultaneousLights,this._disableLighting),(0,u.PrepareDefinesForFrameBoundValues)(o,s,this,r,!!t),(0,u.PrepareDefinesForAttributes)(e,r,!0,!0),r.isDirty){r.markAsProcessed(),o.resetCachedMaterial();var a=new u.EffectFallbacks;r.FOG&&a.addFallback(1,"FOG"),(0,u.HandleFallbacksForShadows)(r,a),r.NUM_BONE_INFLUENCERS>0&&a.addCPUSkinningFallback(0,e),r.IMAGEPROCESSINGPOSTPROCESS=o.imageProcessingConfiguration.applyByPostProcess;var f=[u.VertexBuffer.PositionKind];r.NORMAL&&f.push(u.VertexBuffer.NormalKind),r.UV1&&f.push(u.VertexBuffer.UVKind),r.UV2&&f.push(u.VertexBuffer.UV2Kind),r.VERTEXCOLOR&&f.push(u.VertexBuffer.ColorKind),(0,u.PrepareAttributesForBones)(f,e,r,a),(0,u.PrepareAttributesForInstances)(f,r);var l=r.toString(),d=["world","view","viewProjection","vEyePosition","vLightsType","vDiffuseColor","vFogInfos","vFogColor","pointSize","vDiffuseInfos","mBones","diffuseMatrix","logarithmicDepthConstant","time","speed","movingSpeed","fogColor","fogDensity","lowFrequencySpeed"];(0,u.addClipPlaneUniforms)(d);var c=["diffuseSampler","noiseTexture"],h=[];(0,u.PrepareUniformsAndSamplersList)({uniformsNames:d,uniformBuffersNames:h,samplers:c,defines:r,maxSimultaneousLights:this.maxSimultaneousLights}),i.setEffect(o.getEngine().createEffect("lava",{attributes:f,uniformsNames:d,uniformBuffersNames:h,samplers:c,defines:l,fallbacks:a,onCompiled:this.onCompiled,onError:this.onError,indexParameters:{maxSimultaneousLights:this.maxSimultaneousLights}},s),r,this._materialContext)}return!(!i.effect||!i.effect.isReady()||(r._renderId=o.getRenderId(),n._wasPreviouslyReady=!0,n._wasPreviouslyUsingInstances=!!t,0))},i.prototype.bindForSubMesh=function(e,i,t){var n=this.getScene(),r=t.materialDefines;if(r){var o=t.effect;o&&(this._activeEffect=o,r.UNLIT=this._unlit,this.bindOnlyWorldMatrix(e),this._activeEffect.setMatrix("viewProjection",n.getTransformMatrix()),(0,u.BindBonesParameters)(i,this._activeEffect),this._mustRebind(n,o,t)&&(this.diffuseTexture&&u.MaterialFlags.DiffuseTextureEnabled&&(this._activeEffect.setTexture("diffuseSampler",this.diffuseTexture),this._activeEffect.setFloat2("vDiffuseInfos",this.diffuseTexture.coordinatesIndex,this.diffuseTexture.level),this._activeEffect.setMatrix("diffuseMatrix",this.diffuseTexture.getTextureMatrix())),this.noiseTexture&&this._activeEffect.setTexture("noiseTexture",this.noiseTexture),(0,u.bindClipPlane)(o,this,n),this.pointsCloud&&this._activeEffect.setFloat("pointSize",this.pointSize),this._useLogarithmicDepth&&(0,u.BindLogDepth)(r,o,n),n.bindEyePosition(o)),this._activeEffect.setColor4("vDiffuseColor",this._scaledDiffuse,this.alpha*i.visibility),n.lightsEnabled&&!this.disableLighting&&(0,u.BindLights)(n,i,this._activeEffect,r),n.fogEnabled&&i.applyFog&&n.fogMode!==u.Scene.FOGMODE_NONE&&this._activeEffect.setMatrix("view",n.getViewMatrix()),(0,u.BindFogParameters)(n,i,this._activeEffect),this._lastTime+=n.getEngine().getDeltaTime(),this._activeEffect.setFloat("time",this._lastTime*this.speed/1e3),this.fogColor||(this.fogColor=u.Color3.Black()),this._activeEffect.setColor3("fogColor",this.fogColor),this._activeEffect.setFloat("fogDensity",this.fogDensity),this._activeEffect.setFloat("lowFrequencySpeed",this.lowFrequencySpeed),this._activeEffect.setFloat("movingSpeed",this.movingSpeed),this._afterBind(i,this._activeEffect,t))}},i.prototype.getAnimatables=function(){var e=[];return this.diffuseTexture&&this.diffuseTexture.animations&&this.diffuseTexture.animations.length>0&&e.push(this.diffuseTexture),this.noiseTexture&&this.noiseTexture.animations&&this.noiseTexture.animations.length>0&&e.push(this.noiseTexture),e},i.prototype.getActiveTextures=function(){var i=e.prototype.getActiveTextures.call(this);return this._diffuseTexture&&i.push(this._diffuseTexture),i},i.prototype.hasTexture=function(i){return!!e.prototype.hasTexture.call(this,i)||this.diffuseTexture===i},i.prototype.dispose=function(i){this.diffuseTexture&&this.diffuseTexture.dispose(),this.noiseTexture&&this.noiseTexture.dispose(),e.prototype.dispose.call(this,i)},i.prototype.clone=function(e){var t=this;return u.SerializationHelper.Clone((function(){return new i(e,t.getScene())}),this)},i.prototype.serialize=function(){var i=e.prototype.serialize.call(this);return i.customType="BABYLON.LavaMaterial",i},i.prototype.getClassName=function(){return"LavaMaterial"},i.Parse=function(e,t,n){return u.SerializationHelper.Parse((function(){return new i(e.name,t)}),e,t,n)},l([(0,u.serializeAsTexture)("diffuseTexture")],i.prototype,"_diffuseTexture",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture",void 0),l([(0,u.serializeAsTexture)()],i.prototype,"noiseTexture",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"fogColor",void 0),l([(0,u.serialize)()],i.prototype,"speed",void 0),l([(0,u.serialize)()],i.prototype,"movingSpeed",void 0),l([(0,u.serialize)()],i.prototype,"lowFrequencySpeed",void 0),l([(0,u.serialize)()],i.prototype,"fogDensity",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"diffuseColor",void 0),l([(0,u.serialize)("disableLighting")],i.prototype,"_disableLighting",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"disableLighting",void 0),l([(0,u.serialize)("unlit")],i.prototype,"_unlit",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"unlit",void 0),l([(0,u.serialize)("maxSimultaneousLights")],i.prototype,"_maxSimultaneousLights",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"maxSimultaneousLights",void 0),i}(u.PushMaterial);(0,u.RegisterClass)("BABYLON.LavaMaterial",L);u.ShaderStore.ShadersStore.mixPixelShader="precision highp float;uniform vec4 vEyePosition;uniform vec4 vDiffuseColor;\n#ifdef SPECULARTERM\nuniform vec4 vSpecularColor;\n#endif\nvarying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\n#endif\n#include<helperFunctions>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#ifdef DIFFUSE\nvarying vec2 vTextureUV;uniform sampler2D mixMap1Sampler;uniform vec2 vTextureInfos;\n#ifdef MIXMAP2\nuniform sampler2D mixMap2Sampler;\n#endif\nuniform sampler2D diffuse1Sampler;uniform sampler2D diffuse2Sampler;uniform sampler2D diffuse3Sampler;uniform sampler2D diffuse4Sampler;uniform vec2 diffuse1Infos;uniform vec2 diffuse2Infos;uniform vec2 diffuse3Infos;uniform vec2 diffuse4Infos;\n#ifdef MIXMAP2\nuniform sampler2D diffuse5Sampler;uniform sampler2D diffuse6Sampler;uniform sampler2D diffuse7Sampler;uniform sampler2D diffuse8Sampler;uniform vec2 diffuse5Infos;uniform vec2 diffuse6Infos;uniform vec2 diffuse7Infos;uniform vec2 diffuse8Infos;\n#endif\n#endif\n#include<lightsFragmentFunctions>\n#include<shadowsFragmentFunctions>\n#include<clipPlaneFragmentDeclaration>\n#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\n#include<logDepthDeclaration>\n#include<fogFragmentDeclaration>\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);vec4 finalMixColor=vec4(1.,1.,1.,1.);vec3 diffuseColor=vDiffuseColor.rgb;\n#ifdef MIXMAP2\nvec4 mixColor2=vec4(1.,1.,1.,1.);\n#endif\n#ifdef SPECULARTERM\nfloat glossiness=vSpecularColor.a;vec3 specularColor=vSpecularColor.rgb;\n#else\nfloat glossiness=0.;\n#endif\nfloat alpha=vDiffuseColor.a;\n#ifdef NORMAL\nvec3 normalW=normalize(vNormalW);\n#else\nvec3 normalW=vec3(1.0,1.0,1.0);\n#endif\n#ifdef DIFFUSE\nvec4 mixColor=texture2D(mixMap1Sampler,vTextureUV);\n#include<depthPrePass>\nmixColor.rgb*=vTextureInfos.y;vec4 diffuse1Color=texture2D(diffuse1Sampler,vTextureUV*diffuse1Infos);vec4 diffuse2Color=texture2D(diffuse2Sampler,vTextureUV*diffuse2Infos);vec4 diffuse3Color=texture2D(diffuse3Sampler,vTextureUV*diffuse3Infos);vec4 diffuse4Color=texture2D(diffuse4Sampler,vTextureUV*diffuse4Infos);diffuse1Color.rgb*=mixColor.r;diffuse2Color.rgb=mix(diffuse1Color.rgb,diffuse2Color.rgb,mixColor.g);diffuse3Color.rgb=mix(diffuse2Color.rgb,diffuse3Color.rgb,mixColor.b);finalMixColor.rgb=mix(diffuse3Color.rgb,diffuse4Color.rgb,1.0-mixColor.a);\n#ifdef MIXMAP2\nmixColor=texture2D(mixMap2Sampler,vTextureUV);mixColor.rgb*=vTextureInfos.y;vec4 diffuse5Color=texture2D(diffuse5Sampler,vTextureUV*diffuse5Infos);vec4 diffuse6Color=texture2D(diffuse6Sampler,vTextureUV*diffuse6Infos);vec4 diffuse7Color=texture2D(diffuse7Sampler,vTextureUV*diffuse7Infos);vec4 diffuse8Color=texture2D(diffuse8Sampler,vTextureUV*diffuse8Infos);diffuse5Color.rgb=mix(finalMixColor.rgb,diffuse5Color.rgb,mixColor.r);diffuse6Color.rgb=mix(diffuse5Color.rgb,diffuse6Color.rgb,mixColor.g);diffuse7Color.rgb=mix(diffuse6Color.rgb,diffuse7Color.rgb,mixColor.b);finalMixColor.rgb=mix(diffuse7Color.rgb,diffuse8Color.rgb,1.0-mixColor.a);\n#endif\n#endif\n#ifdef VERTEXCOLOR\nfinalMixColor.rgb*=vColor.rgb;\n#endif\nvec3 diffuseBase=vec3(0.,0.,0.);lightingInfo info;float shadow=1.;float aggShadow=0.;float numLights=0.;\n#ifdef SPECULARTERM\nvec3 specularBase=vec3(0.,0.,0.);\n#endif\n#include<lightFragment>[0..maxSimultaneousLights]\n#if defined(VERTEXALPHA) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nalpha*=vColor.a;\n#endif\n#ifdef SPECULARTERM\nvec3 finalSpecular=specularBase*specularColor;\n#else\nvec3 finalSpecular=vec3(0.0);\n#endif\nvec3 finalDiffuse=clamp(diffuseBase*diffuseColor*finalMixColor.rgb,0.0,1.0);vec4 color=vec4(finalDiffuse+finalSpecular,alpha);\n#include<logDepthFragment>\n#include<fogFragment>\ngl_FragColor=color;\n#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}\n";u.ShaderStore.ShadersStore.mixVertexShader="precision highp float;attribute vec3 position;\n#ifdef NORMAL\nattribute vec3 normal;\n#endif\n#ifdef UV1\nattribute vec2 uv;\n#endif\n#ifdef UV2\nattribute vec2 uv2;\n#endif\n#ifdef VERTEXCOLOR\nattribute vec4 color;\n#endif\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\n#include<instancesDeclaration>\nuniform mat4 view;uniform mat4 viewProjection;\n#ifdef DIFFUSE\nvarying vec2 vTextureUV;uniform mat4 textureMatrix;uniform vec2 vTextureInfos;\n#endif\n#ifdef POINTSIZE\nuniform float pointSize;\n#endif\nvarying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\n#endif\n#include<clipPlaneVertexDeclaration>\n#include<logDepthDeclaration>\n#include<fogVertexDeclaration>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\nvec4 worldPos=finalWorld*vec4(position,1.0);gl_Position=viewProjection*worldPos;vPositionW=vec3(worldPos);\n#ifdef NORMAL\nvNormalW=normalize(vec3(finalWorld*vec4(normal,0.0)));\n#endif\n#ifndef UV1\nvec2 uv=vec2(0.,0.);\n#endif\n#ifndef UV2\nvec2 uv2=vec2(0.,0.);\n#endif\n#ifdef DIFFUSE\nif (vTextureInfos.x==0.)\n{vTextureUV=vec2(textureMatrix*vec4(uv,1.0,0.0));}\nelse\n{vTextureUV=vec2(textureMatrix*vec4(uv2,1.0,0.0));}\n#endif\n#include<clipPlaneVertex>\n#include<fogVertex>\n#include<shadowsVertex>[0..maxSimultaneousLights]\n#include<vertexColorMixing>\n#if defined(POINTSIZE) && !defined(WEBGPU)\ngl_PointSize=pointSize;\n#endif\n#include<logDepthVertex>\n#define CUSTOM_VERTEX_MAIN_END\n}\n";var M=function(e){function i(){var i=e.call(this)||this;return i.DIFFUSE=!1,i.CLIPPLANE=!1,i.CLIPPLANE2=!1,i.CLIPPLANE3=!1,i.CLIPPLANE4=!1,i.CLIPPLANE5=!1,i.CLIPPLANE6=!1,i.ALPHATEST=!1,i.DEPTHPREPASS=!1,i.POINTSIZE=!1,i.FOG=!1,i.SPECULARTERM=!1,i.NORMAL=!1,i.UV1=!1,i.UV2=!1,i.VERTEXCOLOR=!1,i.VERTEXALPHA=!1,i.NUM_BONE_INFLUENCERS=0,i.BonesPerMesh=0,i.INSTANCES=!1,i.INSTANCESCOLOR=!1,i.MIXMAP2=!1,i.IMAGEPROCESSINGPOSTPROCESS=!1,i.SKIPFINALCOLORCLAMP=!1,i.LOGARITHMICDEPTH=!1,i.rebuild(),i}return f(i,e),i}(u.MaterialDefines),F=function(e){function i(i,t){var n=e.call(this,i,t)||this;return n.diffuseColor=new u.Color3(1,1,1),n.specularColor=new u.Color3(0,0,0),n.specularPower=64,n._disableLighting=!1,n._maxSimultaneousLights=4,n}return f(i,e),i.prototype.needAlphaBlending=function(){return this.alpha<1},i.prototype.needAlphaTesting=function(){return!1},i.prototype.getAlphaTestTexture=function(){return null},i.prototype.isReadyForSubMesh=function(e,i,t){var n=i._drawWrapper;if(this.isFrozen&&n.effect&&n._wasPreviouslyReady&&n._wasPreviouslyUsingInstances===t)return!0;i.materialDefines||(i.materialDefines=new M);var r=i.materialDefines,o=this.getScene();if(this._isReadyForSubMesh(i))return!0;var s=o.getEngine();if(o.texturesEnabled){if(!this._mixTexture1||!this._mixTexture1.isReady())return!1;if(r._needUVs=!0,u.MaterialFlags.DiffuseTextureEnabled){if(!this._diffuseTexture1||!this._diffuseTexture1.isReady())return!1;if(r.DIFFUSE=!0,!this._diffuseTexture2||!this._diffuseTexture2.isReady())return!1;if(!this._diffuseTexture3||!this._diffuseTexture3.isReady())return!1;if(!this._diffuseTexture4||!this._diffuseTexture4.isReady())return!1;if(this._mixTexture2){if(!this._mixTexture2.isReady())return!1;if(r.MIXMAP2=!0,!this._diffuseTexture5||!this._diffuseTexture5.isReady())return!1;if(!this._diffuseTexture6||!this._diffuseTexture6.isReady())return!1;if(!this._diffuseTexture7||!this._diffuseTexture7.isReady())return!1;if(!this._diffuseTexture8||!this._diffuseTexture8.isReady())return!1}}}if((0,u.PrepareDefinesForMisc)(e,o,this._useLogarithmicDepth,this.pointsCloud,this.fogEnabled,this._shouldTurnAlphaTestOn(e),r),r._needNormals=(0,u.PrepareDefinesForLights)(o,e,r,!1,this._maxSimultaneousLights,this._disableLighting),(0,u.PrepareDefinesForFrameBoundValues)(o,s,this,r,!!t),(0,u.PrepareDefinesForAttributes)(e,r,!0,!0),r.isDirty){r.markAsProcessed(),o.resetCachedMaterial();var a=new u.EffectFallbacks;r.FOG&&a.addFallback(1,"FOG"),(0,u.HandleFallbacksForShadows)(r,a,this.maxSimultaneousLights),r.NUM_BONE_INFLUENCERS>0&&a.addCPUSkinningFallback(0,e),r.IMAGEPROCESSINGPOSTPROCESS=o.imageProcessingConfiguration.applyByPostProcess;var f=[u.VertexBuffer.PositionKind];r.NORMAL&&f.push(u.VertexBuffer.NormalKind),r.UV1&&f.push(u.VertexBuffer.UVKind),r.UV2&&f.push(u.VertexBuffer.UV2Kind),r.VERTEXCOLOR&&f.push(u.VertexBuffer.ColorKind),(0,u.PrepareAttributesForBones)(f,e,r,a),(0,u.PrepareAttributesForInstances)(f,r);var l=r.toString(),d=["world","view","viewProjection","vEyePosition","vLightsType","vDiffuseColor","vSpecularColor","vFogInfos","vFogColor","pointSize","vTextureInfos","mBones","textureMatrix","logarithmicDepthConstant","diffuse1Infos","diffuse2Infos","diffuse3Infos","diffuse4Infos","diffuse5Infos","diffuse6Infos","diffuse7Infos","diffuse8Infos"],c=["mixMap1Sampler","mixMap2Sampler","diffuse1Sampler","diffuse2Sampler","diffuse3Sampler","diffuse4Sampler","diffuse5Sampler","diffuse6Sampler","diffuse7Sampler","diffuse8Sampler"],h=[];(0,u.addClipPlaneUniforms)(d),(0,u.PrepareUniformsAndSamplersList)({uniformsNames:d,uniformBuffersNames:h,samplers:c,defines:r,maxSimultaneousLights:this.maxSimultaneousLights}),i.setEffect(o.getEngine().createEffect("mix",{attributes:f,uniformsNames:d,uniformBuffersNames:h,samplers:c,defines:l,fallbacks:a,onCompiled:this.onCompiled,onError:this.onError,indexParameters:{maxSimultaneousLights:this.maxSimultaneousLights}},s),r,this._materialContext)}return!(!i.effect||!i.effect.isReady()||(r._renderId=o.getRenderId(),n._wasPreviouslyReady=!0,n._wasPreviouslyUsingInstances=!!t,0))},i.prototype.bindForSubMesh=function(e,i,t){var n=this.getScene(),r=t.materialDefines;if(r){var o=t.effect;o&&(this._activeEffect=o,this.bindOnlyWorldMatrix(e),this._activeEffect.setMatrix("viewProjection",n.getTransformMatrix()),(0,u.BindBonesParameters)(i,this._activeEffect),this._mustRebind(n,o,t)&&(this._mixTexture1&&(this._activeEffect.setTexture("mixMap1Sampler",this._mixTexture1),this._activeEffect.setFloat2("vTextureInfos",this._mixTexture1.coordinatesIndex,this._mixTexture1.level),this._activeEffect.setMatrix("textureMatrix",this._mixTexture1.getTextureMatrix()),u.MaterialFlags.DiffuseTextureEnabled&&(this._diffuseTexture1&&(this._activeEffect.setTexture("diffuse1Sampler",this._diffuseTexture1),this._activeEffect.setFloat2("diffuse1Infos",this._diffuseTexture1.uScale,this._diffuseTexture1.vScale)),this._diffuseTexture2&&(this._activeEffect.setTexture("diffuse2Sampler",this._diffuseTexture2),this._activeEffect.setFloat2("diffuse2Infos",this._diffuseTexture2.uScale,this._diffuseTexture2.vScale)),this._diffuseTexture3&&(this._activeEffect.setTexture("diffuse3Sampler",this._diffuseTexture3),this._activeEffect.setFloat2("diffuse3Infos",this._diffuseTexture3.uScale,this._diffuseTexture3.vScale)),this._diffuseTexture4&&(this._activeEffect.setTexture("diffuse4Sampler",this._diffuseTexture4),this._activeEffect.setFloat2("diffuse4Infos",this._diffuseTexture4.uScale,this._diffuseTexture4.vScale)))),this._mixTexture2&&(this._activeEffect.setTexture("mixMap2Sampler",this._mixTexture2),u.MaterialFlags.DiffuseTextureEnabled&&(this._diffuseTexture5&&(this._activeEffect.setTexture("diffuse5Sampler",this._diffuseTexture5),this._activeEffect.setFloat2("diffuse5Infos",this._diffuseTexture5.uScale,this._diffuseTexture5.vScale)),this._diffuseTexture6&&(this._activeEffect.setTexture("diffuse6Sampler",this._diffuseTexture6),this._activeEffect.setFloat2("diffuse6Infos",this._diffuseTexture6.uScale,this._diffuseTexture6.vScale)),this._diffuseTexture7&&(this._activeEffect.setTexture("diffuse7Sampler",this._diffuseTexture7),this._activeEffect.setFloat2("diffuse7Infos",this._diffuseTexture7.uScale,this._diffuseTexture7.vScale)),this._diffuseTexture8&&(this._activeEffect.setTexture("diffuse8Sampler",this._diffuseTexture8),this._activeEffect.setFloat2("diffuse8Infos",this._diffuseTexture8.uScale,this._diffuseTexture8.vScale)))),(0,u.bindClipPlane)(o,this,n),this.pointsCloud&&this._activeEffect.setFloat("pointSize",this.pointSize),this._useLogarithmicDepth&&(0,u.BindLogDepth)(r,o,n),n.bindEyePosition(o)),this._activeEffect.setColor4("vDiffuseColor",this.diffuseColor,this.alpha*i.visibility),r.SPECULARTERM&&this._activeEffect.setColor4("vSpecularColor",this.specularColor,this.specularPower),n.lightsEnabled&&!this.disableLighting&&(0,u.BindLights)(n,i,this._activeEffect,r,this.maxSimultaneousLights),n.fogEnabled&&i.applyFog&&n.fogMode!==u.Scene.FOGMODE_NONE&&this._activeEffect.setMatrix("view",n.getViewMatrix()),(0,u.BindFogParameters)(n,i,this._activeEffect),this._afterBind(i,this._activeEffect,t))}},i.prototype.getAnimatables=function(){var e=[];return this._mixTexture1&&this._mixTexture1.animations&&this._mixTexture1.animations.length>0&&e.push(this._mixTexture1),this._mixTexture2&&this._mixTexture2.animations&&this._mixTexture2.animations.length>0&&e.push(this._mixTexture2),e},i.prototype.getActiveTextures=function(){var i=e.prototype.getActiveTextures.call(this);return this._mixTexture1&&i.push(this._mixTexture1),this._diffuseTexture1&&i.push(this._diffuseTexture1),this._diffuseTexture2&&i.push(this._diffuseTexture2),this._diffuseTexture3&&i.push(this._diffuseTexture3),this._diffuseTexture4&&i.push(this._diffuseTexture4),this._mixTexture2&&i.push(this._mixTexture2),this._diffuseTexture5&&i.push(this._diffuseTexture5),this._diffuseTexture6&&i.push(this._diffuseTexture6),this._diffuseTexture7&&i.push(this._diffuseTexture7),this._diffuseTexture8&&i.push(this._diffuseTexture8),i},i.prototype.hasTexture=function(i){return!!e.prototype.hasTexture.call(this,i)||this._mixTexture1===i||this._diffuseTexture1===i||this._diffuseTexture2===i||this._diffuseTexture3===i||this._diffuseTexture4===i||this._mixTexture2===i||this._diffuseTexture5===i||this._diffuseTexture6===i||this._diffuseTexture7===i||this._diffuseTexture8===i},i.prototype.dispose=function(i){this._mixTexture1&&this._mixTexture1.dispose(),e.prototype.dispose.call(this,i)},i.prototype.clone=function(e){var t=this;return u.SerializationHelper.Clone((function(){return new i(e,t.getScene())}),this)},i.prototype.serialize=function(){var i=e.prototype.serialize.call(this);return i.customType="BABYLON.MixMaterial",i},i.prototype.getClassName=function(){return"MixMaterial"},i.Parse=function(e,t,n){return u.SerializationHelper.Parse((function(){return new i(e.name,t)}),e,t,n)},l([(0,u.serializeAsTexture)("mixTexture1")],i.prototype,"_mixTexture1",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"mixTexture1",void 0),l([(0,u.serializeAsTexture)("mixTexture2")],i.prototype,"_mixTexture2",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"mixTexture2",void 0),l([(0,u.serializeAsTexture)("diffuseTexture1")],i.prototype,"_diffuseTexture1",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture1",void 0),l([(0,u.serializeAsTexture)("diffuseTexture2")],i.prototype,"_diffuseTexture2",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture2",void 0),l([(0,u.serializeAsTexture)("diffuseTexture3")],i.prototype,"_diffuseTexture3",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture3",void 0),l([(0,u.serializeAsTexture)("diffuseTexture4")],i.prototype,"_diffuseTexture4",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture4",void 0),l([(0,u.serializeAsTexture)("diffuseTexture1")],i.prototype,"_diffuseTexture5",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture5",void 0),l([(0,u.serializeAsTexture)("diffuseTexture2")],i.prototype,"_diffuseTexture6",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture6",void 0),l([(0,u.serializeAsTexture)("diffuseTexture3")],i.prototype,"_diffuseTexture7",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture7",void 0),l([(0,u.serializeAsTexture)("diffuseTexture4")],i.prototype,"_diffuseTexture8",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture8",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"diffuseColor",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"specularColor",void 0),l([(0,u.serialize)()],i.prototype,"specularPower",void 0),l([(0,u.serialize)("disableLighting")],i.prototype,"_disableLighting",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"disableLighting",void 0),l([(0,u.serialize)("maxSimultaneousLights")],i.prototype,"_maxSimultaneousLights",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"maxSimultaneousLights",void 0),i}(u.PushMaterial);(0,u.RegisterClass)("BABYLON.MixMaterial",F);u.ShaderStore.ShadersStore.normalPixelShader="precision highp float;uniform vec4 vEyePosition;uniform vec4 vDiffuseColor;varying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#ifdef LIGHTING\n#include<helperFunctions>\n#include<__decl__lightFragment>[0]\n#include<__decl__lightFragment>[1]\n#include<__decl__lightFragment>[2]\n#include<__decl__lightFragment>[3]\n#include<lightsFragmentFunctions>\n#include<shadowsFragmentFunctions>\n#endif\n#ifdef DIFFUSE\nvarying vec2 vDiffuseUV;uniform sampler2D diffuseSampler;uniform vec2 vDiffuseInfos;\n#endif\n#include<clipPlaneFragmentDeclaration>\n#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\n#include<logDepthDeclaration>\n#include<fogFragmentDeclaration>\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);vec4 baseColor=vec4(1.,1.,1.,1.);vec3 diffuseColor=vDiffuseColor.rgb;float alpha=vDiffuseColor.a;\n#ifdef DIFFUSE\nbaseColor=texture2D(diffuseSampler,vDiffuseUV);\n#ifdef ALPHATEST\nif (baseColor.a<0.4)\ndiscard;\n#endif\n#include<depthPrePass>\nbaseColor.rgb*=vDiffuseInfos.y;\n#endif\n#ifdef NORMAL\nbaseColor=mix(baseColor,vec4(vNormalW,1.0),0.5);\n#endif\n#ifdef NORMAL\nvec3 normalW=normalize(vNormalW);\n#else\nvec3 normalW=vec3(1.0,1.0,1.0);\n#endif\n#ifdef LIGHTING\nvec3 diffuseBase=vec3(0.,0.,0.);lightingInfo info;float shadow=1.;float glossiness=0.;float aggShadow=0.;float numLights=0.;\n#include<lightFragment>[0]\n#include<lightFragment>[1]\n#include<lightFragment>[2]\n#include<lightFragment>[3]\nvec3 finalDiffuse=clamp(diffuseBase*diffuseColor,0.0,1.0)*baseColor.rgb;\n#else\nvec3 finalDiffuse= baseColor.rgb;\n#endif\nvec4 color=vec4(finalDiffuse,alpha);\n#include<logDepthFragment>\n#include<fogFragment>\ngl_FragColor=color;\n#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}";u.ShaderStore.ShadersStore.normalVertexShader="precision highp float;attribute vec3 position;\n#ifdef NORMAL\nattribute vec3 normal;\n#endif\n#ifdef UV1\nattribute vec2 uv;\n#endif\n#ifdef UV2\nattribute vec2 uv2;\n#endif\n#ifdef VERTEXCOLOR\nattribute vec4 color;\n#endif\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\n#include<instancesDeclaration>\nuniform mat4 view;uniform mat4 viewProjection;\n#ifdef DIFFUSE\nvarying vec2 vDiffuseUV;uniform mat4 diffuseMatrix;uniform vec2 vDiffuseInfos;\n#endif\n#ifdef POINTSIZE\nuniform float pointSize;\n#endif\nvarying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#include<clipPlaneVertexDeclaration>\n#include<logDepthDeclaration>\n#include<fogVertexDeclaration>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\nvec4 worldPos=finalWorld*vec4(position,1.0);gl_Position=viewProjection*worldPos;vPositionW=vec3(worldPos);\n#ifdef NORMAL\nvNormalW=normalize(vec3(finalWorld*vec4(normal,0.0)));\n#endif\n#ifndef UV1\nvec2 uv=vec2(0.,0.);\n#endif\n#ifndef UV2\nvec2 uv2=vec2(0.,0.);\n#endif\n#ifdef DIFFUSE\nif (vDiffuseInfos.x==0.)\n{vDiffuseUV=vec2(diffuseMatrix*vec4(uv,1.0,0.0));}\nelse\n{vDiffuseUV=vec2(diffuseMatrix*vec4(uv2,1.0,0.0));}\n#endif\n#include<clipPlaneVertex>\n#include<logDepthVertex>\n#include<fogVertex>\n#include<shadowsVertex>[0..maxSimultaneousLights]\n#if defined(POINTSIZE) && !defined(WEBGPU)\ngl_PointSize=pointSize;\n#endif\n#define CUSTOM_VERTEX_MAIN_END\n}\n";var b=function(e){function i(){var i=e.call(this)||this;return i.DIFFUSE=!1,i.CLIPPLANE=!1,i.CLIPPLANE2=!1,i.CLIPPLANE3=!1,i.CLIPPLANE4=!1,i.CLIPPLANE5=!1,i.CLIPPLANE6=!1,i.ALPHATEST=!1,i.DEPTHPREPASS=!1,i.POINTSIZE=!1,i.FOG=!1,i.LIGHT0=!1,i.LIGHT1=!1,i.LIGHT2=!1,i.LIGHT3=!1,i.SPOTLIGHT0=!1,i.SPOTLIGHT1=!1,i.SPOTLIGHT2=!1,i.SPOTLIGHT3=!1,i.HEMILIGHT0=!1,i.HEMILIGHT1=!1,i.HEMILIGHT2=!1,i.HEMILIGHT3=!1,i.DIRLIGHT0=!1,i.DIRLIGHT1=!1,i.DIRLIGHT2=!1,i.DIRLIGHT3=!1,i.POINTLIGHT0=!1,i.POINTLIGHT1=!1,i.POINTLIGHT2=!1,i.POINTLIGHT3=!1,i.SHADOW0=!1,i.SHADOW1=!1,i.SHADOW2=!1,i.SHADOW3=!1,i.SHADOWS=!1,i.SHADOWESM0=!1,i.SHADOWESM1=!1,i.SHADOWESM2=!1,i.SHADOWESM3=!1,i.SHADOWPOISSON0=!1,i.SHADOWPOISSON1=!1,i.SHADOWPOISSON2=!1,i.SHADOWPOISSON3=!1,i.SHADOWPCF0=!1,i.SHADOWPCF1=!1,i.SHADOWPCF2=!1,i.SHADOWPCF3=!1,i.SHADOWPCSS0=!1,i.SHADOWPCSS1=!1,i.SHADOWPCSS2=!1,i.SHADOWPCSS3=!1,i.NORMAL=!1,i.UV1=!1,i.UV2=!1,i.NUM_BONE_INFLUENCERS=0,i.BonesPerMesh=0,i.INSTANCES=!1,i.LIGHTING=!1,i.IMAGEPROCESSINGPOSTPROCESS=!1,i.SKIPFINALCOLORCLAMP=!1,i.LOGARITHMICDEPTH=!1,i.rebuild(),i}return f(i,e),i}(u.MaterialDefines),N=function(e){function i(i,t){var n=e.call(this,i,t)||this;return n.diffuseColor=new u.Color3(1,1,1),n._disableLighting=!1,n._maxSimultaneousLights=4,n}return f(i,e),i.prototype.needAlphaBlending=function(){return this.alpha<1},i.prototype.needAlphaBlendingForMesh=function(e){return this.needAlphaBlending()||e.visibility<1},i.prototype.needAlphaTesting=function(){return!1},i.prototype.getAlphaTestTexture=function(){return null},i.prototype.isReadyForSubMesh=function(e,i,t){var n=i._drawWrapper;if(this.isFrozen&&n.effect&&n._wasPreviouslyReady&&n._wasPreviouslyUsingInstances===t)return!0;i.materialDefines||(i.materialDefines=new b);var r=i.materialDefines,o=this.getScene();if(this._isReadyForSubMesh(i))return!0;var s=o.getEngine();if(r._areTexturesDirty&&(r._needUVs=!1,o.texturesEnabled&&this._diffuseTexture&&u.MaterialFlags.DiffuseTextureEnabled)){if(!this._diffuseTexture.isReady())return!1;r._needUVs=!0,r.DIFFUSE=!0}if((0,u.PrepareDefinesForMisc)(e,o,this._useLogarithmicDepth,this.pointsCloud,this.fogEnabled,this._shouldTurnAlphaTestOn(e),r),r._needNormals=!0,(0,u.PrepareDefinesForLights)(o,e,r,!1,this._maxSimultaneousLights,this._disableLighting),(0,u.PrepareDefinesForFrameBoundValues)(o,s,this,r,!!t),r.LIGHTING=!this._disableLighting,(0,u.PrepareDefinesForAttributes)(e,r,!0,!0),r.isDirty){r.markAsProcessed(),o.resetCachedMaterial();var a=new u.EffectFallbacks;r.FOG&&a.addFallback(1,"FOG"),(0,u.HandleFallbacksForShadows)(r,a),r.NUM_BONE_INFLUENCERS>0&&a.addCPUSkinningFallback(0,e),r.IMAGEPROCESSINGPOSTPROCESS=o.imageProcessingConfiguration.applyByPostProcess;var f=[u.VertexBuffer.PositionKind];r.NORMAL&&f.push(u.VertexBuffer.NormalKind),r.UV1&&f.push(u.VertexBuffer.UVKind),r.UV2&&f.push(u.VertexBuffer.UV2Kind),(0,u.PrepareAttributesForBones)(f,e,r,a),(0,u.PrepareAttributesForInstances)(f,r);var l=r.toString(),d=["world","view","viewProjection","vEyePosition","vLightsType","vDiffuseColor","vFogInfos","vFogColor","pointSize","vDiffuseInfos","mBones","diffuseMatrix","logarithmicDepthConstant"],c=["diffuseSampler"],h=[];(0,u.addClipPlaneUniforms)(d),(0,u.PrepareUniformsAndSamplersList)({uniformsNames:d,uniformBuffersNames:h,samplers:c,defines:r,maxSimultaneousLights:4}),i.setEffect(o.getEngine().createEffect("normal",{attributes:f,uniformsNames:d,uniformBuffersNames:h,samplers:c,defines:l,fallbacks:a,onCompiled:this.onCompiled,onError:this.onError,indexParameters:{maxSimultaneousLights:4}},s),r,this._materialContext)}return!(!i.effect||!i.effect.isReady()||(r._renderId=o.getRenderId(),n._wasPreviouslyReady=!0,n._wasPreviouslyUsingInstances=!!t,0))},i.prototype.bindForSubMesh=function(e,i,t){var n=this.getScene(),r=t.materialDefines;if(r){var o=t.effect;o&&(this._activeEffect=o,this.bindOnlyWorldMatrix(e),this._activeEffect.setMatrix("viewProjection",n.getTransformMatrix()),(0,u.BindBonesParameters)(i,this._activeEffect),this._mustRebind(n,o,t)&&(this.diffuseTexture&&u.MaterialFlags.DiffuseTextureEnabled&&(this._activeEffect.setTexture("diffuseSampler",this.diffuseTexture),this._activeEffect.setFloat2("vDiffuseInfos",this.diffuseTexture.coordinatesIndex,this.diffuseTexture.level),this._activeEffect.setMatrix("diffuseMatrix",this.diffuseTexture.getTextureMatrix())),(0,u.bindClipPlane)(o,this,n),this.pointsCloud&&this._activeEffect.setFloat("pointSize",this.pointSize),this._useLogarithmicDepth&&(0,u.BindLogDepth)(r,o,n),n.bindEyePosition(o)),this._activeEffect.setColor4("vDiffuseColor",this.diffuseColor,this.alpha*i.visibility),n.lightsEnabled&&!this.disableLighting&&(0,u.BindLights)(n,i,this._activeEffect,r),n.fogEnabled&&i.applyFog&&n.fogMode!==u.Scene.FOGMODE_NONE&&this._activeEffect.setMatrix("view",n.getViewMatrix()),(0,u.BindFogParameters)(n,i,this._activeEffect),this._afterBind(i,this._activeEffect,t))}},i.prototype.getAnimatables=function(){var e=[];return this.diffuseTexture&&this.diffuseTexture.animations&&this.diffuseTexture.animations.length>0&&e.push(this.diffuseTexture),e},i.prototype.getActiveTextures=function(){var i=e.prototype.getActiveTextures.call(this);return this._diffuseTexture&&i.push(this._diffuseTexture),i},i.prototype.hasTexture=function(i){return!!e.prototype.hasTexture.call(this,i)||this.diffuseTexture===i},i.prototype.dispose=function(i){this.diffuseTexture&&this.diffuseTexture.dispose(),e.prototype.dispose.call(this,i)},i.prototype.clone=function(e){var t=this;return u.SerializationHelper.Clone((function(){return new i(e,t.getScene())}),this)},i.prototype.serialize=function(){var i=e.prototype.serialize.call(this);return i.customType="BABYLON.NormalMaterial",i},i.prototype.getClassName=function(){return"NormalMaterial"},i.Parse=function(e,t,n){return u.SerializationHelper.Parse((function(){return new i(e.name,t)}),e,t,n)},l([(0,u.serializeAsTexture)("diffuseTexture")],i.prototype,"_diffuseTexture",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"diffuseColor",void 0),l([(0,u.serialize)("disableLighting")],i.prototype,"_disableLighting",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"disableLighting",void 0),l([(0,u.serialize)("maxSimultaneousLights")],i.prototype,"_maxSimultaneousLights",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"maxSimultaneousLights",void 0),i}(u.PushMaterial);(0,u.RegisterClass)("BABYLON.NormalMaterial",N);u.ShaderStore.ShadersStore.shadowOnlyPixelShader="precision highp float;uniform vec4 vEyePosition;uniform float alpha;uniform vec3 shadowColor;varying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#include<helperFunctions>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#include<lightsFragmentFunctions>\n#include<shadowsFragmentFunctions>\n#include<clipPlaneFragmentDeclaration>\n#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\n#include<logDepthDeclaration>\n#include<fogFragmentDeclaration>\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);\n#ifdef NORMAL\nvec3 normalW=normalize(vNormalW);\n#else\nvec3 normalW=vec3(1.0,1.0,1.0);\n#endif\nvec3 diffuseBase=vec3(0.,0.,0.);lightingInfo info;float shadow=1.;float glossiness=0.;float aggShadow=0.;float numLights=0.;\n#include<lightFragment>[0..1]\nvec4 color=vec4(shadowColor,(1.0-clamp(shadow,0.,1.))*alpha);\n#include<logDepthFragment>\n#include<fogFragment>\ngl_FragColor=color;\n#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}";u.ShaderStore.ShadersStore.shadowOnlyVertexShader="precision highp float;attribute vec3 position;\n#ifdef NORMAL\nattribute vec3 normal;\n#endif\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\n#include<instancesDeclaration>\nuniform mat4 view;uniform mat4 viewProjection;\n#ifdef POINTSIZE\nuniform float pointSize;\n#endif\nvarying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\n#endif\n#include<clipPlaneVertexDeclaration>\n#include<logDepthDeclaration>\n#include<fogVertexDeclaration>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\nvec4 worldPos=finalWorld*vec4(position,1.0);gl_Position=viewProjection*worldPos;vPositionW=vec3(worldPos);\n#ifdef NORMAL\nvNormalW=normalize(vec3(finalWorld*vec4(normal,0.0)));\n#endif\n#include<clipPlaneVertex>\n#include<logDepthVertex>\n#include<fogVertex>\n#include<shadowsVertex>[0..maxSimultaneousLights]\n#if defined(POINTSIZE) && !defined(WEBGPU)\ngl_PointSize=pointSize;\n#endif\n#define CUSTOM_VERTEX_MAIN_END\n}\n";var D=function(e){function i(){var i=e.call(this)||this;return i.CLIPPLANE=!1,i.CLIPPLANE2=!1,i.CLIPPLANE3=!1,i.CLIPPLANE4=!1,i.CLIPPLANE5=!1,i.CLIPPLANE6=!1,i.POINTSIZE=!1,i.FOG=!1,i.NORMAL=!1,i.NUM_BONE_INFLUENCERS=0,i.BonesPerMesh=0,i.INSTANCES=!1,i.IMAGEPROCESSINGPOSTPROCESS=!1,i.SKIPFINALCOLORCLAMP=!1,i.LOGARITHMICDEPTH=!1,i.rebuild(),i}return f(i,e),i}(u.MaterialDefines),O=function(e){function i(i,t){var n=e.call(this,i,t)||this;return n._needAlphaBlending=!0,n.shadowColor=u.Color3.Black(),n}return f(i,e),i.prototype.needAlphaBlending=function(){return this._needAlphaBlending},i.prototype.needAlphaTesting=function(){return!1},i.prototype.getAlphaTestTexture=function(){return null},Object.defineProperty(i.prototype,"activeLight",{get:function(){return this._activeLight},set:function(e){this._activeLight=e},enumerable:!1,configurable:!0}),i.prototype._getFirstShadowLightForMesh=function(e){for(var i=0,t=e.lightSources;i<t.length;i++){var n=t[i];if(n.shadowEnabled)return n}return null},i.prototype.isReadyForSubMesh=function(e,i,t){var n,r=i._drawWrapper;if(this.isFrozen&&r.effect&&r._wasPreviouslyReady&&r._wasPreviouslyUsingInstances===t)return!0;i.materialDefines||(i.materialDefines=new D);var o=i.materialDefines,s=this.getScene();if(this._isReadyForSubMesh(i))return!0;var a=s.getEngine();if(this._activeLight)for(var f=0,l=e.lightSources;f<l.length;f++){var d=l[f];if(d.shadowEnabled){if(this._activeLight===d)break;var c=e.lightSources.indexOf(this._activeLight);-1!==c&&(e.lightSources.splice(c,1),e.lightSources.splice(0,0,this._activeLight));break}}(0,u.PrepareDefinesForFrameBoundValues)(s,a,this,o,!!t),(0,u.PrepareDefinesForMisc)(e,s,this._useLogarithmicDepth,this.pointsCloud,this.fogEnabled,this._shouldTurnAlphaTestOn(e),o),o._needNormals=(0,u.PrepareDefinesForLights)(s,e,o,!1,1);var h=null===(n=this._getFirstShadowLightForMesh(e))||void 0===n?void 0:n.getShadowGenerator();if(this._needAlphaBlending=!0,h&&h.getClassName&&"CascadedShadowGenerator"===h.getClassName()){var p=h;this._needAlphaBlending=!p.autoCalcDepthBounds}if((0,u.PrepareDefinesForAttributes)(e,o,!1,!0),o.isDirty){o.markAsProcessed(),s.resetCachedMaterial();var m=new u.EffectFallbacks;o.FOG&&m.addFallback(1,"FOG"),(0,u.HandleFallbacksForShadows)(o,m,1),o.NUM_BONE_INFLUENCERS>0&&m.addCPUSkinningFallback(0,e),o.IMAGEPROCESSINGPOSTPROCESS=s.imageProcessingConfiguration.applyByPostProcess;var v=[u.VertexBuffer.PositionKind];o.NORMAL&&v.push(u.VertexBuffer.NormalKind),(0,u.PrepareAttributesForBones)(v,e,o,m),(0,u.PrepareAttributesForInstances)(v,o);var g=o.toString(),T=["world","view","viewProjection","vEyePosition","vLightsType","vFogInfos","vFogColor","pointSize","alpha","shadowColor","mBones","logarithmicDepthConstant"],_=[],x=[];(0,u.addClipPlaneUniforms)(T),(0,u.PrepareUniformsAndSamplersList)({uniformsNames:T,uniformBuffersNames:x,samplers:_,defines:o,maxSimultaneousLights:1}),i.setEffect(s.getEngine().createEffect("shadowOnly",{attributes:v,uniformsNames:T,uniformBuffersNames:x,samplers:_,defines:g,fallbacks:m,onCompiled:this.onCompiled,onError:this.onError,indexParameters:{maxSimultaneousLights:1}},a),o,this._materialContext)}return!(!i.effect||!i.effect.isReady()||(o._renderId=s.getRenderId(),r._wasPreviouslyReady=!0,r._wasPreviouslyUsingInstances=!!t,0))},i.prototype.bindForSubMesh=function(e,i,t){var n=this.getScene(),r=t.materialDefines;if(r){var o=t.effect;if(o){if(this._activeEffect=o,this.bindOnlyWorldMatrix(e),this._activeEffect.setMatrix("viewProjection",n.getTransformMatrix()),(0,u.BindBonesParameters)(i,this._activeEffect),this._mustRebind(n,o,t)&&((0,u.bindClipPlane)(o,this,n),this.pointsCloud&&this._activeEffect.setFloat("pointSize",this.pointSize),this._activeEffect.setFloat("alpha",this.alpha),this._activeEffect.setColor3("shadowColor",this.shadowColor),this._useLogarithmicDepth&&(0,u.BindLogDepth)(r,o,n),n.bindEyePosition(o)),n.lightsEnabled){(0,u.BindLights)(n,i,this._activeEffect,r,1);var s=this._getFirstShadowLightForMesh(i);s&&(s._renderId=-1)}(n.fogEnabled&&i.applyFog&&n.fogMode!==u.Scene.FOGMODE_NONE||r.SHADOWCSM0)&&this._activeEffect.setMatrix("view",n.getViewMatrix()),(0,u.BindFogParameters)(n,i,this._activeEffect),this._afterBind(i,this._activeEffect,t)}}},i.prototype.clone=function(e){var t=this;return u.SerializationHelper.Clone((function(){return new i(e,t.getScene())}),this)},i.prototype.serialize=function(){var i=e.prototype.serialize.call(this);return i.customType="BABYLON.ShadowOnlyMaterial",i},i.prototype.getClassName=function(){return"ShadowOnlyMaterial"},i.Parse=function(e,t,n){return u.SerializationHelper.Parse((function(){return new i(e.name,t)}),e,t,n)},i}(u.PushMaterial);(0,u.RegisterClass)("BABYLON.ShadowOnlyMaterial",O);u.ShaderStore.ShadersStore.simplePixelShader="precision highp float;uniform vec4 vEyePosition;uniform vec4 vDiffuseColor;varying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#if defined(VERTEXCOLOR) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nvarying vec4 vColor;\n#endif\n#include<helperFunctions>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#include<lightsFragmentFunctions>\n#include<shadowsFragmentFunctions>\n#ifdef DIFFUSE\nvarying vec2 vDiffuseUV;uniform sampler2D diffuseSampler;uniform vec2 vDiffuseInfos;\n#endif\n#include<clipPlaneFragmentDeclaration>\n#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\n#include<logDepthDeclaration>\n#include<fogFragmentDeclaration>\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);vec4 baseColor=vec4(1.,1.,1.,1.);vec3 diffuseColor=vDiffuseColor.rgb;float alpha=vDiffuseColor.a;\n#ifdef DIFFUSE\nbaseColor=texture2D(diffuseSampler,vDiffuseUV);\n#ifdef ALPHATEST\nif (baseColor.a<0.4)\ndiscard;\n#endif\n#include<depthPrePass>\nbaseColor.rgb*=vDiffuseInfos.y;\n#endif\n#if defined(VERTEXCOLOR) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nbaseColor.rgb*=vColor.rgb;\n#endif\n#ifdef NORMAL\nvec3 normalW=normalize(vNormalW);\n#else\nvec3 normalW=vec3(1.0,1.0,1.0);\n#endif\nvec3 diffuseBase=vec3(0.,0.,0.);lightingInfo info;float shadow=1.;float glossiness=0.;float aggShadow=0.;float numLights=0.;\n#ifdef SPECULARTERM\nvec3 specularBase=vec3(0.,0.,0.);\n#endif \n#include<lightFragment>[0..maxSimultaneousLights]\n#if defined(VERTEXALPHA) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nalpha*=vColor.a;\n#endif\nvec3 finalDiffuse=clamp(diffuseBase*diffuseColor,0.0,1.0)*baseColor.rgb;vec4 color=vec4(finalDiffuse,alpha);\n#include<logDepthFragment>\n#include<fogFragment>\ngl_FragColor=color;\n#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}";u.ShaderStore.ShadersStore.simpleVertexShader="precision highp float;attribute vec3 position;\n#ifdef NORMAL\nattribute vec3 normal;\n#endif\n#ifdef UV1\nattribute vec2 uv;\n#endif\n#ifdef UV2\nattribute vec2 uv2;\n#endif\n#ifdef VERTEXCOLOR\nattribute vec4 color;\n#endif\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\n#include<instancesDeclaration>\nuniform mat4 view;uniform mat4 viewProjection;\n#ifdef DIFFUSE\nvarying vec2 vDiffuseUV;uniform mat4 diffuseMatrix;uniform vec2 vDiffuseInfos;\n#endif\n#ifdef POINTSIZE\nuniform float pointSize;\n#endif\nvarying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#if defined(VERTEXCOLOR) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nvarying vec4 vColor;\n#endif\n#include<clipPlaneVertexDeclaration>\n#include<logDepthDeclaration>\n#include<fogVertexDeclaration>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\nvec4 worldPos=finalWorld*vec4(position,1.0);gl_Position=viewProjection*worldPos;vPositionW=vec3(worldPos);\n#ifdef NORMAL\nvNormalW=normalize(vec3(finalWorld*vec4(normal,0.0)));\n#endif\n#ifndef UV1\nvec2 uv=vec2(0.,0.);\n#endif\n#ifndef UV2\nvec2 uv2=vec2(0.,0.);\n#endif\n#ifdef DIFFUSE\nif (vDiffuseInfos.x==0.)\n{vDiffuseUV=vec2(diffuseMatrix*vec4(uv,1.0,0.0));}\nelse\n{vDiffuseUV=vec2(diffuseMatrix*vec4(uv2,1.0,0.0));}\n#endif\n#include<clipPlaneVertex>\n#include<logDepthVertex>\n#include<fogVertex>\n#include<shadowsVertex>[0..maxSimultaneousLights]\n#include<vertexColorMixing>\n#if defined(POINTSIZE) && !defined(WEBGPU)\ngl_PointSize=pointSize;\n#endif\n#define CUSTOM_VERTEX_MAIN_END\n}\n";var R=function(e){function i(){var i=e.call(this)||this;return i.DIFFUSE=!1,i.CLIPPLANE=!1,i.CLIPPLANE2=!1,i.CLIPPLANE3=!1,i.CLIPPLANE4=!1,i.CLIPPLANE5=!1,i.CLIPPLANE6=!1,i.ALPHATEST=!1,i.DEPTHPREPASS=!1,i.POINTSIZE=!1,i.FOG=!1,i.NORMAL=!1,i.UV1=!1,i.UV2=!1,i.VERTEXCOLOR=!1,i.VERTEXALPHA=!1,i.NUM_BONE_INFLUENCERS=0,i.BonesPerMesh=0,i.INSTANCES=!1,i.INSTANCESCOLOR=!1,i.IMAGEPROCESSINGPOSTPROCESS=!1,i.SKIPFINALCOLORCLAMP=!1,i.LOGARITHMICDEPTH=!1,i.rebuild(),i}return f(i,e),i}(u.MaterialDefines),U=function(e){function i(i,t){var n=e.call(this,i,t)||this;return n.diffuseColor=new u.Color3(1,1,1),n._disableLighting=!1,n._maxSimultaneousLights=4,n}return f(i,e),i.prototype.needAlphaBlending=function(){return this.alpha<1},i.prototype.needAlphaTesting=function(){return!1},i.prototype.getAlphaTestTexture=function(){return null},i.prototype.isReadyForSubMesh=function(e,i,t){var n=i._drawWrapper;if(this.isFrozen&&n.effect&&n._wasPreviouslyReady&&n._wasPreviouslyUsingInstances===t)return!0;i.materialDefines||(i.materialDefines=new R);var r=i.materialDefines,o=this.getScene();if(this._isReadyForSubMesh(i))return!0;var s=o.getEngine();if(r._areTexturesDirty&&(r._needUVs=!1,o.texturesEnabled&&this._diffuseTexture&&u.MaterialFlags.DiffuseTextureEnabled)){if(!this._diffuseTexture.isReady())return!1;r._needUVs=!0,r.DIFFUSE=!0}if((0,u.PrepareDefinesForMisc)(e,o,this._useLogarithmicDepth,this.pointsCloud,this.fogEnabled,this._shouldTurnAlphaTestOn(e),r),r._needNormals=(0,u.PrepareDefinesForLights)(o,e,r,!1,this._maxSimultaneousLights,this._disableLighting),(0,u.PrepareDefinesForFrameBoundValues)(o,s,this,r,!!t),(0,u.PrepareDefinesForAttributes)(e,r,!0,!0),r.isDirty){r.markAsProcessed(),o.resetCachedMaterial();var a=new u.EffectFallbacks;r.FOG&&a.addFallback(1,"FOG"),(0,u.HandleFallbacksForShadows)(r,a,this.maxSimultaneousLights),r.NUM_BONE_INFLUENCERS>0&&a.addCPUSkinningFallback(0,e),r.IMAGEPROCESSINGPOSTPROCESS=o.imageProcessingConfiguration.applyByPostProcess;var f=[u.VertexBuffer.PositionKind];r.NORMAL&&f.push(u.VertexBuffer.NormalKind),r.UV1&&f.push(u.VertexBuffer.UVKind),r.UV2&&f.push(u.VertexBuffer.UV2Kind),r.VERTEXCOLOR&&f.push(u.VertexBuffer.ColorKind),(0,u.PrepareAttributesForBones)(f,e,r,a),(0,u.PrepareAttributesForInstances)(f,r);var l=r.toString(),d=["world","view","viewProjection","vEyePosition","vLightsType","vDiffuseColor","vFogInfos","vFogColor","pointSize","vDiffuseInfos","mBones","diffuseMatrix","logarithmicDepthConstant"],c=["diffuseSampler"],h=[];(0,u.addClipPlaneUniforms)(d),(0,u.PrepareUniformsAndSamplersList)({uniformsNames:d,uniformBuffersNames:h,samplers:c,defines:r,maxSimultaneousLights:this.maxSimultaneousLights}),i.setEffect(o.getEngine().createEffect("simple",{attributes:f,uniformsNames:d,uniformBuffersNames:h,samplers:c,defines:l,fallbacks:a,onCompiled:this.onCompiled,onError:this.onError,indexParameters:{maxSimultaneousLights:this._maxSimultaneousLights-1}},s),r,this._materialContext)}return!(!i.effect||!i.effect.isReady()||(r._renderId=o.getRenderId(),n._wasPreviouslyReady=!0,n._wasPreviouslyUsingInstances=!!t,0))},i.prototype.bindForSubMesh=function(e,i,t){var n=this.getScene(),r=t.materialDefines;if(r){var o=t.effect;o&&(this._activeEffect=o,this.bindOnlyWorldMatrix(e),this._activeEffect.setMatrix("viewProjection",n.getTransformMatrix()),(0,u.BindBonesParameters)(i,this._activeEffect),this._mustRebind(n,o,t)&&(this._diffuseTexture&&u.MaterialFlags.DiffuseTextureEnabled&&(this._activeEffect.setTexture("diffuseSampler",this._diffuseTexture),this._activeEffect.setFloat2("vDiffuseInfos",this._diffuseTexture.coordinatesIndex,this._diffuseTexture.level),this._activeEffect.setMatrix("diffuseMatrix",this._diffuseTexture.getTextureMatrix())),(0,u.bindClipPlane)(o,this,n),this.pointsCloud&&this._activeEffect.setFloat("pointSize",this.pointSize),this._useLogarithmicDepth&&(0,u.BindLogDepth)(r,o,n),n.bindEyePosition(o)),this._activeEffect.setColor4("vDiffuseColor",this.diffuseColor,this.alpha*i.visibility),n.lightsEnabled&&!this.disableLighting&&(0,u.BindLights)(n,i,this._activeEffect,r,this.maxSimultaneousLights),n.fogEnabled&&i.applyFog&&n.fogMode!==u.Scene.FOGMODE_NONE&&this._activeEffect.setMatrix("view",n.getViewMatrix()),(0,u.BindFogParameters)(n,i,this._activeEffect),this._afterBind(i,this._activeEffect,t))}},i.prototype.getAnimatables=function(){var e=[];return this._diffuseTexture&&this._diffuseTexture.animations&&this._diffuseTexture.animations.length>0&&e.push(this._diffuseTexture),e},i.prototype.getActiveTextures=function(){var i=e.prototype.getActiveTextures.call(this);return this._diffuseTexture&&i.push(this._diffuseTexture),i},i.prototype.hasTexture=function(i){return!!e.prototype.hasTexture.call(this,i)||this.diffuseTexture===i},i.prototype.dispose=function(i){this._diffuseTexture&&this._diffuseTexture.dispose(),e.prototype.dispose.call(this,i)},i.prototype.clone=function(e){var t=this;return u.SerializationHelper.Clone((function(){return new i(e,t.getScene())}),this)},i.prototype.serialize=function(){var i=e.prototype.serialize.call(this);return i.customType="BABYLON.SimpleMaterial",i},i.prototype.getClassName=function(){return"SimpleMaterial"},i.Parse=function(e,t,n){return u.SerializationHelper.Parse((function(){return new i(e.name,t)}),e,t,n)},l([(0,u.serializeAsTexture)("diffuseTexture")],i.prototype,"_diffuseTexture",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture",void 0),l([(0,u.serializeAsColor3)("diffuse")],i.prototype,"diffuseColor",void 0),l([(0,u.serialize)("disableLighting")],i.prototype,"_disableLighting",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"disableLighting",void 0),l([(0,u.serialize)("maxSimultaneousLights")],i.prototype,"_maxSimultaneousLights",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"maxSimultaneousLights",void 0),i}(u.PushMaterial);(0,u.RegisterClass)("BABYLON.SimpleMaterial",U);u.ShaderStore.ShadersStore.skyPixelShader="precision highp float;varying vec3 vPositionW;\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\n#endif\n#include<clipPlaneFragmentDeclaration>\nuniform vec3 cameraPosition;uniform vec3 cameraOffset;uniform vec3 up;uniform float luminance;uniform float turbidity;uniform float rayleigh;uniform float mieCoefficient;uniform float mieDirectionalG;uniform vec3 sunPosition;\n#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\n#include<logDepthDeclaration>\n#include<fogFragmentDeclaration>\nconst float e=2.71828182845904523536028747135266249775724709369995957;const float pi=3.141592653589793238462643383279502884197169;const float n=1.0003;const float N=2.545E25;const float pn=0.035;const vec3 lambda=vec3(680E-9,550E-9,450E-9);const vec3 K=vec3(0.686,0.678,0.666);const float v=4.0;const float rayleighZenithLength=8.4E3;const float mieZenithLength=1.25E3;const float EE=1000.0;const float sunAngularDiameterCos=0.999956676946448443553574619906976478926848692873900859324;const float cutoffAngle=pi/1.95;const float steepness=1.5;vec3 totalRayleigh(vec3 lambda)\n{return (8.0*pow(pi,3.0)*pow(pow(n,2.0)-1.0,2.0)*(6.0+3.0*pn))/(3.0*N*pow(lambda,vec3(4.0))*(6.0-7.0*pn));}\nvec3 simplifiedRayleigh()\n{return 0.0005/vec3(94,40,18);}\nfloat rayleighPhase(float cosTheta)\n{ \nreturn (3.0/(16.0*pi))*(1.0+pow(cosTheta,2.0));}\nvec3 totalMie(vec3 lambda,vec3 K,float T)\n{float c=(0.2*T )*10E-18;return 0.434*c*pi*pow((2.0*pi)/lambda,vec3(v-2.0))*K;}\nfloat hgPhase(float cosTheta,float g)\n{return (1.0/(4.0*pi))*((1.0-pow(g,2.0))/pow(1.0-2.0*g*cosTheta+pow(g,2.0),1.5));}\nfloat sunIntensity(float zenithAngleCos)\n{return EE*max(0.0,1.0-exp((-(cutoffAngle-acos(zenithAngleCos))/steepness)));}\nfloat A=0.15;float B=0.50;float C=0.10;float D=0.20;float EEE=0.02;float F=0.30;float W=1000.0;vec3 Uncharted2Tonemap(vec3 x)\n{return ((x*(A*x+C*B)+D*EEE)/(x*(A*x+B)+D*F))-EEE/F;}\n#if DITHER\n#include<helperFunctions>\n#endif\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\n/**\n*--------------------------------------------------------------------------------------------------\n* Sky Color\n*--------------------------------------------------------------------------------------------------\n*/\nfloat sunfade=1.0-clamp(1.0-exp((sunPosition.y/450000.0)),0.0,1.0);float rayleighCoefficient=rayleigh-(1.0*(1.0-sunfade));vec3 sunDirection=normalize(sunPosition);float sunE=sunIntensity(dot(sunDirection,up));vec3 betaR=simplifiedRayleigh()*rayleighCoefficient;vec3 betaM=totalMie(lambda,K,turbidity)*mieCoefficient;float zenithAngle=acos(max(0.0,dot(up,normalize(vPositionW-cameraPosition+cameraOffset))));float sR=rayleighZenithLength/(cos(zenithAngle)+0.15*pow(93.885-((zenithAngle*180.0)/pi),-1.253));float sM=mieZenithLength/(cos(zenithAngle)+0.15*pow(93.885-((zenithAngle*180.0)/pi),-1.253));vec3 Fex=exp(-(betaR*sR+betaM*sM));float cosTheta=dot(normalize(vPositionW-cameraPosition),sunDirection);float rPhase=rayleighPhase(cosTheta*0.5+0.5);vec3 betaRTheta=betaR*rPhase;float mPhase=hgPhase(cosTheta,mieDirectionalG);vec3 betaMTheta=betaM*mPhase;vec3 Lin=pow(sunE*((betaRTheta+betaMTheta)/(betaR+betaM))*(1.0-Fex),vec3(1.5));Lin*=mix(vec3(1.0),pow(sunE*((betaRTheta+betaMTheta)/(betaR+betaM))*Fex,vec3(1.0/2.0)),clamp(pow(1.0-dot(up,sunDirection),5.0),0.0,1.0));vec3 direction=normalize(vPositionW-cameraPosition);float theta=acos(direction.y);float phi=atan(direction.z,direction.x);vec2 uv=vec2(phi,theta)/vec2(2.0*pi,pi)+vec2(0.5,0.0);vec3 L0=vec3(0.1)*Fex;float sundisk=smoothstep(sunAngularDiameterCos,sunAngularDiameterCos+0.00002,cosTheta);L0+=(sunE*19000.0*Fex)*sundisk;vec3 whiteScale=1.0/Uncharted2Tonemap(vec3(W));vec3 texColor=(Lin+L0);texColor*=0.04 ;texColor+=vec3(0.0,0.001,0.0025)*0.3;float g_fMaxLuminance=1.0;float fLumScaled=0.1/luminance; \nfloat fLumCompressed=(fLumScaled*(1.0+(fLumScaled/(g_fMaxLuminance*g_fMaxLuminance))))/(1.0+fLumScaled); \nfloat ExposureBias=fLumCompressed;vec3 curr=Uncharted2Tonemap((log2(2.0/pow(luminance,4.0)))*texColor);vec3 retColor=curr*whiteScale;/**\n*--------------------------------------------------------------------------------------------------\n* Sky Color\n*--------------------------------------------------------------------------------------------------\n*/\nfloat alpha=1.0;\n#ifdef VERTEXCOLOR\nretColor.rgb*=vColor.rgb;\n#endif\n#if defined(VERTEXALPHA) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nalpha*=vColor.a;\n#endif\n#if DITHER\nretColor.rgb+=dither(gl_FragCoord.xy,0.5);\n#endif\nvec4 color=clamp(vec4(retColor.rgb,alpha),0.0,1.0);\n#include<logDepthFragment>\n#include<fogFragment>\ngl_FragColor=color;\n#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}\n";u.ShaderStore.ShadersStore.skyVertexShader="precision highp float;attribute vec3 position;\n#ifdef VERTEXCOLOR\nattribute vec4 color;\n#endif\nuniform mat4 world;uniform mat4 view;uniform mat4 viewProjection;\n#ifdef POINTSIZE\nuniform float pointSize;\n#endif\nvarying vec3 vPositionW;\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\n#endif\n#include<logDepthDeclaration>\n#include<clipPlaneVertexDeclaration>\n#include<fogVertexDeclaration>\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\ngl_Position=viewProjection*world*vec4(position,1.0);vec4 worldPos=world*vec4(position,1.0);vPositionW=vec3(worldPos);\n#include<clipPlaneVertex>\n#include<logDepthVertex>\n#include<fogVertex>\n#ifdef VERTEXCOLOR\nvColor=color;\n#endif\n#if defined(POINTSIZE) && !defined(WEBGPU)\ngl_PointSize=pointSize;\n#endif\n#define CUSTOM_VERTEX_MAIN_END\n}\n";var V=function(e){function i(){var i=e.call(this)||this;return i.CLIPPLANE=!1,i.CLIPPLANE2=!1,i.CLIPPLANE3=!1,i.CLIPPLANE4=!1,i.CLIPPLANE5=!1,i.CLIPPLANE6=!1,i.POINTSIZE=!1,i.FOG=!1,i.VERTEXCOLOR=!1,i.VERTEXALPHA=!1,i.IMAGEPROCESSINGPOSTPROCESS=!1,i.SKIPFINALCOLORCLAMP=!1,i.DITHER=!1,i.LOGARITHMICDEPTH=!1,i.rebuild(),i}return f(i,e),i}(u.MaterialDefines),w=function(e){function i(i,t){var n=e.call(this,i,t)||this;return n.luminance=1,n.turbidity=10,n.rayleigh=2,n.mieCoefficient=.005,n.mieDirectionalG=.8,n.distance=500,n.inclination=.49,n.azimuth=.25,n.sunPosition=new u.Vector3(0,100,0),n.useSunPosition=!1,n.cameraOffset=u.Vector3.Zero(),n.up=u.Vector3.Up(),n.dithering=!1,n._cameraPosition=u.Vector3.Zero(),n._skyOrientation=new u.Quaternion,n}return f(i,e),i.prototype.needAlphaBlending=function(){return this.alpha<1},i.prototype.needAlphaTesting=function(){return!1},i.prototype.getAlphaTestTexture=function(){return null},i.prototype.isReadyForSubMesh=function(e,i){var t=i._drawWrapper;if(this.isFrozen&&t.effect&&t._wasPreviouslyReady)return!0;i.materialDefines||(i.materialDefines=new V);var n=i.materialDefines,r=this.getScene();if(this._isReadyForSubMesh(i))return!0;if((0,u.PrepareDefinesForMisc)(e,r,this._useLogarithmicDepth,this.pointsCloud,this.fogEnabled,!1,n),(0,u.PrepareDefinesForAttributes)(e,n,!0,!1),n.IMAGEPROCESSINGPOSTPROCESS!==r.imageProcessingConfiguration.applyByPostProcess&&n.markAsMiscDirty(),n.DITHER!==this.dithering&&n.markAsMiscDirty(),n.isDirty){n.markAsProcessed(),r.resetCachedMaterial();var o=new u.EffectFallbacks;n.FOG&&o.addFallback(1,"FOG"),n.IMAGEPROCESSINGPOSTPROCESS=r.imageProcessingConfiguration.applyByPostProcess,n.DITHER=this.dithering;var s=[u.VertexBuffer.PositionKind];n.VERTEXCOLOR&&s.push(u.VertexBuffer.ColorKind);var a=["world","viewProjection","view","vFogInfos","vFogColor","logarithmicDepthConstant","pointSize","luminance","turbidity","rayleigh","mieCoefficient","mieDirectionalG","sunPosition","cameraPosition","cameraOffset","up"];(0,u.addClipPlaneUniforms)(a);var f=n.toString();i.setEffect(r.getEngine().createEffect("sky",s,a,[],f,o,this.onCompiled,this.onError),n,this._materialContext)}return!(!i.effect||!i.effect.isReady()||(n._renderId=r.getRenderId(),t._wasPreviouslyReady=!0,0))},i.prototype.bindForSubMesh=function(e,i,t){var n=this.getScene(),r=t.materialDefines;if(r){var o=t.effect;if(o){this._activeEffect=o,this.bindOnlyWorldMatrix(e),this._activeEffect.setMatrix("viewProjection",n.getTransformMatrix()),this._mustRebind(n,o,t)&&((0,u.bindClipPlane)(o,this,n),this.pointsCloud&&this._activeEffect.setFloat("pointSize",this.pointSize),this._useLogarithmicDepth&&(0,u.BindLogDepth)(r,o,n)),n.fogEnabled&&i.applyFog&&n.fogMode!==u.Scene.FOGMODE_NONE&&this._activeEffect.setMatrix("view",n.getViewMatrix()),(0,u.BindFogParameters)(n,i,this._activeEffect);var s=n.activeCamera;if(s){var a=s.getWorldMatrix();this._cameraPosition.x=a.m[12],this._cameraPosition.y=a.m[13],this._cameraPosition.z=a.m[14],this._activeEffect.setVector3("cameraPosition",this._cameraPosition)}if(this._activeEffect.setVector3("cameraOffset",this.cameraOffset),this._activeEffect.setVector3("up",this.up),this.luminance>0&&this._activeEffect.setFloat("luminance",this.luminance),this._activeEffect.setFloat("turbidity",this.turbidity),this._activeEffect.setFloat("rayleigh",this.rayleigh),this._activeEffect.setFloat("mieCoefficient",this.mieCoefficient),this._activeEffect.setFloat("mieDirectionalG",this.mieDirectionalG),!this.useSunPosition){var f=Math.PI*(this.inclination-.5),l=2*Math.PI*(this.azimuth-.5);this.sunPosition.x=this.distance*Math.cos(l)*Math.cos(f),this.sunPosition.y=this.distance*Math.sin(-f),this.sunPosition.z=this.distance*Math.sin(l)*Math.cos(f),u.Quaternion.FromUnitVectorsToRef(u.Vector3.UpReadOnly,this.up,this._skyOrientation),this.sunPosition.rotateByQuaternionToRef(this._skyOrientation,this.sunPosition)}this._activeEffect.setVector3("sunPosition",this.sunPosition),this._afterBind(i,this._activeEffect,t)}}},i.prototype.getAnimatables=function(){return[]},i.prototype.dispose=function(i){e.prototype.dispose.call(this,i)},i.prototype.clone=function(e){var t=this;return u.SerializationHelper.Clone((function(){return new i(e,t.getScene())}),this)},i.prototype.serialize=function(){var i=e.prototype.serialize.call(this);return i.customType="BABYLON.SkyMaterial",i},i.prototype.getClassName=function(){return"SkyMaterial"},i.Parse=function(e,t,n){return u.SerializationHelper.Parse((function(){return new i(e.name,t)}),e,t,n)},l([(0,u.serialize)()],i.prototype,"luminance",void 0),l([(0,u.serialize)()],i.prototype,"turbidity",void 0),l([(0,u.serialize)()],i.prototype,"rayleigh",void 0),l([(0,u.serialize)()],i.prototype,"mieCoefficient",void 0),l([(0,u.serialize)()],i.prototype,"mieDirectionalG",void 0),l([(0,u.serialize)()],i.prototype,"distance",void 0),l([(0,u.serialize)()],i.prototype,"inclination",void 0),l([(0,u.serialize)()],i.prototype,"azimuth",void 0),l([(0,u.serializeAsVector3)()],i.prototype,"sunPosition",void 0),l([(0,u.serialize)()],i.prototype,"useSunPosition",void 0),l([(0,u.serializeAsVector3)()],i.prototype,"cameraOffset",void 0),l([(0,u.serializeAsVector3)()],i.prototype,"up",void 0),l([(0,u.serialize)()],i.prototype,"dithering",void 0),i}(u.PushMaterial);(0,u.RegisterClass)("BABYLON.SkyMaterial",w);u.ShaderStore.ShadersStore.terrainPixelShader="precision highp float;uniform vec4 vEyePosition;uniform vec4 vDiffuseColor;\n#ifdef SPECULARTERM\nuniform vec4 vSpecularColor;\n#endif\nvarying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#if defined(VERTEXCOLOR) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nvarying vec4 vColor;\n#endif\n#include<helperFunctions>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#ifdef DIFFUSE\nvarying vec2 vTextureUV;uniform sampler2D textureSampler;uniform vec2 vTextureInfos;uniform sampler2D diffuse1Sampler;uniform sampler2D diffuse2Sampler;uniform sampler2D diffuse3Sampler;uniform vec2 diffuse1Infos;uniform vec2 diffuse2Infos;uniform vec2 diffuse3Infos;\n#endif\n#ifdef BUMP\nuniform sampler2D bump1Sampler;uniform sampler2D bump2Sampler;uniform sampler2D bump3Sampler;\n#endif\n#include<lightsFragmentFunctions>\n#include<shadowsFragmentFunctions>\n#include<clipPlaneFragmentDeclaration>\n#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\n#include<logDepthDeclaration>\n#include<fogFragmentDeclaration>\n#ifdef BUMP\n#extension GL_OES_standard_derivatives : enable\nmat3 cotangent_frame(vec3 normal,vec3 p,vec2 uv)\n{vec3 dp1=dFdx(p);vec3 dp2=dFdy(p);vec2 duv1=dFdx(uv);vec2 duv2=dFdy(uv);vec3 dp2perp=cross(dp2,normal);vec3 dp1perp=cross(normal,dp1);vec3 tangent=dp2perp*duv1.x+dp1perp*duv2.x;vec3 binormal=dp2perp*duv1.y+dp1perp*duv2.y;float invmax=inversesqrt(max(dot(tangent,tangent),dot(binormal,binormal)));return mat3(tangent*invmax,binormal*invmax,normal);}\nvec3 perturbNormal(vec3 viewDir,vec3 mixColor)\n{vec3 bump1Color=texture2D(bump1Sampler,vTextureUV*diffuse1Infos).xyz;vec3 bump2Color=texture2D(bump2Sampler,vTextureUV*diffuse2Infos).xyz;vec3 bump3Color=texture2D(bump3Sampler,vTextureUV*diffuse3Infos).xyz;bump1Color.rgb*=mixColor.r;bump2Color.rgb=mix(bump1Color.rgb,bump2Color.rgb,mixColor.g);vec3 map=mix(bump2Color.rgb,bump3Color.rgb,mixColor.b);map=map*255./127.-128./127.;mat3 TBN=cotangent_frame(vNormalW*vTextureInfos.y,-viewDir,vTextureUV);return normalize(TBN*map);}\n#endif\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);vec4 baseColor=vec4(1.,1.,1.,1.);vec3 diffuseColor=vDiffuseColor.rgb;\n#ifdef SPECULARTERM\nfloat glossiness=vSpecularColor.a;vec3 specularColor=vSpecularColor.rgb;\n#else\nfloat glossiness=0.;\n#endif\nfloat alpha=vDiffuseColor.a;\n#ifdef NORMAL\nvec3 normalW=normalize(vNormalW);\n#else\nvec3 normalW=vec3(1.0,1.0,1.0);\n#endif\n#ifdef DIFFUSE\nbaseColor=texture2D(textureSampler,vTextureUV);\n#if defined(BUMP) && defined(DIFFUSE)\nnormalW=perturbNormal(viewDirectionW,baseColor.rgb);\n#endif\n#ifdef ALPHATEST\nif (baseColor.a<0.4)\ndiscard;\n#endif\n#include<depthPrePass>\nbaseColor.rgb*=vTextureInfos.y;vec4 diffuse1Color=texture2D(diffuse1Sampler,vTextureUV*diffuse1Infos);vec4 diffuse2Color=texture2D(diffuse2Sampler,vTextureUV*diffuse2Infos);vec4 diffuse3Color=texture2D(diffuse3Sampler,vTextureUV*diffuse3Infos);diffuse1Color.rgb*=baseColor.r;diffuse2Color.rgb=mix(diffuse1Color.rgb,diffuse2Color.rgb,baseColor.g);baseColor.rgb=mix(diffuse2Color.rgb,diffuse3Color.rgb,baseColor.b);\n#endif\n#if defined(VERTEXCOLOR) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nbaseColor.rgb*=vColor.rgb;\n#endif\nvec3 diffuseBase=vec3(0.,0.,0.);lightingInfo info;float shadow=1.;float aggShadow=0.;float numLights=0.;\n#ifdef SPECULARTERM\nvec3 specularBase=vec3(0.,0.,0.);\n#endif\n#include<lightFragment>[0..maxSimultaneousLights]\n#if defined(VERTEXALPHA) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nalpha*=vColor.a;\n#endif\n#ifdef SPECULARTERM\nvec3 finalSpecular=specularBase*specularColor;\n#else\nvec3 finalSpecular=vec3(0.0);\n#endif\nvec3 finalDiffuse=clamp(diffuseBase*diffuseColor*baseColor.rgb,0.0,1.0);vec4 color=vec4(finalDiffuse+finalSpecular,alpha);\n#include<logDepthFragment>\n#include<fogFragment>\ngl_FragColor=color;\n#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}\n";u.ShaderStore.ShadersStore.terrainVertexShader="precision highp float;attribute vec3 position;\n#ifdef NORMAL\nattribute vec3 normal;\n#endif\n#ifdef UV1\nattribute vec2 uv;\n#endif\n#ifdef UV2\nattribute vec2 uv2;\n#endif\n#ifdef VERTEXCOLOR\nattribute vec4 color;\n#endif\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\n#include<instancesDeclaration>\nuniform mat4 view;uniform mat4 viewProjection;\n#ifdef DIFFUSE\nvarying vec2 vTextureUV;uniform mat4 textureMatrix;uniform vec2 vTextureInfos;\n#endif\n#ifdef POINTSIZE\nuniform float pointSize;\n#endif\nvarying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#if defined(VERTEXCOLOR) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nvarying vec4 vColor;\n#endif\n#include<logDepthDeclaration>\n#include<clipPlaneVertexDeclaration>\n#include<fogVertexDeclaration>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\nvec4 worldPos=finalWorld*vec4(position,1.0);gl_Position=viewProjection*worldPos;vPositionW=vec3(worldPos);\n#ifdef NORMAL\nvNormalW=normalize(vec3(finalWorld*vec4(normal,0.0)));\n#endif\n#ifndef UV1\nvec2 uv=vec2(0.,0.);\n#endif\n#ifndef UV2\nvec2 uv2=vec2(0.,0.);\n#endif\n#ifdef DIFFUSE\nif (vTextureInfos.x==0.)\n{vTextureUV=vec2(textureMatrix*vec4(uv,1.0,0.0));}\nelse\n{vTextureUV=vec2(textureMatrix*vec4(uv2,1.0,0.0));}\n#endif\n#include<clipPlaneVertex>\n#include<logDepthVertex>\n#include<fogVertex>\n#include<shadowsVertex>[0..maxSimultaneousLights]\n#include<vertexColorMixing>\n#if defined(POINTSIZE) && !defined(WEBGPU)\ngl_PointSize=pointSize;\n#endif\n#define CUSTOM_VERTEX_MAIN_END\n}\n";var B=function(e){function i(){var i=e.call(this)||this;return i.DIFFUSE=!1,i.BUMP=!1,i.CLIPPLANE=!1,i.CLIPPLANE2=!1,i.CLIPPLANE3=!1,i.CLIPPLANE4=!1,i.CLIPPLANE5=!1,i.CLIPPLANE6=!1,i.ALPHATEST=!1,i.DEPTHPREPASS=!1,i.POINTSIZE=!1,i.FOG=!1,i.SPECULARTERM=!1,i.NORMAL=!1,i.UV1=!1,i.UV2=!1,i.VERTEXCOLOR=!1,i.VERTEXALPHA=!1,i.NUM_BONE_INFLUENCERS=0,i.BonesPerMesh=0,i.INSTANCES=!1,i.INSTANCESCOLOR=!1,i.IMAGEPROCESSINGPOSTPROCESS=!1,i.SKIPFINALCOLORCLAMP=!1,i.LOGARITHMICDEPTH=!1,i.rebuild(),i}return f(i,e),i}(u.MaterialDefines),z=function(e){function i(i,t){var n=e.call(this,i,t)||this;return n.diffuseColor=new u.Color3(1,1,1),n.specularColor=new u.Color3(0,0,0),n.specularPower=64,n._disableLighting=!1,n._maxSimultaneousLights=4,n}return f(i,e),i.prototype.needAlphaBlending=function(){return this.alpha<1},i.prototype.needAlphaTesting=function(){return!1},i.prototype.getAlphaTestTexture=function(){return null},i.prototype.isReadyForSubMesh=function(e,i,t){var n=i._drawWrapper;if(this.isFrozen&&n.effect&&n._wasPreviouslyReady&&n._wasPreviouslyUsingInstances===t)return!0;i.materialDefines||(i.materialDefines=new B);var r=i.materialDefines,o=this.getScene();if(this._isReadyForSubMesh(i))return!0;var s=o.getEngine();if(o.texturesEnabled){if(!this.mixTexture||!this.mixTexture.isReady())return!1;if(r._needUVs=!0,u.MaterialFlags.DiffuseTextureEnabled){if(!this.diffuseTexture1||!this.diffuseTexture1.isReady())return!1;if(!this.diffuseTexture2||!this.diffuseTexture2.isReady())return!1;if(!this.diffuseTexture3||!this.diffuseTexture3.isReady())return!1;r.DIFFUSE=!0}if(this.bumpTexture1&&this.bumpTexture2&&this.bumpTexture3&&u.MaterialFlags.BumpTextureEnabled){if(!this.bumpTexture1.isReady())return!1;if(!this.bumpTexture2.isReady())return!1;if(!this.bumpTexture3.isReady())return!1;r._needNormals=!0,r.BUMP=!0}}if((0,u.PrepareDefinesForMisc)(e,o,this._useLogarithmicDepth,this.pointsCloud,this.fogEnabled,this._shouldTurnAlphaTestOn(e),r),r._needNormals=(0,u.PrepareDefinesForLights)(o,e,r,!1,this._maxSimultaneousLights,this._disableLighting),(0,u.PrepareDefinesForFrameBoundValues)(o,s,this,r,!!t),(0,u.PrepareDefinesForAttributes)(e,r,!0,!0),r.isDirty){r.markAsProcessed(),o.resetCachedMaterial();var a=new u.EffectFallbacks;r.FOG&&a.addFallback(1,"FOG"),(0,u.HandleFallbacksForShadows)(r,a,this.maxSimultaneousLights),r.NUM_BONE_INFLUENCERS>0&&a.addCPUSkinningFallback(0,e),r.IMAGEPROCESSINGPOSTPROCESS=o.imageProcessingConfiguration.applyByPostProcess;var f=[u.VertexBuffer.PositionKind];r.NORMAL&&f.push(u.VertexBuffer.NormalKind),r.UV1&&f.push(u.VertexBuffer.UVKind),r.UV2&&f.push(u.VertexBuffer.UV2Kind),r.VERTEXCOLOR&&f.push(u.VertexBuffer.ColorKind),(0,u.PrepareAttributesForBones)(f,e,r,a),(0,u.PrepareAttributesForInstances)(f,r);var l=r.toString(),d=["world","view","viewProjection","vEyePosition","vLightsType","vDiffuseColor","vSpecularColor","vFogInfos","vFogColor","pointSize","vTextureInfos","mBones","textureMatrix","diffuse1Infos","diffuse2Infos","diffuse3Infos"],c=["textureSampler","diffuse1Sampler","diffuse2Sampler","diffuse3Sampler","bump1Sampler","bump2Sampler","bump3Sampler","logarithmicDepthConstant"],h=[];(0,u.addClipPlaneUniforms)(d),(0,u.PrepareUniformsAndSamplersList)({uniformsNames:d,uniformBuffersNames:h,samplers:c,defines:r,maxSimultaneousLights:this.maxSimultaneousLights}),i.setEffect(o.getEngine().createEffect("terrain",{attributes:f,uniformsNames:d,uniformBuffersNames:h,samplers:c,defines:l,fallbacks:a,onCompiled:this.onCompiled,onError:this.onError,indexParameters:{maxSimultaneousLights:this.maxSimultaneousLights}},s),r,this._materialContext)}return!(!i.effect||!i.effect.isReady()||(r._renderId=o.getRenderId(),n._wasPreviouslyReady=!0,n._wasPreviouslyUsingInstances=!!t,0))},i.prototype.bindForSubMesh=function(e,i,t){var n=this.getScene(),r=t.materialDefines;if(r){var o=t.effect;o&&(this._activeEffect=o,this.bindOnlyWorldMatrix(e),this._activeEffect.setMatrix("view",n.getViewMatrix()),this._activeEffect.setMatrix("viewProjection",n.getTransformMatrix()),(0,u.BindBonesParameters)(i,this._activeEffect),this._mustRebind(n,o,t)&&(this.mixTexture&&(this._activeEffect.setTexture("textureSampler",this._mixTexture),this._activeEffect.setFloat2("vTextureInfos",this._mixTexture.coordinatesIndex,this._mixTexture.level),this._activeEffect.setMatrix("textureMatrix",this._mixTexture.getTextureMatrix()),u.MaterialFlags.DiffuseTextureEnabled&&(this._diffuseTexture1&&(this._activeEffect.setTexture("diffuse1Sampler",this._diffuseTexture1),this._activeEffect.setFloat2("diffuse1Infos",this._diffuseTexture1.uScale,this._diffuseTexture1.vScale)),this._diffuseTexture2&&(this._activeEffect.setTexture("diffuse2Sampler",this._diffuseTexture2),this._activeEffect.setFloat2("diffuse2Infos",this._diffuseTexture2.uScale,this._diffuseTexture2.vScale)),this._diffuseTexture3&&(this._activeEffect.setTexture("diffuse3Sampler",this._diffuseTexture3),this._activeEffect.setFloat2("diffuse3Infos",this._diffuseTexture3.uScale,this._diffuseTexture3.vScale))),u.MaterialFlags.BumpTextureEnabled&&n.getEngine().getCaps().standardDerivatives&&(this._bumpTexture1&&this._activeEffect.setTexture("bump1Sampler",this._bumpTexture1),this._bumpTexture2&&this._activeEffect.setTexture("bump2Sampler",this._bumpTexture2),this._bumpTexture3&&this._activeEffect.setTexture("bump3Sampler",this._bumpTexture3))),(0,u.bindClipPlane)(o,this,n),this.pointsCloud&&this._activeEffect.setFloat("pointSize",this.pointSize),this._useLogarithmicDepth&&(0,u.BindLogDepth)(r,o,n),n.bindEyePosition(o)),this._activeEffect.setColor4("vDiffuseColor",this.diffuseColor,this.alpha*i.visibility),r.SPECULARTERM&&this._activeEffect.setColor4("vSpecularColor",this.specularColor,this.specularPower),n.lightsEnabled&&!this.disableLighting&&(0,u.BindLights)(n,i,this._activeEffect,r,this.maxSimultaneousLights),n.fogEnabled&&i.applyFog&&n.fogMode!==u.Scene.FOGMODE_NONE&&this._activeEffect.setMatrix("view",n.getViewMatrix()),(0,u.BindFogParameters)(n,i,this._activeEffect),this._afterBind(i,this._activeEffect,t))}},i.prototype.getAnimatables=function(){var e=[];return this.mixTexture&&this.mixTexture.animations&&this.mixTexture.animations.length>0&&e.push(this.mixTexture),e},i.prototype.getActiveTextures=function(){var i=e.prototype.getActiveTextures.call(this);return this._mixTexture&&i.push(this._mixTexture),this._diffuseTexture1&&i.push(this._diffuseTexture1),this._diffuseTexture2&&i.push(this._diffuseTexture2),this._diffuseTexture3&&i.push(this._diffuseTexture3),this._bumpTexture1&&i.push(this._bumpTexture1),this._bumpTexture2&&i.push(this._bumpTexture2),this._bumpTexture3&&i.push(this._bumpTexture3),i},i.prototype.hasTexture=function(i){return!!e.prototype.hasTexture.call(this,i)||this._mixTexture===i||this._diffuseTexture1===i||this._diffuseTexture2===i||this._diffuseTexture3===i||this._bumpTexture1===i||this._bumpTexture2===i||this._bumpTexture3===i},i.prototype.dispose=function(i){this.mixTexture&&this.mixTexture.dispose(),e.prototype.dispose.call(this,i)},i.prototype.clone=function(e){var t=this;return u.SerializationHelper.Clone((function(){return new i(e,t.getScene())}),this)},i.prototype.serialize=function(){var i=e.prototype.serialize.call(this);return i.customType="BABYLON.TerrainMaterial",i},i.prototype.getClassName=function(){return"TerrainMaterial"},i.Parse=function(e,t,n){return u.SerializationHelper.Parse((function(){return new i(e.name,t)}),e,t,n)},l([(0,u.serializeAsTexture)("mixTexture")],i.prototype,"_mixTexture",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"mixTexture",void 0),l([(0,u.serializeAsTexture)("diffuseTexture1")],i.prototype,"_diffuseTexture1",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture1",void 0),l([(0,u.serializeAsTexture)("diffuseTexture2")],i.prototype,"_diffuseTexture2",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture2",void 0),l([(0,u.serializeAsTexture)("diffuseTexture3")],i.prototype,"_diffuseTexture3",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTexture3",void 0),l([(0,u.serializeAsTexture)("bumpTexture1")],i.prototype,"_bumpTexture1",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"bumpTexture1",void 0),l([(0,u.serializeAsTexture)("bumpTexture2")],i.prototype,"_bumpTexture2",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"bumpTexture2",void 0),l([(0,u.serializeAsTexture)("bumpTexture3")],i.prototype,"_bumpTexture3",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"bumpTexture3",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"diffuseColor",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"specularColor",void 0),l([(0,u.serialize)()],i.prototype,"specularPower",void 0),l([(0,u.serialize)("disableLighting")],i.prototype,"_disableLighting",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"disableLighting",void 0),l([(0,u.serialize)("maxSimultaneousLights")],i.prototype,"_maxSimultaneousLights",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"maxSimultaneousLights",void 0),i}(u.PushMaterial);(0,u.RegisterClass)("BABYLON.TerrainMaterial",z);u.ShaderStore.ShadersStore.triplanarPixelShader="precision highp float;uniform vec4 vEyePosition;uniform vec4 vDiffuseColor;\n#ifdef SPECULARTERM\nuniform vec4 vSpecularColor;\n#endif\nvarying vec3 vPositionW;\n#if defined(VERTEXCOLOR) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nvarying vec4 vColor;\n#endif\n#include<helperFunctions>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#ifdef DIFFUSEX\nvarying vec2 vTextureUVX;uniform sampler2D diffuseSamplerX;\n#ifdef BUMPX\nuniform sampler2D normalSamplerX;\n#endif\n#endif\n#ifdef DIFFUSEY\nvarying vec2 vTextureUVY;uniform sampler2D diffuseSamplerY;\n#ifdef BUMPY\nuniform sampler2D normalSamplerY;\n#endif\n#endif\n#ifdef DIFFUSEZ\nvarying vec2 vTextureUVZ;uniform sampler2D diffuseSamplerZ;\n#ifdef BUMPZ\nuniform sampler2D normalSamplerZ;\n#endif\n#endif\n#ifdef NORMAL\nvarying mat3 tangentSpace;\n#endif\n#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\n#include<logDepthDeclaration>\n#include<lightsFragmentFunctions>\n#include<shadowsFragmentFunctions>\n#include<clipPlaneFragmentDeclaration>\n#include<fogFragmentDeclaration>\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);vec4 baseColor=vec4(0.,0.,0.,1.);vec3 diffuseColor=vDiffuseColor.rgb;float alpha=vDiffuseColor.a;\n#ifdef NORMAL\nvec3 normalW=tangentSpace[2];\n#else\nvec3 normalW=vec3(1.0,1.0,1.0);\n#endif\nvec4 baseNormal=vec4(0.0,0.0,0.0,1.0);normalW*=normalW;\n#ifdef DIFFUSEX\nbaseColor+=texture2D(diffuseSamplerX,vTextureUVX)*normalW.x;\n#ifdef BUMPX\nbaseNormal+=texture2D(normalSamplerX,vTextureUVX)*normalW.x;\n#endif\n#endif\n#ifdef DIFFUSEY\nbaseColor+=texture2D(diffuseSamplerY,vTextureUVY)*normalW.y;\n#ifdef BUMPY\nbaseNormal+=texture2D(normalSamplerY,vTextureUVY)*normalW.y;\n#endif\n#endif\n#ifdef DIFFUSEZ\nbaseColor+=texture2D(diffuseSamplerZ,vTextureUVZ)*normalW.z;\n#ifdef BUMPZ\nbaseNormal+=texture2D(normalSamplerZ,vTextureUVZ)*normalW.z;\n#endif\n#endif\n#ifdef NORMAL\nnormalW=normalize((2.0*baseNormal.xyz-1.0)*tangentSpace);\n#endif\n#ifdef ALPHATEST\nif (baseColor.a<0.4)\ndiscard;\n#endif\n#include<depthPrePass>\n#if defined(VERTEXCOLOR) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nbaseColor.rgb*=vColor.rgb;\n#endif\nvec3 diffuseBase=vec3(0.,0.,0.);lightingInfo info;float shadow=1.;float aggShadow=0.;float numLights=0.;\n#ifdef SPECULARTERM\nfloat glossiness=vSpecularColor.a;vec3 specularBase=vec3(0.,0.,0.);vec3 specularColor=vSpecularColor.rgb;\n#else\nfloat glossiness=0.;\n#endif\n#include<lightFragment>[0..maxSimultaneousLights]\n#if defined(VERTEXALPHA) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nalpha*=vColor.a;\n#endif\n#ifdef SPECULARTERM\nvec3 finalSpecular=specularBase*specularColor;\n#else\nvec3 finalSpecular=vec3(0.0);\n#endif\nvec3 finalDiffuse=clamp(diffuseBase*diffuseColor,0.0,1.0)*baseColor.rgb;vec4 color=vec4(finalDiffuse+finalSpecular,alpha);\n#include<logDepthFragment>\n#include<fogFragment>\ngl_FragColor=color;\n#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}\n";u.ShaderStore.ShadersStore.triplanarVertexShader="precision highp float;attribute vec3 position;\n#ifdef NORMAL\nattribute vec3 normal;\n#endif\n#ifdef VERTEXCOLOR\nattribute vec4 color;\n#endif\n#include<helperFunctions>\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\n#include<instancesDeclaration>\nuniform mat4 view;uniform mat4 viewProjection;\n#ifdef DIFFUSEX\nvarying vec2 vTextureUVX;\n#endif\n#ifdef DIFFUSEY\nvarying vec2 vTextureUVY;\n#endif\n#ifdef DIFFUSEZ\nvarying vec2 vTextureUVZ;\n#endif\nuniform float tileSize;\n#ifdef POINTSIZE\nuniform float pointSize;\n#endif\nvarying vec3 vPositionW;\n#ifdef NORMAL\nvarying mat3 tangentSpace;\n#endif\n#if defined(VERTEXCOLOR) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nvarying vec4 vColor;\n#endif\n#include<clipPlaneVertexDeclaration>\n#include<logDepthDeclaration>\n#include<fogVertexDeclaration>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void)\n{\n#define CUSTOM_VERTEX_MAIN_BEGIN\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\nvec4 worldPos=finalWorld*vec4(position,1.0);gl_Position=viewProjection*worldPos;vPositionW=vec3(worldPos);\n#ifdef DIFFUSEX\nvTextureUVX=worldPos.zy/tileSize;\n#endif\n#ifdef DIFFUSEY\nvTextureUVY=worldPos.xz/tileSize;\n#endif\n#ifdef DIFFUSEZ\nvTextureUVZ=worldPos.xy/tileSize;\n#endif\n#ifdef NORMAL\nvec3 xtan=vec3(0,0,1);vec3 xbin=vec3(0,1,0);vec3 ytan=vec3(1,0,0);vec3 ybin=vec3(0,0,1);vec3 ztan=vec3(1,0,0);vec3 zbin=vec3(0,1,0);vec3 normalizedNormal=normalize(normal);normalizedNormal*=normalizedNormal;vec3 worldBinormal=normalize(xbin*normalizedNormal.x+ybin*normalizedNormal.y+zbin*normalizedNormal.z);vec3 worldTangent=normalize(xtan*normalizedNormal.x+ytan*normalizedNormal.y+ztan*normalizedNormal.z);mat3 normalWorld=mat3(finalWorld);\n#ifdef NONUNIFORMSCALING\nnormalWorld=transposeMat3(inverseMat3(normalWorld));\n#endif\nworldTangent=normalize((normalWorld*worldTangent).xyz);worldBinormal=normalize((normalWorld*worldBinormal).xyz);vec3 worldNormal=normalize((normalWorld*normalize(normal)).xyz);tangentSpace[0]=worldTangent;tangentSpace[1]=worldBinormal;tangentSpace[2]=worldNormal;\n#endif\n#include<clipPlaneVertex>\n#include<logDepthVertex>\n#include<fogVertex>\n#include<shadowsVertex>[0..maxSimultaneousLights]\n#include<vertexColorMixing>\n#if defined(POINTSIZE) && !defined(WEBGPU)\ngl_PointSize=pointSize;\n#endif\n#define CUSTOM_VERTEX_MAIN_END\n}\n";var G=function(e){function i(){var i=e.call(this)||this;return i.DIFFUSEX=!1,i.DIFFUSEY=!1,i.DIFFUSEZ=!1,i.BUMPX=!1,i.BUMPY=!1,i.BUMPZ=!1,i.CLIPPLANE=!1,i.CLIPPLANE2=!1,i.CLIPPLANE3=!1,i.CLIPPLANE4=!1,i.CLIPPLANE5=!1,i.CLIPPLANE6=!1,i.ALPHATEST=!1,i.DEPTHPREPASS=!1,i.POINTSIZE=!1,i.FOG=!1,i.SPECULARTERM=!1,i.NORMAL=!1,i.VERTEXCOLOR=!1,i.VERTEXALPHA=!1,i.NUM_BONE_INFLUENCERS=0,i.BonesPerMesh=0,i.INSTANCES=!1,i.INSTANCESCOLOR=!1,i.IMAGEPROCESSINGPOSTPROCESS=!1,i.SKIPFINALCOLORCLAMP=!1,i.NONUNIFORMSCALING=!1,i.LOGARITHMICDEPTH=!1,i.rebuild(),i}return f(i,e),i}(u.MaterialDefines),H=function(e){function i(i,t){var n=e.call(this,i,t)||this;return n.tileSize=1,n.diffuseColor=new u.Color3(1,1,1),n.specularColor=new u.Color3(.2,.2,.2),n.specularPower=64,n._disableLighting=!1,n._maxSimultaneousLights=4,n}return f(i,e),i.prototype.needAlphaBlending=function(){return this.alpha<1},i.prototype.needAlphaTesting=function(){return!1},i.prototype.getAlphaTestTexture=function(){return null},i.prototype.isReadyForSubMesh=function(e,i,t){var n=i._drawWrapper;if(this.isFrozen&&n.effect&&n._wasPreviouslyReady&&n._wasPreviouslyUsingInstances===t)return!0;i.materialDefines||(i.materialDefines=new G);var r=i.materialDefines,o=this.getScene();if(this._isReadyForSubMesh(i))return!0;var s=o.getEngine();if(r._areTexturesDirty&&o.texturesEnabled){if(u.MaterialFlags.DiffuseTextureEnabled)for(var a=[this.diffuseTextureX,this.diffuseTextureY,this.diffuseTextureZ],f=["DIFFUSEX","DIFFUSEY","DIFFUSEZ"],l=0;l<a.length;l++)if(a[l]){if(!a[l].isReady())return!1;r[f[l]]=!0}if(u.MaterialFlags.BumpTextureEnabled)for(a=[this.normalTextureX,this.normalTextureY,this.normalTextureZ],f=["BUMPX","BUMPY","BUMPZ"],l=0;l<a.length;l++)if(a[l]){if(!a[l].isReady())return!1;r[f[l]]=!0}}if((0,u.PrepareDefinesForMisc)(e,o,this._useLogarithmicDepth,this.pointsCloud,this.fogEnabled,this._shouldTurnAlphaTestOn(e),r),r._needNormals=(0,u.PrepareDefinesForLights)(o,e,r,!1,this._maxSimultaneousLights,this._disableLighting),(0,u.PrepareDefinesForFrameBoundValues)(o,s,this,r,!!t),(0,u.PrepareDefinesForAttributes)(e,r,!0,!0),r.isDirty){r.markAsProcessed(),o.resetCachedMaterial();var d=new u.EffectFallbacks;r.FOG&&d.addFallback(1,"FOG"),(0,u.HandleFallbacksForShadows)(r,d,this.maxSimultaneousLights),r.NUM_BONE_INFLUENCERS>0&&d.addCPUSkinningFallback(0,e),r.IMAGEPROCESSINGPOSTPROCESS=o.imageProcessingConfiguration.applyByPostProcess;var c=[u.VertexBuffer.PositionKind];r.NORMAL&&c.push(u.VertexBuffer.NormalKind),r.VERTEXCOLOR&&c.push(u.VertexBuffer.ColorKind),(0,u.PrepareAttributesForBones)(c,e,r,d),(0,u.PrepareAttributesForInstances)(c,r);var h=r.toString(),p=["world","view","viewProjection","vEyePosition","vLightsType","vDiffuseColor","vSpecularColor","vFogInfos","vFogColor","pointSize","mBones","tileSize"],m=["diffuseSamplerX","diffuseSamplerY","diffuseSamplerZ","normalSamplerX","normalSamplerY","normalSamplerZ","logarithmicDepthConstant"],v=[];(0,u.addClipPlaneUniforms)(p),(0,u.PrepareUniformsAndSamplersList)({uniformsNames:p,uniformBuffersNames:v,samplers:m,defines:r,maxSimultaneousLights:this.maxSimultaneousLights}),i.setEffect(o.getEngine().createEffect("triplanar",{attributes:c,uniformsNames:p,uniformBuffersNames:v,samplers:m,defines:h,fallbacks:d,onCompiled:this.onCompiled,onError:this.onError,indexParameters:{maxSimultaneousLights:this.maxSimultaneousLights}},s),r,this._materialContext)}return!(!i.effect||!i.effect.isReady()||(r._renderId=o.getRenderId(),n._wasPreviouslyReady=!0,n._wasPreviouslyUsingInstances=!!t,0))},i.prototype.bindForSubMesh=function(e,i,t){var n=this.getScene(),r=t.materialDefines;if(r){var o=t.effect;o&&(this._activeEffect=o,this.bindOnlyWorldMatrix(e),this._activeEffect.setMatrix("viewProjection",n.getTransformMatrix()),(0,u.BindBonesParameters)(i,this._activeEffect),this._activeEffect.setFloat("tileSize",this.tileSize),this._mustRebind(n,o,t)&&(this.diffuseTextureX&&this._activeEffect.setTexture("diffuseSamplerX",this.diffuseTextureX),this.diffuseTextureY&&this._activeEffect.setTexture("diffuseSamplerY",this.diffuseTextureY),this.diffuseTextureZ&&this._activeEffect.setTexture("diffuseSamplerZ",this.diffuseTextureZ),this.normalTextureX&&this._activeEffect.setTexture("normalSamplerX",this.normalTextureX),this.normalTextureY&&this._activeEffect.setTexture("normalSamplerY",this.normalTextureY),this.normalTextureZ&&this._activeEffect.setTexture("normalSamplerZ",this.normalTextureZ),(0,u.bindClipPlane)(o,this,n),this.pointsCloud&&this._activeEffect.setFloat("pointSize",this.pointSize),this._useLogarithmicDepth&&(0,u.BindLogDepth)(r,o,n),n.bindEyePosition(o)),this._activeEffect.setColor4("vDiffuseColor",this.diffuseColor,this.alpha*i.visibility),r.SPECULARTERM&&this._activeEffect.setColor4("vSpecularColor",this.specularColor,this.specularPower),n.lightsEnabled&&!this.disableLighting&&(0,u.BindLights)(n,i,this._activeEffect,r,this.maxSimultaneousLights),n.fogEnabled&&i.applyFog&&n.fogMode!==u.Scene.FOGMODE_NONE&&this._activeEffect.setMatrix("view",n.getViewMatrix()),(0,u.BindFogParameters)(n,i,this._activeEffect),this._afterBind(i,this._activeEffect,t))}},i.prototype.getAnimatables=function(){var e=[];return this.mixTexture&&this.mixTexture.animations&&this.mixTexture.animations.length>0&&e.push(this.mixTexture),e},i.prototype.getActiveTextures=function(){var i=e.prototype.getActiveTextures.call(this);return this._diffuseTextureX&&i.push(this._diffuseTextureX),this._diffuseTextureY&&i.push(this._diffuseTextureY),this._diffuseTextureZ&&i.push(this._diffuseTextureZ),this._normalTextureX&&i.push(this._normalTextureX),this._normalTextureY&&i.push(this._normalTextureY),this._normalTextureZ&&i.push(this._normalTextureZ),i},i.prototype.hasTexture=function(i){return!!e.prototype.hasTexture.call(this,i)||this._diffuseTextureX===i||this._diffuseTextureY===i||this._diffuseTextureZ===i||this._normalTextureX===i||this._normalTextureY===i||this._normalTextureZ===i},i.prototype.dispose=function(i){this.mixTexture&&this.mixTexture.dispose(),e.prototype.dispose.call(this,i)},i.prototype.clone=function(e){var t=this;return u.SerializationHelper.Clone((function(){return new i(e,t.getScene())}),this)},i.prototype.serialize=function(){var i=e.prototype.serialize.call(this);return i.customType="BABYLON.TriPlanarMaterial",i},i.prototype.getClassName=function(){return"TriPlanarMaterial"},i.Parse=function(e,t,n){return u.SerializationHelper.Parse((function(){return new i(e.name,t)}),e,t,n)},l([(0,u.serializeAsTexture)()],i.prototype,"mixTexture",void 0),l([(0,u.serializeAsTexture)("diffuseTextureX")],i.prototype,"_diffuseTextureX",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTextureX",void 0),l([(0,u.serializeAsTexture)("diffuseTexturY")],i.prototype,"_diffuseTextureY",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTextureY",void 0),l([(0,u.serializeAsTexture)("diffuseTextureZ")],i.prototype,"_diffuseTextureZ",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"diffuseTextureZ",void 0),l([(0,u.serializeAsTexture)("normalTextureX")],i.prototype,"_normalTextureX",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"normalTextureX",void 0),l([(0,u.serializeAsTexture)("normalTextureY")],i.prototype,"_normalTextureY",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"normalTextureY",void 0),l([(0,u.serializeAsTexture)("normalTextureZ")],i.prototype,"_normalTextureZ",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"normalTextureZ",void 0),l([(0,u.serialize)()],i.prototype,"tileSize",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"diffuseColor",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"specularColor",void 0),l([(0,u.serialize)()],i.prototype,"specularPower",void 0),l([(0,u.serialize)("disableLighting")],i.prototype,"_disableLighting",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"disableLighting",void 0),l([(0,u.serialize)("maxSimultaneousLights")],i.prototype,"_maxSimultaneousLights",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"maxSimultaneousLights",void 0),i}(u.PushMaterial);(0,u.RegisterClass)("BABYLON.TriPlanarMaterial",H);u.ShaderStore.ShadersStore.waterPixelShader="#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\nprecision highp float;uniform vec4 vEyePosition;uniform vec4 vDiffuseColor;\n#ifdef SPECULARTERM\nuniform vec4 vSpecularColor;\n#endif\nvarying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#if defined(VERTEXCOLOR) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nvarying vec4 vColor;\n#endif\n#include<helperFunctions>\n#include<imageProcessingDeclaration>\n#include<imageProcessingFunctions>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#include<lightsFragmentFunctions>\n#include<shadowsFragmentFunctions>\n#ifdef BUMP\nvarying vec2 vNormalUV;\n#ifdef BUMPSUPERIMPOSE\nvarying vec2 vNormalUV2;\n#endif\nuniform sampler2D normalSampler;uniform vec2 vNormalInfos;\n#endif\nuniform sampler2D refractionSampler;uniform sampler2D reflectionSampler;const float LOG2=1.442695;uniform vec3 cameraPosition;uniform vec4 waterColor;uniform float colorBlendFactor;uniform vec4 waterColor2;uniform float colorBlendFactor2;uniform float bumpHeight;uniform float time;varying vec3 vRefractionMapTexCoord;varying vec3 vReflectionMapTexCoord;\n#include<clipPlaneFragmentDeclaration>\n#include<logDepthDeclaration>\n#include<fogFragmentDeclaration>\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);vec4 baseColor=vec4(1.,1.,1.,1.);vec3 diffuseColor=vDiffuseColor.rgb;float alpha=vDiffuseColor.a;\n#ifdef BUMP\n#ifdef BUMPSUPERIMPOSE\nbaseColor=0.6*texture2D(normalSampler,vNormalUV)+0.4*texture2D(normalSampler,vec2(vNormalUV2.x,vNormalUV2.y));\n#else\nbaseColor=texture2D(normalSampler,vNormalUV);\n#endif\nvec3 bumpColor=baseColor.rgb;\n#ifdef ALPHATEST\nif (baseColor.a<0.4)\ndiscard;\n#endif\nbaseColor.rgb*=vNormalInfos.y;\n#else\nvec3 bumpColor=vec3(1.0);\n#endif\n#if defined(VERTEXCOLOR) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nbaseColor.rgb*=vColor.rgb;\n#endif\n#ifdef NORMAL\nvec2 perturbation=bumpHeight*(baseColor.rg-0.5);\n#ifdef BUMPAFFECTSREFLECTION\nvec3 normalW=normalize(vNormalW+vec3(perturbation.x*8.0,0.0,perturbation.y*8.0));if (normalW.y<0.0) {normalW.y=-normalW.y;}\n#else\nvec3 normalW=normalize(vNormalW);\n#endif\n#else\nvec3 normalW=vec3(1.0,1.0,1.0);vec2 perturbation=bumpHeight*(vec2(1.0,1.0)-0.5);\n#endif\n#ifdef FRESNELSEPARATE\n#ifdef REFLECTION\nvec2 projectedRefractionTexCoords=clamp(vRefractionMapTexCoord.xy/vRefractionMapTexCoord.z+perturbation*0.5,0.0,1.0);vec4 refractiveColor=texture2D(refractionSampler,projectedRefractionTexCoords);\n#ifdef IS_REFRACTION_LINEAR\nrefractiveColor.rgb=toGammaSpace(refractiveColor.rgb);\n#endif\nvec2 projectedReflectionTexCoords=clamp(vec2(\nvReflectionMapTexCoord.x/vReflectionMapTexCoord.z+perturbation.x*0.3,\nvReflectionMapTexCoord.y/vReflectionMapTexCoord.z+perturbation.y\n),0.0,1.0);vec4 reflectiveColor=texture2D(reflectionSampler,projectedReflectionTexCoords);\n#ifdef IS_REFLECTION_LINEAR\nreflectiveColor.rgb=toGammaSpace(reflectiveColor.rgb);\n#endif\nvec3 upVector=vec3(0.0,1.0,0.0);float fresnelTerm=clamp(abs(pow(dot(viewDirectionW,upVector),3.0)),0.05,0.65);float IfresnelTerm=1.0-fresnelTerm;refractiveColor=colorBlendFactor*waterColor+(1.0-colorBlendFactor)*refractiveColor;reflectiveColor=IfresnelTerm*colorBlendFactor2*waterColor+(1.0-colorBlendFactor2*IfresnelTerm)*reflectiveColor;vec4 combinedColor=refractiveColor*fresnelTerm+reflectiveColor*IfresnelTerm;baseColor=combinedColor;\n#endif\nvec3 diffuseBase=vec3(0.,0.,0.);lightingInfo info;float shadow=1.;float aggShadow=0.;float numLights=0.;\n#ifdef SPECULARTERM\nfloat glossiness=vSpecularColor.a;vec3 specularBase=vec3(0.,0.,0.);vec3 specularColor=vSpecularColor.rgb;\n#else\nfloat glossiness=0.;\n#endif\n#include<lightFragment>[0..maxSimultaneousLights]\nvec3 finalDiffuse=clamp(baseColor.rgb,0.0,1.0);\n#if defined(VERTEXALPHA) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nalpha*=vColor.a;\n#endif\n#ifdef SPECULARTERM\nvec3 finalSpecular=specularBase*specularColor;\n#else\nvec3 finalSpecular=vec3(0.0);\n#endif\n#else \n#ifdef REFLECTION\nvec2 projectedRefractionTexCoords=clamp(vRefractionMapTexCoord.xy/vRefractionMapTexCoord.z+perturbation,0.0,1.0);vec4 refractiveColor=texture2D(refractionSampler,projectedRefractionTexCoords);\n#ifdef IS_REFRACTION_LINEAR\nrefractiveColor.rgb=toGammaSpace(refractiveColor.rgb);\n#endif\nvec2 projectedReflectionTexCoords=clamp(vReflectionMapTexCoord.xy/vReflectionMapTexCoord.z+perturbation,0.0,1.0);vec4 reflectiveColor=texture2D(reflectionSampler,projectedReflectionTexCoords);\n#ifdef IS_REFLECTION_LINEAR\nreflectiveColor.rgb=toGammaSpace(reflectiveColor.rgb);\n#endif\nvec3 upVector=vec3(0.0,1.0,0.0);float fresnelTerm=max(dot(viewDirectionW,upVector),0.0);vec4 combinedColor=refractiveColor*fresnelTerm+reflectiveColor*(1.0-fresnelTerm);baseColor=colorBlendFactor*waterColor+(1.0-colorBlendFactor)*combinedColor;\n#endif\nvec3 diffuseBase=vec3(0.,0.,0.);lightingInfo info;float shadow=1.;float aggShadow=0.;float numLights=0.;\n#ifdef SPECULARTERM\nfloat glossiness=vSpecularColor.a;vec3 specularBase=vec3(0.,0.,0.);vec3 specularColor=vSpecularColor.rgb;\n#else\nfloat glossiness=0.;\n#endif\n#include<lightFragment>[0..maxSimultaneousLights]\nvec3 finalDiffuse=clamp(baseColor.rgb,0.0,1.0);\n#if defined(VERTEXALPHA) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nalpha*=vColor.a;\n#endif\n#ifdef SPECULARTERM\nvec3 finalSpecular=specularBase*specularColor;\n#else\nvec3 finalSpecular=vec3(0.0);\n#endif\n#endif\nvec4 color=vec4(finalDiffuse+finalSpecular,alpha);\n#include<logDepthFragment>\n#include<fogFragment>\n#ifdef IMAGEPROCESSINGPOSTPROCESS\ncolor.rgb=toLinearSpace(color.rgb);\n#elif defined(IMAGEPROCESSING)\ncolor.rgb=toLinearSpace(color.rgb);color=applyImageProcessing(color);\n#endif\ngl_FragColor=color;\n#define CUSTOM_FRAGMENT_MAIN_END\n}\n";u.ShaderStore.ShadersStore.waterVertexShader="precision highp float;attribute vec3 position;\n#ifdef NORMAL\nattribute vec3 normal;\n#endif\n#ifdef UV1\nattribute vec2 uv;\n#endif\n#ifdef UV2\nattribute vec2 uv2;\n#endif\n#ifdef VERTEXCOLOR\nattribute vec4 color;\n#endif\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\n#include<instancesDeclaration>\nuniform mat4 view;uniform mat4 viewProjection;\n#ifdef BUMP\nvarying vec2 vNormalUV;\n#ifdef BUMPSUPERIMPOSE\nvarying vec2 vNormalUV2;\n#endif\nuniform mat4 normalMatrix;uniform vec2 vNormalInfos;\n#endif\n#ifdef POINTSIZE\nuniform float pointSize;\n#endif\nvarying vec3 vPositionW;\n#ifdef NORMAL\nvarying vec3 vNormalW;\n#endif\n#if defined(VERTEXCOLOR) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nvarying vec4 vColor;\n#endif\n#include<clipPlaneVertexDeclaration>\n#include<fogVertexDeclaration>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#include<logDepthDeclaration>\nuniform mat4 reflectionViewProjection;uniform vec2 windDirection;uniform float waveLength;uniform float time;uniform float windForce;uniform float waveHeight;uniform float waveSpeed;uniform float waveCount;varying vec3 vRefractionMapTexCoord;varying vec3 vReflectionMapTexCoord;\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\nvec4 worldPos=finalWorld*vec4(position,1.0);vPositionW=vec3(worldPos);\n#ifdef NORMAL\nvNormalW=normalize(vec3(finalWorld*vec4(normal,0.0)));\n#endif\n#ifndef UV1\nvec2 uv=vec2(0.,0.);\n#endif\n#ifndef UV2\nvec2 uv2=vec2(0.,0.);\n#endif\n#ifdef BUMP\nif (vNormalInfos.x==0.)\n{vNormalUV=vec2(normalMatrix*vec4((uv*1.0)/waveLength+time*windForce*windDirection,1.0,0.0));\n#ifdef BUMPSUPERIMPOSE\nvNormalUV2=vec2(normalMatrix*vec4((uv*0.721)/waveLength+time*1.2*windForce*windDirection,1.0,0.0));\n#endif\n}\nelse\n{vNormalUV=vec2(normalMatrix*vec4((uv2*1.0)/waveLength+time*windForce*windDirection ,1.0,0.0));\n#ifdef BUMPSUPERIMPOSE\nvNormalUV2=vec2(normalMatrix*vec4((uv2*0.721)/waveLength+time*1.2*windForce*windDirection ,1.0,0.0));\n#endif\n}\n#endif\n#include<clipPlaneVertex>\n#include<fogVertex>\n#include<shadowsVertex>[0..maxSimultaneousLights]\n#include<vertexColorMixing>\n#if defined(POINTSIZE) && !defined(WEBGPU)\ngl_PointSize=pointSize;\n#endif\nfloat finalWaveCount=1.0/(waveCount*0.5);\n#ifdef USE_WORLD_COORDINATES\nvec3 p=worldPos.xyz;\n#else\nvec3 p=position;\n#endif\nfloat newY=(sin(((p.x/finalWaveCount)+time*waveSpeed))*waveHeight*windDirection.x*5.0)\n+ (cos(((p.z/finalWaveCount)+ time*waveSpeed))*waveHeight*windDirection.y*5.0);p.y+=abs(newY);\n#ifdef USE_WORLD_COORDINATES\ngl_Position=viewProjection*vec4(p,1.0);\n#else\ngl_Position=viewProjection*finalWorld*vec4(p,1.0);\n#endif\n#ifdef REFLECTION\nvRefractionMapTexCoord.x=0.5*(gl_Position.w+gl_Position.x);vRefractionMapTexCoord.y=0.5*(gl_Position.w+gl_Position.y);vRefractionMapTexCoord.z=gl_Position.w;worldPos=reflectionViewProjection*finalWorld*vec4(position,1.0);vReflectionMapTexCoord.x=0.5*(worldPos.w+worldPos.x);vReflectionMapTexCoord.y=0.5*(worldPos.w+worldPos.y);vReflectionMapTexCoord.z=worldPos.w;\n#endif\n#include<logDepthVertex>\n#define CUSTOM_VERTEX_MAIN_END\n}\n";var W=function(e){function i(){var i=e.call(this)||this;return i.BUMP=!1,i.REFLECTION=!1,i.CLIPPLANE=!1,i.CLIPPLANE2=!1,i.CLIPPLANE3=!1,i.CLIPPLANE4=!1,i.CLIPPLANE5=!1,i.CLIPPLANE6=!1,i.ALPHATEST=!1,i.DEPTHPREPASS=!1,i.POINTSIZE=!1,i.FOG=!1,i.NORMAL=!1,i.UV1=!1,i.UV2=!1,i.VERTEXCOLOR=!1,i.VERTEXALPHA=!1,i.NUM_BONE_INFLUENCERS=0,i.BonesPerMesh=0,i.INSTANCES=!1,i.INSTANCESCOLOR=!1,i.SPECULARTERM=!1,i.LOGARITHMICDEPTH=!1,i.USE_REVERSE_DEPTHBUFFER=!1,i.FRESNELSEPARATE=!1,i.BUMPSUPERIMPOSE=!1,i.BUMPAFFECTSREFLECTION=!1,i.USE_WORLD_COORDINATES=!1,i.IMAGEPROCESSING=!1,i.VIGNETTE=!1,i.VIGNETTEBLENDMODEMULTIPLY=!1,i.VIGNETTEBLENDMODEOPAQUE=!1,i.TONEMAPPING=0,i.CONTRAST=!1,i.EXPOSURE=!1,i.COLORCURVES=!1,i.COLORGRADING=!1,i.COLORGRADING3D=!1,i.SAMPLER3DGREENDEPTH=!1,i.SAMPLER3DBGRMAP=!1,i.DITHER=!1,i.IMAGEPROCESSINGPOSTPROCESS=!1,i.SKIPFINALCOLORCLAMP=!1,i.rebuild(),i}return f(i,e),i}(u.MaterialDefines),X=function(e){function i(i,t,n){void 0===n&&(n=new u.Vector2(512,512));var r=e.call(this,i,t)||this;return r.renderTargetSize=n,r.diffuseColor=new u.Color3(1,1,1),r.specularColor=new u.Color3(0,0,0),r.specularPower=64,r._disableLighting=!1,r._maxSimultaneousLights=4,r.windForce=6,r.windDirection=new u.Vector2(0,1),r.waveHeight=.4,r.bumpHeight=.4,r._bumpSuperimpose=!1,r._fresnelSeparate=!1,r._bumpAffectsReflection=!1,r.waterColor=new u.Color3(.1,.1,.6),r.colorBlendFactor=.2,r.waterColor2=new u.Color3(.1,.1,.6),r.colorBlendFactor2=.2,r.waveLength=.1,r.waveSpeed=1,r.waveCount=20,r.disableClipPlane=!1,r._useWorldCoordinatesForWaveDeformation=!1,r._renderTargets=new u.SmartArray(16),r._mesh=null,r._reflectionTransform=u.Matrix.Zero(),r._lastTime=0,r._lastDeltaTime=0,r._createRenderTargets(r.getScene(),n),r.getRenderTargetTextures=function(){return r._renderTargets.reset(),r._renderTargets.push(r._reflectionRTT),r._renderTargets.push(r._refractionRTT),r._renderTargets},r._imageProcessingConfiguration=r.getScene().imageProcessingConfiguration,r._imageProcessingConfiguration&&(r._imageProcessingObserver=r._imageProcessingConfiguration.onUpdateParameters.add((function(){r._markAllSubMeshesAsImageProcessingDirty()}))),r}return f(i,e),Object.defineProperty(i.prototype,"hasRenderTargetTextures",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"refractionTexture",{get:function(){return this._refractionRTT},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"reflectionTexture",{get:function(){return this._reflectionRTT},enumerable:!1,configurable:!0}),i.prototype.addToRenderList=function(e){this._refractionRTT&&this._refractionRTT.renderList&&this._refractionRTT.renderList.push(e),this._reflectionRTT&&this._reflectionRTT.renderList&&this._reflectionRTT.renderList.push(e)},i.prototype.removeFromRenderList=function(e){var i;this._refractionRTT&&this._refractionRTT.renderList&&-1!==(i=this._refractionRTT.renderList.indexOf(e))&&this._refractionRTT.renderList.splice(i,1),this._reflectionRTT&&this._reflectionRTT.renderList&&-1!==(i=this._reflectionRTT.renderList.indexOf(e))&&this._reflectionRTT.renderList.splice(i,1)},i.prototype.enableRenderTargets=function(e){var i=e?1:0;this._refractionRTT&&(this._refractionRTT.refreshRate=i),this._reflectionRTT&&(this._reflectionRTT.refreshRate=i)},i.prototype.getRenderList=function(){return this._refractionRTT?this._refractionRTT.renderList:[]},Object.defineProperty(i.prototype,"renderTargetsEnabled",{get:function(){return!(this._refractionRTT&&0===this._refractionRTT.refreshRate)},enumerable:!1,configurable:!0}),i.prototype.needAlphaBlending=function(){return this.alpha<1},i.prototype.needAlphaTesting=function(){return!1},i.prototype.getAlphaTestTexture=function(){return null},i.prototype.isReadyForSubMesh=function(e,i,t){var n=i._drawWrapper;if(this.isFrozen&&n.effect&&n._wasPreviouslyReady&&n._wasPreviouslyUsingInstances===t)return!0;i.materialDefines||(i.materialDefines=new W);var r=i.materialDefines,o=this.getScene();if(this._isReadyForSubMesh(i))return!0;var s=o.getEngine();if(r._areTexturesDirty&&(r._needUVs=!1,o.texturesEnabled)){if(this.bumpTexture&&u.MaterialFlags.BumpTextureEnabled){if(!this.bumpTexture.isReady())return!1;r._needUVs=!0,r.BUMP=!0}u.MaterialFlags.ReflectionTextureEnabled&&(r.REFLECTION=!0)}if((0,u.PrepareDefinesForFrameBoundValues)(o,s,this,r,!!t),(0,u.PrepareDefinesForMisc)(e,o,this._useLogarithmicDepth,this.pointsCloud,this.fogEnabled,this._shouldTurnAlphaTestOn(e),r),r._areMiscDirty&&(r.FRESNELSEPARATE=this._fresnelSeparate,r.BUMPSUPERIMPOSE=this._bumpSuperimpose,r.BUMPAFFECTSREFLECTION=this._bumpAffectsReflection,r.USE_WORLD_COORDINATES=this._useWorldCoordinatesForWaveDeformation),r._needNormals=(0,u.PrepareDefinesForLights)(o,e,r,!0,this._maxSimultaneousLights,this._disableLighting),r._areImageProcessingDirty&&this._imageProcessingConfiguration){if(!this._imageProcessingConfiguration.isReady())return!1;this._imageProcessingConfiguration.prepareDefines(r),r.IS_REFLECTION_LINEAR=null!=this.reflectionTexture&&!this.reflectionTexture.gammaSpace,r.IS_REFRACTION_LINEAR=null!=this.refractionTexture&&!this.refractionTexture.gammaSpace}if((0,u.PrepareDefinesForAttributes)(e,r,!0,!0),this._mesh=e,this._waitingRenderList){for(var a=0;a<this._waitingRenderList.length;a++)this.addToRenderList(o.getNodeById(this._waitingRenderList[a]));this._waitingRenderList=null}if(r.isDirty){r.markAsProcessed(),o.resetCachedMaterial();var f=new u.EffectFallbacks;r.FOG&&f.addFallback(1,"FOG"),r.LOGARITHMICDEPTH&&f.addFallback(0,"LOGARITHMICDEPTH"),(0,u.HandleFallbacksForShadows)(r,f,this.maxSimultaneousLights),r.NUM_BONE_INFLUENCERS>0&&f.addCPUSkinningFallback(0,e);var l=[u.VertexBuffer.PositionKind];r.NORMAL&&l.push(u.VertexBuffer.NormalKind),r.UV1&&l.push(u.VertexBuffer.UVKind),r.UV2&&l.push(u.VertexBuffer.UV2Kind),r.VERTEXCOLOR&&l.push(u.VertexBuffer.ColorKind),(0,u.PrepareAttributesForBones)(l,e,r,f),(0,u.PrepareAttributesForInstances)(l,r);var d=r.toString(),c=["world","view","viewProjection","vEyePosition","vLightsType","vDiffuseColor","vSpecularColor","vFogInfos","vFogColor","pointSize","vNormalInfos","mBones","normalMatrix","logarithmicDepthConstant","reflectionViewProjection","windDirection","waveLength","time","windForce","cameraPosition","bumpHeight","waveHeight","waterColor","waterColor2","colorBlendFactor","colorBlendFactor2","waveSpeed","waveCount"],h=["normalSampler","refractionSampler","reflectionSampler"],p=[];u.ImageProcessingConfiguration&&(u.ImageProcessingConfiguration.PrepareUniforms(c,r),u.ImageProcessingConfiguration.PrepareSamplers(h,r)),(0,u.addClipPlaneUniforms)(c),(0,u.PrepareUniformsAndSamplersList)({uniformsNames:c,uniformBuffersNames:p,samplers:h,defines:r,maxSimultaneousLights:this.maxSimultaneousLights}),i.setEffect(o.getEngine().createEffect("water",{attributes:l,uniformsNames:c,uniformBuffersNames:p,samplers:h,defines:d,fallbacks:f,onCompiled:this.onCompiled,onError:this.onError,indexParameters:{maxSimultaneousLights:this._maxSimultaneousLights}},s),r,this._materialContext)}return!(!i.effect||!i.effect.isReady()||(r._renderId=o.getRenderId(),n._wasPreviouslyReady=!0,n._wasPreviouslyUsingInstances=!!t,0))},i.prototype.bindForSubMesh=function(e,i,t){var n=this.getScene(),r=t.materialDefines;if(r){var o=t.effect;if(o&&this._mesh){this._activeEffect=o,this.bindOnlyWorldMatrix(e),this._activeEffect.setMatrix("viewProjection",n.getTransformMatrix()),(0,u.BindBonesParameters)(i,this._activeEffect),this._mustRebind(n,o,t)&&(this.bumpTexture&&u.MaterialFlags.BumpTextureEnabled&&(this._activeEffect.setTexture("normalSampler",this.bumpTexture),this._activeEffect.setFloat2("vNormalInfos",this.bumpTexture.coordinatesIndex,this.bumpTexture.level),this._activeEffect.setMatrix("normalMatrix",this.bumpTexture.getTextureMatrix())),(0,u.bindClipPlane)(o,this,n),this.pointsCloud&&this._activeEffect.setFloat("pointSize",this.pointSize),this._useLogarithmicDepth&&(0,u.BindLogDepth)(r,o,n),n.bindEyePosition(o)),this._activeEffect.setColor4("vDiffuseColor",this.diffuseColor,this.alpha*i.visibility),r.SPECULARTERM&&this._activeEffect.setColor4("vSpecularColor",this.specularColor,this.specularPower),n.lightsEnabled&&!this.disableLighting&&(0,u.BindLights)(n,i,this._activeEffect,r,this.maxSimultaneousLights),n.fogEnabled&&i.applyFog&&n.fogMode!==u.Scene.FOGMODE_NONE&&this._activeEffect.setMatrix("view",n.getViewMatrix()),(0,u.BindFogParameters)(n,i,this._activeEffect),(0,u.BindLogDepth)(r,this._activeEffect,n),u.MaterialFlags.ReflectionTextureEnabled&&(this._activeEffect.setTexture("refractionSampler",this._refractionRTT),this._activeEffect.setTexture("reflectionSampler",this._reflectionRTT));var s=this._reflectionTransform.multiply(n.getProjectionMatrix()),a=n.getEngine().getDeltaTime();a!==this._lastDeltaTime&&(this._lastDeltaTime=a,this._lastTime+=this._lastDeltaTime),this._activeEffect.setMatrix("reflectionViewProjection",s),this._activeEffect.setVector2("windDirection",this.windDirection),this._activeEffect.setFloat("waveLength",this.waveLength),this._activeEffect.setFloat("time",this._lastTime/1e5),this._activeEffect.setFloat("windForce",this.windForce),this._activeEffect.setFloat("waveHeight",this.waveHeight),this._activeEffect.setFloat("bumpHeight",this.bumpHeight),this._activeEffect.setColor4("waterColor",this.waterColor,1),this._activeEffect.setFloat("colorBlendFactor",this.colorBlendFactor),this._activeEffect.setColor4("waterColor2",this.waterColor2,1),this._activeEffect.setFloat("colorBlendFactor2",this.colorBlendFactor2),this._activeEffect.setFloat("waveSpeed",this.waveSpeed),this._activeEffect.setFloat("waveCount",this.waveCount),this._imageProcessingConfiguration&&!this._imageProcessingConfiguration.applyByPostProcess&&this._imageProcessingConfiguration.bind(this._activeEffect),this._afterBind(i,this._activeEffect,t)}}},i.prototype._createRenderTargets=function(e,i){var t=this;this._refractionRTT=new u.RenderTargetTexture(name+"_refraction",{width:i.x,height:i.y},e,!1,!0),this._refractionRTT.wrapU=u.Constants.TEXTURE_MIRROR_ADDRESSMODE,this._refractionRTT.wrapV=u.Constants.TEXTURE_MIRROR_ADDRESSMODE,this._refractionRTT.ignoreCameraViewport=!0;var n,r=!1;this._refractionRTT.onBeforeRenderObservable.add((function(){r=e.getBoundingBoxRenderer().enabled,e.getBoundingBoxRenderer().enabled=!1})),this._refractionRTT.onAfterRenderObservable.add((function(){e.getBoundingBoxRenderer().enabled=r})),this._reflectionRTT=new u.RenderTargetTexture(name+"_reflection",{width:i.x,height:i.y},e,!1,!0),this._reflectionRTT.wrapU=u.Constants.TEXTURE_MIRROR_ADDRESSMODE,this._reflectionRTT.wrapV=u.Constants.TEXTURE_MIRROR_ADDRESSMODE,this._reflectionRTT.ignoreCameraViewport=!0;var o,s=null,a=u.Matrix.Zero();this._refractionRTT.onBeforeRender=function(){if(t._mesh&&(n=t._mesh.isVisible,t._mesh.isVisible=!1),!t.disableClipPlane){s=e.clipPlane;var i=t._mesh?t._mesh.absolutePosition.y:0;e.clipPlane=u.Plane.FromPositionAndNormal(new u.Vector3(0,i+.05,0),new u.Vector3(0,1,0))}},this._refractionRTT.onAfterRender=function(){t._mesh&&(t._mesh.isVisible=n),t.disableClipPlane||(e.clipPlane=s)},this._reflectionRTT.onBeforeRender=function(){if(t._mesh&&(n=t._mesh.isVisible,t._mesh.isVisible=!1),!t.disableClipPlane){s=e.clipPlane;var i=t._mesh?t._mesh.absolutePosition.y:0;e.clipPlane=u.Plane.FromPositionAndNormal(new u.Vector3(0,i-.05,0),new u.Vector3(0,-1,0)),u.Matrix.ReflectionToRef(e.clipPlane,a)}o=e.getViewMatrix(),a.multiplyToRef(o,t._reflectionTransform),e.setTransformMatrix(t._reflectionTransform,e.getProjectionMatrix()),e._mirroredCameraPosition=u.Vector3.TransformCoordinates(e.activeCamera.position,a)},this._reflectionRTT.onAfterRender=function(){t._mesh&&(t._mesh.isVisible=n),e.clipPlane=s,e.setTransformMatrix(o,e.getProjectionMatrix()),e._mirroredCameraPosition=null}},i.prototype.getAnimatables=function(){var e=[];return this.bumpTexture&&this.bumpTexture.animations&&this.bumpTexture.animations.length>0&&e.push(this.bumpTexture),this._reflectionRTT&&this._reflectionRTT.animations&&this._reflectionRTT.animations.length>0&&e.push(this._reflectionRTT),this._refractionRTT&&this._refractionRTT.animations&&this._refractionRTT.animations.length>0&&e.push(this._refractionRTT),e},i.prototype.getActiveTextures=function(){var i=e.prototype.getActiveTextures.call(this);return this._bumpTexture&&i.push(this._bumpTexture),i},i.prototype.hasTexture=function(i){return!!e.prototype.hasTexture.call(this,i)||this._bumpTexture===i},i.prototype.dispose=function(i){this.bumpTexture&&this.bumpTexture.dispose();var t=this.getScene().customRenderTargets.indexOf(this._refractionRTT);-1!=t&&this.getScene().customRenderTargets.splice(t,1),t=-1,-1!=(t=this.getScene().customRenderTargets.indexOf(this._reflectionRTT))&&this.getScene().customRenderTargets.splice(t,1),this._reflectionRTT&&this._reflectionRTT.dispose(),this._refractionRTT&&this._refractionRTT.dispose(),this._imageProcessingConfiguration&&this._imageProcessingObserver&&this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver),e.prototype.dispose.call(this,i)},i.prototype.clone=function(e){var t=this;return u.SerializationHelper.Clone((function(){return new i(e,t.getScene())}),this)},i.prototype.serialize=function(){var i=e.prototype.serialize.call(this);if(i.customType="BABYLON.WaterMaterial",i.renderList=[],this._refractionRTT&&this._refractionRTT.renderList)for(var t=0;t<this._refractionRTT.renderList.length;t++)i.renderList.push(this._refractionRTT.renderList[t].id);return i},i.prototype.getClassName=function(){return"WaterMaterial"},i.Parse=function(e,t,n){var r=u.SerializationHelper.Parse((function(){return new i(e.name,t)}),e,t,n);return r._waitingRenderList=e.renderList,r},i.CreateDefaultMesh=function(e,i){return(0,u.CreateGround)(e,{width:512,height:512,subdivisions:32,updatable:!1},i)},l([(0,u.serializeAsTexture)("bumpTexture")],i.prototype,"_bumpTexture",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],i.prototype,"bumpTexture",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"diffuseColor",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"specularColor",void 0),l([(0,u.serialize)()],i.prototype,"specularPower",void 0),l([(0,u.serialize)("disableLighting")],i.prototype,"_disableLighting",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"disableLighting",void 0),l([(0,u.serialize)("maxSimultaneousLights")],i.prototype,"_maxSimultaneousLights",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsLightsDirty")],i.prototype,"maxSimultaneousLights",void 0),l([(0,u.serialize)()],i.prototype,"windForce",void 0),l([(0,u.serializeAsVector2)()],i.prototype,"windDirection",void 0),l([(0,u.serialize)()],i.prototype,"waveHeight",void 0),l([(0,u.serialize)()],i.prototype,"bumpHeight",void 0),l([(0,u.serialize)("bumpSuperimpose")],i.prototype,"_bumpSuperimpose",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsMiscDirty")],i.prototype,"bumpSuperimpose",void 0),l([(0,u.serialize)("fresnelSeparate")],i.prototype,"_fresnelSeparate",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsMiscDirty")],i.prototype,"fresnelSeparate",void 0),l([(0,u.serialize)("bumpAffectsReflection")],i.prototype,"_bumpAffectsReflection",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsMiscDirty")],i.prototype,"bumpAffectsReflection",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"waterColor",void 0),l([(0,u.serialize)()],i.prototype,"colorBlendFactor",void 0),l([(0,u.serializeAsColor3)()],i.prototype,"waterColor2",void 0),l([(0,u.serialize)()],i.prototype,"colorBlendFactor2",void 0),l([(0,u.serialize)()],i.prototype,"waveLength",void 0),l([(0,u.serialize)()],i.prototype,"waveSpeed",void 0),l([(0,u.serialize)()],i.prototype,"waveCount",void 0),l([(0,u.serialize)()],i.prototype,"disableClipPlane",void 0),l([(0,u.serialize)("useWorldCoordinatesForWaveDeformation")],i.prototype,"_useWorldCoordinatesForWaveDeformation",void 0),l([(0,u.expandToProperty)("_markAllSubMeshesAsMiscDirty")],i.prototype,"useWorldCoordinatesForWaveDeformation",void 0),i}(u.PushMaterial);(0,u.RegisterClass)("BABYLON.WaterMaterial",X);var k=void 0!==n.g?n.g:"undefined"!=typeof window?window:void 0;if(void 0!==k)for(var j in k.BABYLON=k.BABYLON||{},o)k.BABYLON[j]=o[j];const Z=s;return r.default})()));
//# sourceMappingURL=babylonjs.materials.min.js.map